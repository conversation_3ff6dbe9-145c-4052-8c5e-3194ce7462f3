import logging
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional

# Configure logging format
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'


class JsonFormatter(logging.Formatter):
    """
    Formatter that outputs JSON strings after parsing the log record.
    """
    def __init__(self, **kwargs):
        self.json_default = kwargs.pop("json_default", str)
        self.json_encoder = kwargs.pop("json_encoder", None)
        self.json_indent = kwargs.pop("json_indent", None)
        self.json_separators = kwargs.pop("json_separators", None)
        self.prefix = kwargs.pop("prefix", "")
        super(JsonFormatter, self).__init__(**kwargs)
        self.default_keys = [
            'name', 'levelname', 'levelno', 'pathname', 'filename', 'module',
            'lineno', 'funcName', 'created', 'asctime', 'msecs', 'relativeCreated',
            'thread', 'threadName', 'process', 'message'
        ]

    def format(self, record):
        """Format the log record as JSON."""
        record.message = record.getMessage()
        record.asctime = self.formatTime(record, self.datefmt)
        
        log_data = {
            'timestamp': record.asctime,
            'level': record.levelname,
            'name': record.name,
            'message': record.message,
        }
        
        if hasattr(record, 'props'):
            log_data.update(record.props)
        
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        return self.prefix + json.dumps(
            log_data,
            default=self.json_default,
            cls=self.json_encoder,
            indent=self.json_indent,
            separators=self.json_separators,
        )


def get_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """
    Get a logger instance with the specified name and level.
    
    Args:
        name: The name of the logger
        level: The logging level (default: INFO)
        
    Returns:
        A configured logger instance
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Check if handlers are already configured to avoid duplicate logs
    if not logger.handlers:
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(JsonFormatter())
        logger.addHandler(console_handler)
        
        # File handler (optional, based on environment)
        log_file = os.environ.get('LOG_FILE')
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(JsonFormatter())
            logger.addHandler(file_handler)
    
    return logger


class LoggerAdapter(logging.LoggerAdapter):
    """
    Logger adapter that allows adding context to log messages.
    """
    def __init__(self, logger: logging.Logger, extra: Optional[Dict[str, Any]] = None):
        """
        Initialize the adapter with a logger and optional extra context.
        
        Args:
            logger: The logger instance to adapt
            extra: Optional extra context to include in all log messages
        """
        super(LoggerAdapter, self).__init__(logger, extra or {})
    
    def process(self, msg, kwargs):
        """Process the log message and keyword arguments."""
        props = kwargs.pop('props', {})
        props.update(self.extra)
        kwargs['extra'] = {'props': props}
        return msg, kwargs


def get_service_logger(service_name: str, component: Optional[str] = None) -> LoggerAdapter:
    """
    Get a logger adapter configured for a microservice.
    
    Args:
        service_name: The name of the service
        component: Optional component name within the service
        
    Returns:
        A configured logger adapter
    """
    logger_name = f"{service_name}.{component}" if component else service_name
    logger = get_logger(logger_name)
    
    extra = {
        'service': service_name,
    }
    
    if component:
        extra['component'] = component
    
    return LoggerAdapter(logger, extra)
