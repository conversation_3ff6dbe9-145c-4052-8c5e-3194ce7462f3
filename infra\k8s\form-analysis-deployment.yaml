apiVersion: apps/v1
kind: Deployment
metadata:
  name: form-analysis
  labels:
    app: form-analysis
spec:
  replicas: 2
  selector:
    matchLabels:
      app: form-analysis
  template:
    metadata:
      labels:
        app: form-analysis
    spec:
      containers:
      - name: form-analysis
        image: ${DOCKER_REGISTRY}/leadgen-form-analysis:latest
        ports:
        - containerPort: 8002
        env:
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka:9092"
        - name: FORM_DISCOVERED_TOPIC
          value: "form.discovered"
        - name: FORM_ANALYZED_TOPIC
          value: "form.analyzed"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: mongodb-secrets
              key: uri
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: service-secrets
              key: secret-key
        resources:
          limits:
            cpu: "1000m"
            memory: "2Gi"
          requests:
            cpu: "500m"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: screenshots
          mountPath: /app/screenshots
          readOnly: true
      volumes:
      - name: screenshots
        persistentVolumeClaim:
          claimName: screenshots-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: form-analysis
spec:
  selector:
    app: form-analysis
  ports:
  - port: 8002
    targetPort: 8002
  type: ClusterIP
