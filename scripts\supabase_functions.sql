-- These functions need to be executed in your Supabase SQL editor
-- They provide the ability to truncate and drop tables via RPC calls

-- Function to truncate a table
CREATE OR REPLACE FUNCTION truncate_table(table_name text)
R<PERSON><PERSON><PERSON> void AS $$
BEGIN
  EXECUTE 'TRUNCATE TABLE "' || table_name || '" CASCADE';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to drop a table
CREATE OR REPLACE FUNCTION drop_table(table_name text)
RETURNS void AS $$
BEGIN
  EXECUTE 'DROP TABLE IF EXISTS "' || table_name || '" CASCADE';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Note: These functions use SECURITY DEFINER which means they run with the
-- permissions of the user who created them (typically the database owner).
-- This is necessary because the Supabase client may not have direct DROP or TRUNCATE permissions.
