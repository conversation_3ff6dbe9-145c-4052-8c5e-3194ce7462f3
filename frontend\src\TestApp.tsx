import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';

// Simple Home component
const Home = () => (
  <div>
    <h1>Home Page</h1>
    <p>Welcome to the test app!</p>
  </div>
);

// Simple About component
const About = () => (
  <div>
    <h1>About Page</h1>
    <p>This is a test app to verify React Router is working.</p>
  </div>
);

// Main TestApp component
function TestApp() {
  return (
    <Router>
      <div style={{ padding: '20px' }}>
        <nav style={{ marginBottom: '20px' }}>
          <ul style={{ display: 'flex', listStyle: 'none', gap: '20px' }}>
            <li>
              <Link to="/">Home</Link>
            </li>
            <li>
              <Link to="/about">About</Link>
            </li>
          </ul>
        </nav>

        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
        </Routes>
      </div>
    </Router>
  );
}

export default TestApp;
