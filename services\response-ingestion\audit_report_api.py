"""
Audit Report API

This module provides a FastAPI interface for generating audit reports.
"""
import os
import sys
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel, Field

# Add parent directory to path to allow importing from sibling modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import audit report generator
from audit_report_generator import AuditReportGenerator

# Initialize FastAPI app
app = FastAPI(
    title="Audit Report API",
    description="API for generating lead response audit reports",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize audit report generator
generator = None

# Models
class AuditReportRequest(BaseModel):
    lead_id: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    output_format: str = "csv"

class AuditReportResponse(BaseModel):
    report_file: str
    row_count: int
    start_date: str
    end_date: str
    summary: Dict[str, Any]

class AuditSummary(BaseModel):
    total_leads: int
    responded_leads: int
    response_rate: float
    avg_score: float
    avg_grade: str
    channels: Dict[str, Dict[str, Any]]
    grades: Dict[str, Dict[str, Any]]

# Initialize components on startup
@app.on_event("startup")
async def startup_event():
    global generator
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("[WARN] python-dotenv not installed; skipping .env loading.")
    
    # Initialize audit report generator
    generator = AuditReportGenerator()

# API endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

@app.post("/generate", response_model=AuditReportResponse)
async def generate_report(request: AuditReportRequest):
    """Generate an audit report."""
    global generator
    
    if not generator:
        raise HTTPException(status_code=500, detail="Audit report generator not initialized")
    
    # Parse dates
    start_date = None
    end_date = None
    
    if request.start_date:
        try:
            start_date = datetime.fromisoformat(request.start_date)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid start_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS).")
    
    if request.end_date:
        try:
            end_date = datetime.fromisoformat(request.end_date)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid end_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS).")
    
    # Generate report
    report = generator.generate_audit_report(
        lead_id=request.lead_id,
        start_date=start_date,
        end_date=end_date
    )
    
    return report

@app.get("/download/{report_file}")
async def download_report(report_file: str):
    """Download a report file."""
    global generator
    
    if not generator:
        raise HTTPException(status_code=500, detail="Audit report generator not initialized")
    
    # Construct file path
    file_path = os.path.join(generator.output_dir, report_file)
    
    # Check if file exists
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Report file not found")
    
    return FileResponse(file_path, media_type="text/csv", filename=report_file)

@app.get("/summary", response_model=AuditSummary)
async def get_summary(
    days: int = Query(30, description="Number of days to include in the summary")
):
    """Get a summary of audit reports."""
    global generator
    
    if not generator:
        raise HTTPException(status_code=500, detail="Audit report generator not initialized")
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    # Generate report
    report = generator.generate_audit_report(
        start_date=start_date,
        end_date=end_date
    )
    
    return report["summary"]

# Run the app
if __name__ == "__main__":
    import uvicorn
    
    # Get port from environment or use default
    port = int(os.environ.get("AUDIT_REPORT_PORT", 8006))
    
    print(f"[INFO] Starting Audit Report API on port {port}")
    uvicorn.run(app, host="0.0.0.0", port=port)
