import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Typography, Grid, Box, CircularProgress, Divider, <PERSON>, <PERSON>ton, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  AccessTime as AccessTimeIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  ArrowForward as ArrowForwardIcon,
  Download as DownloadIcon
} from '@mui/icons-material';

// Mock data for the audit report - single form submission
const mockAuditData = {
  summary: {
    company_name: "Acme Roofing Solutions",
    website: "acmeroofing.com",
    form_url: "https://acmeroofing.com/contact",
    submission_time: "2023-05-03T14:32:15Z",
    audit_completed: "2023-05-05T14:32:15Z",
    responded: true,
    response_time_seconds: 2180,
    response_time_human: "0:36:20",
    response_channel: "sms",
    time_score: 60,
    channel_bonus: 15,
    total_score: 75,
    grade: "C",
    industry_avg_score: 45,
    industry_avg_grade: "D",
    industry_avg_response_time: "1:42:15",
    industry_best_practice: "Under 5 minutes via phone"
  },
  response_details: {
    submission: {
      timestamp: "2023-05-03T14:32:15Z",
      form_url: "https://acmeroofing.com/contact",
      form_type: "Contact Form",
      fields_submitted: {
        name: "John Smith",
        email: "<EMAIL>",
        phone: "(*************",
        message: "I need a quote for a new roof installation."
      }
    },
    responses: [
      {
        channel: "sms",
        timestamp: "2023-05-03T15:08:35Z",
        delay_seconds: 2180,
        delay_human: "0:36:20",
        content: "Hi John, thanks for your inquiry about a new roof. This is Mike from Acme Roofing. When would be a good time to discuss your project?",
        sender_info: {
          name: "Mike Johnson",
          role: "Sales Representative",
          phone: "(*************"
        }
      }
    ]
  }
};

// Helper function to get color based on grade
const getGradeColor = (grade) => {
  switch (grade) {
    case 'A+': return '#4caf50'; // Green
    case 'A': return '#8bc34a'; // Light Green
    case 'B': return '#ffeb3b'; // Yellow
    case 'C': return '#ff9800'; // Orange
    case 'D': return '#f44336'; // Red
    case 'F': return '#9e9e9e'; // Grey
    default: return '#9e9e9e'; // Grey
  }
};

// Helper function to get icon for channel
const getChannelIcon = (channel) => {
  switch (channel) {
    case 'phone': return <PhoneIcon />;
    case 'sms': return <SmsIcon />;
    case 'email': return <EmailIcon />;
    case 'NO_RESPONSE': return <CancelIcon />;
    default: return null;
  }
};

const AuditReportImproved = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [auditData, setAuditData] = useState(mockAuditData);

  // Fetch audit data from API
  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we're using mock data
    setLoading(true);
    setTimeout(() => {
      setAuditData(mockAuditData);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  const { summary } = auditData;
  const gradeColor = getGradeColor(summary.grade);

  // Calculate conversion rate based on response time
  const getConversionRate = (seconds) => {
    if (seconds < 300) return "85%";
    if (seconds < 600) return "70%";
    if (seconds < 1800) return "52%";
    if (seconds < 3600) return "40%";
    if (seconds < 10800) return "28%";
    if (seconds < 86400) return "15%";
    return "5%";
  };

  const conversionRate = getConversionRate(summary.response_time_seconds);
  const potentialConversionRate = "85%";

  return (
    <Box sx={{ p: 3, maxWidth: 1000, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
          LEAD RESPONSE AUDIT
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          {summary.company_name}
        </Typography>
        <Divider sx={{ my: 2 }} />
      </Box>

      {/* Grade Card */}
      <Card sx={{ mb: 4, boxShadow: 3, borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{ bgcolor: gradeColor, height: 8 }} />
        <CardContent sx={{ p: 3 }}>
          <Grid container spacing={3} alignItems="center">
            {/* Grade */}
            <Grid item xs={12} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="overline" sx={{ fontWeight: 'medium' }}>Your Grade</Typography>
                <Typography variant="h1" sx={{ fontWeight: 'bold', color: gradeColor, lineHeight: 1.2 }}>
                  {summary.grade}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Score: {summary.total_score}/125
                </Typography>
              </Box>
            </Grid>

            {/* Response Details */}
            <Grid item xs={12} md={5}>
              <Box>
                <Typography variant="overline" sx={{ fontWeight: 'medium' }}>Response Details</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  {getChannelIcon(summary.response_channel)}
                  <Typography variant="h6" sx={{ ml: 1, textTransform: 'capitalize' }}>
                    {summary.response_channel}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <AccessTimeIcon fontSize="small" sx={{ color: summary.time_score > 80 ? 'success.main' : 'warning.main' }} />
                  <Typography variant="h6" sx={{ ml: 1 }}>
                    {summary.response_time_human}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            {/* Impact */}
            <Grid item xs={12} md={4}>
              <Box>
                <Typography variant="overline" sx={{ fontWeight: 'medium' }}>Conversion Impact</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Typography variant="h6" color="error.main">
                    {conversionRate}
                  </Typography>
                  <ArrowForwardIcon sx={{ mx: 1 }} />
                  <Typography variant="h6" color="success.main">
                    {potentialConversionRate}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Current vs. Potential Conversion Rate
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Key Findings */}
      <Card sx={{ mb: 4, boxShadow: 2 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
            KEY FINDINGS
          </Typography>
          
          <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2, mb: 3 }}>
            <Typography variant="body1" paragraph sx={{ fontWeight: 'medium' }}>
              {summary.grade === 'A+' && "Your team is responding at an elite level. Stay sharp and maintain this advantage."}
              {summary.grade === 'A' && "Your response system is strong, but vulnerable. One slip and competitors will eat your lunch."}
              {summary.grade === 'B' && "Your above-average response performance still leaves money on the table."}
              {summary.grade === 'C' && "Your average response times are costing you deals. You consistently lose to the first responder."}
              {summary.grade === 'D' && "Your slow response speed is costing you 6 out of every 10 deals."}
              {summary.grade === 'F' && "This is the equivalent of paying for advertising and then ignoring the leads."}
            </Typography>
          </Box>

          {/* Response Time Impact */}
          <Paper elevation={0} sx={{ p: 2, bgcolor: '#e3f2fd', borderRadius: 2, mb: 3 }}>
            <Box display="flex" alignItems="flex-start">
              <AccessTimeIcon color="primary" sx={{ mr: 2, mt: 0.5 }} />
              <Box>
                <Typography variant="subtitle1" color="primary.main" sx={{ fontWeight: 'medium' }}>Response Time Impact</Typography>
                <Typography variant="body2">
                  <strong>5-minute response:</strong> 85% conversion rate<br />
                  <strong>Your response ({summary.response_time_human}):</strong> {conversionRate} conversion rate
                </Typography>
              </Box>
            </Box>
          </Paper>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card sx={{ mb: 4, bgcolor: '#e8f5e9', boxShadow: 2 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
            RECOMMENDATIONS
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ p: 2, bgcolor: 'white', borderRadius: 2 }}>
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#2e7d32', fontWeight: 'medium' }}>
                  1. Improve Response Time
                </Typography>
                <Typography variant="body2">
                  Reduce response time from {summary.response_time_human} to under 5 minutes to increase conversion rate from {conversionRate} to 85%.
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ p: 2, bgcolor: 'white', borderRadius: 2 }}>
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#2e7d32', fontWeight: 'medium' }}>
                  2. Optimize Channel Selection
                </Typography>
                <Typography variant="body2">
                  {summary.response_channel === 'phone' ? 
                    "Continue using phone calls while improving response time." : 
                    `Switch from ${summary.response_channel} to phone calls for 3x better conversion rates.`}
                </Typography>
              </Box>
            </Grid>
          </Grid>

          <Box display="flex" justifyContent="center" mt={3}>
            <Button
              variant="contained"
              color="success"
              size="large"
              sx={{ px: 4, py: 1.5, fontWeight: 'bold' }}
            >
              Schedule a Consultation
            </Button>
          </Box>
        </CardContent>
      </Card>

      <Box display="flex" justifyContent="center">
        <Button variant="outlined" startIcon={<DownloadIcon />} sx={{ mb: 2 }}>
          Download Full Report
        </Button>
      </Box>
    </Box>
  );
};

export default AuditReportImproved;
