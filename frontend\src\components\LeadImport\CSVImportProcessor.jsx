import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Button,
  Typography,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
  Snackbar,
  TextField,
  Checkbox,
  FormControlLabel,
  Tooltip,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Check as CheckIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import <PERSON> from 'papaparse';
import LeadEnrichmentPanel from './LeadEnrichmentPanel';

// Required fields for our system
const REQUIRED_FIELDS = ['email'];

// Standard fields that we support
const STANDARD_FIELDS = [
  // Name fields - can be combined or separate
  { key: 'name', label: 'Full Name', description: 'Full name of the lead' },
  { key: 'first_name', label: 'First Name', description: 'First name of the lead' },
  { key: 'last_name', label: 'Last Name', description: 'Last name of the lead' },

  // Contact information
  { key: 'email', label: 'Email', description: 'Email address' },
  { key: 'phone', label: 'Phone', description: 'Primary phone number' },
  { key: 'alt_phone', label: 'Alternative Phone 1', description: 'Alternative phone number 1' },
  { key: 'alt_phone2', label: 'Alternative Phone 2', description: 'Alternative phone number 2' },
  { key: 'alt_phone3', label: 'Alternative Phone 3', description: 'Alternative phone number 3' },
  { key: 'alt_phone4', label: 'Alternative Phone 4', description: 'Alternative phone number 4' },
  { key: 'alt_phone5', label: 'Alternative Phone 5', description: 'Alternative phone number 5' },

  // Company information
  { key: 'company', label: 'Company', description: 'Company name' },
  { key: 'title', label: 'Title', description: 'Job title' },

  // Web presence
  { key: 'website', label: 'Website', description: 'Company or personal website' },
  { key: 'linkedin', label: 'LinkedIn', description: 'LinkedIn profile URL' },
  { key: 'facebook', label: 'Facebook', description: 'Facebook profile or page URL' },
  { key: 'facebook_pixel', label: 'Facebook Pixel ID', description: 'Facebook Pixel ID for tracking' },
  { key: 'twitter', label: 'Twitter', description: 'Twitter profile URL' },
  { key: 'instagram', label: 'Instagram', description: 'Instagram profile URL' },
  { key: 'youtube', label: 'YouTube', description: 'YouTube channel URL' },

  // Location information
  { key: 'address', label: 'Address', description: 'Street address' },
  { key: 'city', label: 'City', description: 'City' },
  { key: 'state', label: 'State/Region', description: 'State, province, or region' },
  { key: 'country', label: 'Country', description: 'Country' },

  // Additional information
  { key: 'notes', label: 'Notes', description: 'Additional notes or information' },
];

/**
 * CSV Import Processor Component
 *
 * This component handles the upload, mapping, and cleaning of CSV files.
 */
const CSVImportProcessor = () => {
  // State for the stepper
  const [activeStep, setActiveStep] = useState(0);
  const steps = ['Upload CSV', 'Map Fields', 'Clean Data', 'Deduplicate', 'Enrich Data', 'Review & Save'];

  // State for the CSV data
  const [file, setFile] = useState(null);
  const [headers, setHeaders] = useState([]);
  const [data, setData] = useState([]);
  const [mappedFields, setMappedFields] = useState({});
  const [ignoredFields, setIgnoredFields] = useState([]);
  const [cleanedData, setCleanedData] = useState([]);
  const [deduplicatedData, setDeduplicatedData] = useState([]);
  const [enrichedData, setEnrichedData] = useState([]);
  const [previewData, setPreviewData] = useState([]);
  const [duplicates, setDuplicates] = useState([]);

  // State for deduplication
  const [deduplicationFields, setDeduplicationFields] = useState(['email']);
  const [deduplicationStrategy, setDeduplicationStrategy] = useState('keep_first');
  const [deduplicationStats, setDeduplicationStats] = useState({
    total: 0,
    unique: 0,
    duplicates: 0
  });

  // State for field validation
  const [validationErrors, setValidationErrors] = useState({});
  const [missingRequiredFields, setMissingRequiredFields] = useState([]);

  // UI state
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(null);
  const [error, setError] = useState(null);

  // Handle file upload
  const handleFileUpload = (event) => {
    const uploadedFile = event.target.files[0];
    if (uploadedFile) {
      setFile(uploadedFile);
      setLoading(true);

      Papa.parse(uploadedFile, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          const csvHeaders = results.meta.fields || [];
          const csvData = results.data || [];

          setHeaders(csvHeaders);
          setData(csvData);

          // Initialize field mapping with smart detection
          const initialMapping = {};
          csvHeaders.forEach(header => {
            // Try to find a matching standard field
            const normalizedHeader = header.toLowerCase().trim();

            // Special handling for name fields
            if (normalizedHeader === 'name' || normalizedHeader === 'contact' ||
                normalizedHeader === 'contact name' || normalizedHeader === 'full name' ||
                normalizedHeader === 'lead name') {
              initialMapping[header] = 'name';
            }
            // Special handling for first name
            else if (normalizedHeader === 'first name' || normalizedHeader === 'firstname' ||
                     normalizedHeader === 'first' || normalizedHeader === 'given name') {
              initialMapping[header] = 'first_name';
            }
            // Special handling for last name
            else if (normalizedHeader === 'last name' || normalizedHeader === 'lastname' ||
                     normalizedHeader === 'last' || normalizedHeader === 'surname' ||
                     normalizedHeader === 'family name') {
              initialMapping[header] = 'last_name';
            }
            // Special handling for email
            else if (normalizedHeader === 'email' || normalizedHeader === 'email address' ||
                     normalizedHeader === 'e-mail' || normalizedHeader === 'mail') {
              initialMapping[header] = 'email';
            }
            // Special handling for phone
            else if (normalizedHeader === 'phone' || normalizedHeader === 'phone number' ||
                     normalizedHeader === 'telephone' || normalizedHeader === 'mobile' ||
                     normalizedHeader === 'cell' || normalizedHeader === 'primary phone') {
              initialMapping[header] = 'phone';
            }
            // Special handling for alternative phone 1
            else if (normalizedHeader === 'alt phone' || normalizedHeader === 'alternative phone' ||
                     normalizedHeader === 'secondary phone' || normalizedHeader === 'other phone' ||
                     normalizedHeader === 'phone 2' || normalizedHeader === 'mobile phone' ||
                     normalizedHeader === 'alt phone 1' || normalizedHeader === 'alternative phone 1') {
              initialMapping[header] = 'alt_phone';
            }
            // Special handling for alternative phone 2
            else if (normalizedHeader === 'alt phone 2' || normalizedHeader === 'alternative phone 2' ||
                     normalizedHeader === 'phone 3' || normalizedHeader === 'other phone 2' ||
                     normalizedHeader === 'mobile phone 2') {
              initialMapping[header] = 'alt_phone2';
            }
            // Special handling for alternative phone 3
            else if (normalizedHeader === 'alt phone 3' || normalizedHeader === 'alternative phone 3' ||
                     normalizedHeader === 'phone 4' || normalizedHeader === 'other phone 3' ||
                     normalizedHeader === 'mobile phone 3') {
              initialMapping[header] = 'alt_phone3';
            }
            // Special handling for alternative phone 4
            else if (normalizedHeader === 'alt phone 4' || normalizedHeader === 'alternative phone 4' ||
                     normalizedHeader === 'phone 5' || normalizedHeader === 'other phone 4' ||
                     normalizedHeader === 'mobile phone 4') {
              initialMapping[header] = 'alt_phone4';
            }
            // Special handling for alternative phone 5
            else if (normalizedHeader === 'alt phone 5' || normalizedHeader === 'alternative phone 5' ||
                     normalizedHeader === 'phone 6' || normalizedHeader === 'other phone 5' ||
                     normalizedHeader === 'mobile phone 5') {
              initialMapping[header] = 'alt_phone5';
            }
            // Special handling for company
            else if (normalizedHeader === 'company' || normalizedHeader === 'company name' ||
                     normalizedHeader === 'organization' || normalizedHeader === 'business' ||
                     normalizedHeader === 'employer') {
              initialMapping[header] = 'company';
            }
            // Special handling for website
            else if (normalizedHeader === 'website' || normalizedHeader === 'web' ||
                     normalizedHeader === 'url' || normalizedHeader === 'site' ||
                     normalizedHeader === 'web address' || normalizedHeader === 'company website') {
              initialMapping[header] = 'website';
            }
            // Special handling for Facebook Pixel ID
            else if (normalizedHeader === 'facebook pixel' || normalizedHeader === 'facebook pixel id' ||
                     normalizedHeader === 'fb pixel' || normalizedHeader === 'fb pixel id' ||
                     normalizedHeader === 'pixel id' || normalizedHeader === 'pixel') {
              initialMapping[header] = 'facebook_pixel';
            }
            // Special handling for address
            else if (normalizedHeader === 'address' || normalizedHeader === 'street address' ||
                     normalizedHeader === 'address 1' || normalizedHeader === 'street') {
              initialMapping[header] = 'address';
            }
            // Try to find a matching standard field for other fields
            else {
              const matchedField = STANDARD_FIELDS.find(field =>
                normalizedHeader === field.key ||
                normalizedHeader.includes(field.key) ||
                field.key.includes(normalizedHeader)
              );

              if (matchedField) {
                initialMapping[header] = matchedField.key;
              } else {
                initialMapping[header] = '';
              }
            }
          });

          setMappedFields(initialMapping);
          setLoading(false);
          setActiveStep(1); // Move to mapping step
        },
        error: (error) => {
          console.error('Error parsing CSV:', error);
          setError(`Error parsing CSV: ${error.message}`);
          setLoading(false);
        }
      });
    }
  };

  // Handle field mapping change
  const handleMappingChange = (csvHeader, mappedValue) => {
    setMappedFields(prev => ({
      ...prev,
      [csvHeader]: mappedValue
    }));
  };

  // Handle ignored fields
  const handleIgnoreField = (csvHeader) => {
    if (ignoredFields.includes(csvHeader)) {
      setIgnoredFields(prev => prev.filter(field => field !== csvHeader));
    } else {
      setIgnoredFields(prev => [...prev, csvHeader]);
      // Clear any mapping for this field
      setMappedFields(prev => ({
        ...prev,
        [csvHeader]: ''
      }));
    }
  };

  // Validate field mappings
  const validateMappings = useCallback(() => {
    const errors = {};
    const mappedKeys = Object.values(mappedFields).filter(Boolean);

    // Check for duplicate mappings
    const duplicates = mappedKeys.filter((key, index, self) =>
      key && self.indexOf(key) !== index
    );

    if (duplicates.length > 0) {
      duplicates.forEach(duplicate => {
        const headers = Object.keys(mappedFields).filter(h => mappedFields[h] === duplicate);
        headers.forEach(h => {
          errors[h] = `Duplicate mapping for "${duplicate}"`;
        });
      });
    }

    // Check for missing required fields
    const missing = REQUIRED_FIELDS.filter(field => !mappedKeys.includes(field));
    setMissingRequiredFields(missing);

    setValidationErrors(errors);
    return Object.keys(errors).length === 0 && missing.length === 0;
  }, [mappedFields]);

  // Clean the data based on mappings
  const cleanData = useCallback(() => {
    if (data.length === 0) return;

    const cleaned = data.map(row => {
      const cleanedRow = {};
      const nameComponents = {
        first_name: '',
        last_name: '',
        full_name: ''
      };

      // First pass: collect all mapped fields and do basic cleaning
      Object.keys(row).forEach(header => {
        if (ignoredFields.includes(header)) return;

        const mappedField = mappedFields[header];
        if (mappedField) {
          // Clean the data based on field type
          let value = row[header] || '';

          // Basic cleaning for common fields
          if (mappedField === 'email') {
            value = value.trim().toLowerCase();
          } else if (mappedField === 'phone' ||
                    mappedField === 'alt_phone' ||
                    mappedField === 'alt_phone2' ||
                    mappedField === 'alt_phone3' ||
                    mappedField === 'alt_phone4' ||
                    mappedField === 'alt_phone5') {
            // Remove non-numeric characters except +, -, (, ), and spaces
            value = value.replace(/[^\d+\-\(\)\s]/g, '').trim();
          } else if (mappedField === 'name') {
            // Capitalize first letter of each word
            value = value.trim().replace(/\w\S*/g, (txt) =>
              txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
            );
            nameComponents.full_name = value;
          } else if (mappedField === 'first_name') {
            value = value.trim().replace(/\w\S*/g, (txt) =>
              txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
            );
            nameComponents.first_name = value;
          } else if (mappedField === 'last_name') {
            value = value.trim().replace(/\w\S*/g, (txt) =>
              txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
            );
            nameComponents.last_name = value;
          } else if (mappedField === 'website' ||
                    mappedField === 'linkedin' ||
                    mappedField === 'facebook' ||
                    mappedField === 'twitter' ||
                    mappedField === 'instagram' ||
                    mappedField === 'youtube') {
            // Ensure URLs have http:// or https://
            value = value.trim();
            if (value && !value.match(/^https?:\/\//)) {
              value = 'https://' + value;
            }
          } else if (mappedField === 'facebook_pixel') {
            // Clean Facebook Pixel ID - remove non-numeric characters
            value = value.trim().replace(/[^\d]/g, '');
          } else {
            value = value.trim();
          }

          cleanedRow[mappedField] = value;
        }
      });

      // Second pass: handle name fields
      // If we have first and last name but no full name, combine them
      if (nameComponents.first_name && nameComponents.last_name && !nameComponents.full_name) {
        cleanedRow['name'] = `${nameComponents.first_name} ${nameComponents.last_name}`;
      }
      // If we have full name but no first/last name, try to split it
      else if (nameComponents.full_name && (!nameComponents.first_name || !nameComponents.last_name)) {
        const nameParts = nameComponents.full_name.split(' ');
        if (nameParts.length >= 2) {
          if (!nameComponents.first_name) {
            cleanedRow['first_name'] = nameParts[0];
          }
          if (!nameComponents.last_name) {
            cleanedRow['last_name'] = nameParts.slice(1).join(' ');
          }
        }
      }

      return cleanedRow;
    });

    setCleanedData(cleaned);

    // Create a preview with a subset of the data
    setPreviewData(cleaned.slice(0, 5));
  }, [data, mappedFields, ignoredFields]);

  // Handle next step
  const handleNext = () => {
    if (activeStep === 1) {
      // Validate mappings before proceeding
      if (!validateMappings()) {
        setError('Please fix the mapping errors before proceeding.');
        return;
      }
    }

    if (activeStep === 2) {
      // Clean the data
      cleanData();
    }

    if (activeStep === 3) {
      // Deduplicate the data
      deduplicateData();
    }

    if (activeStep === 4) {
      // Set enriched data to deduplication data initially
      // The actual enrichment happens in the enrichment component
      setEnrichedData(deduplicatedData);
    }

    setActiveStep(prev => prev + 1);
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  // Handle save
  const handleSave = async () => {
    try {
      // First, save to localStorage as a backup
      localStorage.setItem('importedLeads', JSON.stringify(enrichedData));
      console.log('Saved leads to localStorage:', enrichedData);

      // Set loading state
      setLoading(true);

      // Import supabaseService
      const supabaseService = (await import('../../services/supabaseService')).default;
      console.log('Supabase service imported:', supabaseService);
      console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL);
      console.log('Supabase key available:', !!import.meta.env.VITE_SUPABASE_ANON_KEY);

      // Create batch info
      const batchInfo = {
        name: file ? file.name.replace('.csv', '') : 'CSV Import',
        fileName: file ? file.name : 'import.csv',
        mapping: mappedFields,
        settings: {
          ignoredFields,
          deduplicationFields,
          deduplicationStrategy
        }
      };
      console.log('Batch info:', batchInfo);
      console.log('Enriched data to save:', enrichedData);

      // Save to Supabase
      console.log('Saving to Supabase...');
      const result = await supabaseService.createLeads(enrichedData, batchInfo);
      console.log('Supabase save result:', result);

      setSuccess(`Successfully imported ${result.leads.length} leads to database.`);
      setLoading(false);

      // Reset the form after a delay
      setTimeout(() => {
        setFile(null);
        setHeaders([]);
        setData([]);
        setMappedFields({});
        setIgnoredFields([]);
        setCleanedData([]);
        setDeduplicatedData([]);
        setEnrichedData([]);
        setDuplicates([]);
        setPreviewData([]);
        setActiveStep(0);
      }, 2000);
    } catch (error) {
      console.error('Error saving leads:', error);
      setError(`Error saving leads to database: ${error.message}`);
      setLoading(false);
    }
  };

  // Effect to validate mappings when they change
  useEffect(() => {
    if (activeStep === 1) {
      validateMappings();
    }
  }, [mappedFields, activeStep, validateMappings]);

  // Effect to clean data when entering step 3
  useEffect(() => {
    if (activeStep === 2) {
      cleanData();
    }
  }, [activeStep, cleanData]);

  // Deduplicate data based on selected fields and strategy
  const deduplicateData = useCallback(() => {
    if (cleanedData.length === 0) return;

    // Create a map to track unique records and duplicates
    const uniqueMap = new Map();
    const duplicateRecords = [];
    const uniqueRecords = [];

    // Process each record
    cleanedData.forEach((record, index) => {
      // Create a key based on the deduplication fields
      const keyValues = deduplicationFields.map(field => record[field] || '').join('|');

      // If this is a duplicate
      if (uniqueMap.has(keyValues)) {
        const existingIndex = uniqueMap.get(keyValues);
        duplicateRecords.push({
          index,
          record,
          duplicateOf: existingIndex
        });
      } else {
        // This is a unique record
        uniqueMap.set(keyValues, index);
        uniqueRecords.push(record);
      }
    });

    // Apply deduplication strategy
    let finalRecords = [];

    if (deduplicationStrategy === 'keep_first') {
      // Keep only the first occurrence of each record
      finalRecords = uniqueRecords;
    } else if (deduplicationStrategy === 'keep_all') {
      // Keep all records, including duplicates
      finalRecords = cleanedData;
    } else if (deduplicationStrategy === 'merge') {
      // Merge duplicate records (combine non-empty fields)
      finalRecords = uniqueRecords.map((record, index) => {
        const keyValues = deduplicationFields.map(field => record[field] || '').join('|');
        const duplicates = duplicateRecords.filter(d =>
          deduplicationFields.map(field => d.record[field] || '').join('|') === keyValues
        );

        if (duplicates.length === 0) {
          return record;
        }

        // Merge fields from duplicates
        const mergedRecord = { ...record };
        duplicates.forEach(d => {
          Object.keys(d.record).forEach(field => {
            // If the original record has an empty field and the duplicate has a value, use the duplicate's value
            if (!mergedRecord[field] && d.record[field]) {
              mergedRecord[field] = d.record[field];
            }
          });
        });

        return mergedRecord;
      });
    }

    // Update state
    setDeduplicatedData(finalRecords);
    setDuplicates(duplicateRecords);
    setDeduplicationStats({
      total: cleanedData.length,
      unique: uniqueRecords.length,
      duplicates: duplicateRecords.length
    });

    // Update preview data
    setPreviewData(finalRecords.slice(0, 5));
  }, [cleanedData, deduplicationFields, deduplicationStrategy]);

  // Effect to deduplicate data when entering step 4
  useEffect(() => {
    if (activeStep === 3) {
      deduplicateData();
    }
  }, [activeStep, deduplicateData]);

  // Handle lead enrichment
  const handleLeadsEnriched = (enriched) => {
    setEnrichedData(enriched);
    setPreviewData(enriched.slice(0, 5));
  };

  // Render upload step
  const renderUploadStep = () => (
    <Box sx={{ p: 3, textAlign: 'center' }}>
      <input
        accept=".csv"
        style={{ display: 'none' }}
        id="csv-file-upload"
        type="file"
        onChange={handleFileUpload}
      />
      <label htmlFor="csv-file-upload">
        <Button
          variant="contained"
          component="span"
          startIcon={<UploadIcon />}
          size="large"
          sx={{ mb: 2 }}
        >
          Upload CSV File
        </Button>
      </label>

      {file && (
        <Typography variant="body1" sx={{ mt: 2 }}>
          Selected file: {file.name}
        </Typography>
      )}

      {loading && (
        <CircularProgress sx={{ mt: 2 }} />
      )}

      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Instructions
        </Typography>
        <Typography variant="body2" align="left">
          1. Prepare a CSV file with your lead data. The file should have headers.
          <br />
          2. Click the "Upload CSV File" button to select and upload your file.
          <br />
          3. In the next step, you'll map the CSV columns to our system fields.
          <br />
          4. Required fields: Email
          <br />
          5. Supported fields include: Name (or First Name + Last Name), Email, Phone, up to 5 Alternative Phone numbers, Company, Website, LinkedIn, Facebook, Facebook Pixel ID, Twitter, Instagram, YouTube, Title, Address, City, State, Country, and Notes.
          <br />
          6. The system will automatically detect and map common field names.
        </Typography>
      </Box>
    </Box>
  );

  // Render mapping step
  const renderMappingStep = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Map CSV Fields to System Fields
      </Typography>
      <Typography variant="body2" paragraph>
        Map each column from your CSV to the corresponding field in our system. Fields marked with * are required.
      </Typography>

      {missingRequiredFields.length > 0 && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          Missing required fields: {missingRequiredFields.map(field => (
            <strong key={field}>{field}</strong>
          )).reduce((prev, curr) => [prev, ', ', curr])}
        </Alert>
      )}

      <TableContainer component={Paper} sx={{ mb: 3 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>CSV Header</TableCell>
              <TableCell>Map To</TableCell>
              <TableCell>Sample Data</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {headers.map((header, index) => (
              <TableRow key={index} sx={{
                backgroundColor: ignoredFields.includes(header) ? '#f5f5f5' : 'inherit',
                opacity: ignoredFields.includes(header) ? 0.7 : 1
              }}>
                <TableCell>{header}</TableCell>
                <TableCell>
                  <FormControl
                    fullWidth
                    size="small"
                    error={!!validationErrors[header]}
                    disabled={ignoredFields.includes(header)}
                  >
                    <InputLabel>Map to field</InputLabel>
                    <Select
                      value={mappedFields[header] || ''}
                      label="Map to field"
                      onChange={(e) => handleMappingChange(header, e.target.value)}
                    >
                      <MenuItem value="">
                        <em>Don't map</em>
                      </MenuItem>
                      {STANDARD_FIELDS.map(field => (
                        <MenuItem key={field.key} value={field.key}>
                          {field.label} {REQUIRED_FIELDS.includes(field.key) && '*'}
                        </MenuItem>
                      ))}
                    </Select>
                    {validationErrors[header] && (
                      <Typography variant="caption" color="error">
                        {validationErrors[header]}
                      </Typography>
                    )}
                  </FormControl>
                </TableCell>
                <TableCell>
                  {data[0] && data[0][header] ? data[0][header].substring(0, 30) : ''}
                  {data[0] && data[0][header] && data[0][header].length > 30 ? '...' : ''}
                </TableCell>
                <TableCell>
                  <Tooltip title={ignoredFields.includes(header) ? "Include field" : "Ignore field"}>
                    <IconButton
                      size="small"
                      onClick={() => handleIgnoreField(header)}
                      color={ignoredFields.includes(header) ? "default" : "error"}
                    >
                      {ignoredFields.includes(header) ? <RefreshIcon /> : <DeleteIcon />}
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  // Render cleaning step
  const renderCleaningStep = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Clean and Validate Data
      </Typography>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Data Summary
            </Typography>
            <Typography variant="body2">
              Total records: {data.length}
              <br />
              Mapped fields: {Object.values(mappedFields).filter(Boolean).length}
              <br />
              Ignored fields: {ignoredFields.length}
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Data Cleaning Options
            </Typography>
            <FormControlLabel
              control={<Checkbox defaultChecked />}
              label="Trim whitespace from all fields"
            />
            <FormControlLabel
              control={<Checkbox defaultChecked />}
              label="Normalize email addresses (lowercase)"
            />
            <FormControlLabel
              control={<Checkbox defaultChecked />}
              label="Format phone numbers (keep only digits, +, -, (, ), spaces)"
            />
            <FormControlLabel
              control={<Checkbox defaultChecked />}
              label="Capitalize names (first letter of each word)"
            />
            <FormControlLabel
              control={<Checkbox defaultChecked />}
              label="Add https:// to website URLs if missing"
            />
            <FormControlLabel
              control={<Checkbox defaultChecked />}
              label="Handle name fields (combine first/last or split full name)"
            />
          </Paper>
        </Grid>
      </Grid>

      <Typography variant="subtitle1" gutterBottom>
        Data Preview (after cleaning)
      </Typography>

      <TableContainer component={Paper}>
        <Table size="small">
          <TableHead>
            <TableRow>
              {Object.values(mappedFields)
                .filter(Boolean)
                .filter(field => !ignoredFields.includes(field))
                .map((field, index) => (
                  <TableCell key={index}>
                    {STANDARD_FIELDS.find(f => f.key === field)?.label || field}
                  </TableCell>
                ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {previewData.map((row, rowIndex) => (
              <TableRow key={rowIndex}>
                {Object.values(mappedFields)
                  .filter(Boolean)
                  .filter(field => !ignoredFields.includes(field))
                  .map((field, cellIndex) => (
                    <TableCell key={cellIndex}>
                      {row[field] || ''}
                    </TableCell>
                  ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  // Render deduplication step
  const renderDeduplicationStep = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Deduplicate Data
      </Typography>

      <Typography variant="body2" paragraph>
        Identify and handle duplicate leads based on selected fields.
      </Typography>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Deduplication Fields
            </Typography>
            <Typography variant="body2" paragraph>
              Select fields to use for identifying duplicates:
            </Typography>

            <FormControlLabel
              control={
                <Checkbox
                  checked={deduplicationFields.includes('email')}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setDeduplicationFields(prev => [...prev, 'email']);
                    } else {
                      setDeduplicationFields(prev => prev.filter(f => f !== 'email'));
                    }
                  }}
                />
              }
              label="Email"
            />

            <FormControlLabel
              control={
                <Checkbox
                  checked={deduplicationFields.includes('phone')}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setDeduplicationFields(prev => [...prev, 'phone']);
                    } else {
                      setDeduplicationFields(prev => prev.filter(f => f !== 'phone'));
                    }
                  }}
                />
              }
              label="Phone"
            />

            <FormControlLabel
              control={
                <Checkbox
                  checked={deduplicationFields.includes('name')}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setDeduplicationFields(prev => [...prev, 'name']);
                    } else {
                      setDeduplicationFields(prev => prev.filter(f => f !== 'name'));
                    }
                  }}
                />
              }
              label="Name"
            />
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Deduplication Strategy
            </Typography>
            <Typography variant="body2" paragraph>
              Choose how to handle duplicate records:
            </Typography>

            <FormControl fullWidth>
              <Select
                value={deduplicationStrategy}
                onChange={(e) => setDeduplicationStrategy(e.target.value)}
                size="small"
              >
                <MenuItem value="keep_first">
                  Keep First Occurrence Only
                </MenuItem>
                <MenuItem value="keep_all">
                  Keep All Records (No Deduplication)
                </MenuItem>
                <MenuItem value="merge">
                  Merge Duplicate Records
                </MenuItem>
              </Select>
            </FormControl>

            <Typography variant="body2" sx={{ mt: 2, color: 'text.secondary' }}>
              {deduplicationStrategy === 'keep_first' &&
                'Only the first occurrence of each duplicate record will be kept.'}
              {deduplicationStrategy === 'keep_all' &&
                'All records will be kept, including duplicates.'}
              {deduplicationStrategy === 'merge' &&
                'Duplicate records will be merged, combining non-empty fields from all duplicates.'}
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      <Button
        variant="contained"
        color="primary"
        onClick={deduplicateData}
        sx={{ mb: 3 }}
      >
        Run Deduplication
      </Button>

      {deduplicationStats.total > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Deduplication Results
          </Typography>
          <Typography variant="body2">
            Total records: {deduplicationStats.total}
            <br />
            Unique records: {deduplicationStats.unique}
            <br />
            Duplicate records: {deduplicationStats.duplicates}
            <br />
            Records after deduplication: {deduplicationStrategy === 'keep_all'
              ? deduplicationStats.total
              : deduplicationStats.unique}
          </Typography>
        </Paper>
      )}

      <Typography variant="subtitle1" gutterBottom>
        Data Preview (after deduplication)
      </Typography>

      <TableContainer component={Paper}>
        <Table size="small">
          <TableHead>
            <TableRow>
              {Object.values(mappedFields)
                .filter(Boolean)
                .filter(field => !ignoredFields.includes(field))
                .map((field, index) => (
                  <TableCell key={index}>
                    {STANDARD_FIELDS.find(f => f.key === field)?.label || field}
                  </TableCell>
                ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {previewData.map((row, rowIndex) => (
              <TableRow key={rowIndex}>
                {Object.values(mappedFields)
                  .filter(Boolean)
                  .filter(field => !ignoredFields.includes(field))
                  .map((field, cellIndex) => (
                    <TableCell key={cellIndex}>
                      {row[field] || ''}
                    </TableCell>
                  ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  // Render enrichment step
  const renderEnrichmentStep = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Enrich Lead Data
      </Typography>

      <Typography variant="body2" paragraph>
        Enrich your leads with additional information such as website, contact details, and Facebook Pixel detection.
      </Typography>

      <LeadEnrichmentPanel
        leads={deduplicatedData}
        onLeadsEnriched={handleLeadsEnriched}
      />
    </Box>
  );

  // Render review step
  const renderReviewStep = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Review and Save
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        You're about to import {enrichedData.length} leads. Please review the data before saving.
      </Alert>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Import Summary
        </Typography>
        <Typography variant="body2">
          Total records: {enrichedData.length}
          <br />
          Fields included: {Object.values(mappedFields).filter(Boolean).length}
          <br />
          Fields mapped: {Object.values(mappedFields)
            .filter(Boolean)
            .map(field => STANDARD_FIELDS.find(f => f.key === field)?.label || field)
            .join(', ')}
          <br />
          Duplicates removed: {deduplicationStats.duplicates}
          <br />
          Websites found: {enrichedData.filter(lead => lead.website).length}
          <br />
          Facebook Pixels detected: {enrichedData.filter(lead => lead.facebook_pixel === true).length}
          <br />
          Social media profiles found: {
            enrichedData.filter(lead => lead.facebook).length +
            enrichedData.filter(lead => lead.linkedin).length +
            enrichedData.filter(lead => lead.instagram).length +
            enrichedData.filter(lead => lead.twitter).length +
            enrichedData.filter(lead => lead.youtube).length +
            enrichedData.filter(lead => lead.tiktok).length
          }
        </Typography>
      </Paper>

      <Button
        variant="contained"
        color="primary"
        startIcon={<SaveIcon />}
        onClick={handleSave}
        size="large"
      >
        Save Imported Leads
      </Button>
    </Box>
  );

  return (
    <Box sx={{ width: '100%' }}>
      <Stepper activeStep={activeStep} sx={{ pt: 3, pb: 5 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      {activeStep === 0 && renderUploadStep()}
      {activeStep === 1 && renderMappingStep()}
      {activeStep === 2 && renderCleaningStep()}
      {activeStep === 3 && renderDeduplicationStep()}
      {activeStep === 4 && renderEnrichmentStep()}
      {activeStep === 5 && renderReviewStep()}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2, p: 2 }}>
        <Button
          disabled={activeStep === 0}
          onClick={handleBack}
        >
          Back
        </Button>

        {activeStep < steps.length - 1 ? (
          <Button
            variant="contained"
            onClick={handleNext}
            disabled={activeStep === 0 && !file}
          >
            Next
          </Button>
        ) : null}
      </Box>

      {/* Success and Error Messages */}
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setSuccess(null)} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CSVImportProcessor;
