import os
import uuid
from typing import List, Dict, Any, Optional
from fastapi import FastAP<PERSON>, BackgroundTasks, HTTPException, Depends, status
from pydantic import BaseModel, HttpUrl

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.auth import get_current_active_user, User
from libs.interfaces import (
    FormMetadata, 
    FormStatus, 
    Event, 
    EventType, 
    FormAnalyzedPayload,
    FieldType,
    FormField
)

# Import service-specific modules
from .analysis_engine import FormAnalysisEngine
from .event_producer import EventProducer
from .event_consumer import EventConsumer

# Initialize FastAPI app
app = FastAPI(
    title="Form Analysis Service",
    description="Service for analyzing form structure and fields",
    version="0.1.0"
)

# Initialize logger
logger = get_service_logger("form-analysis")

# Initialize event producer
event_producer = EventProducer(
    bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092"),
    topic=os.getenv("FORM_ANALYZED_TOPIC", "form.analyzed")
)

# Initialize event consumer
event_consumer = EventConsumer(
    bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092"),
    topic=os.getenv("FORM_DISCOVERED_TOPIC", "form.discovered"),
    group_id="form-analysis-service"
)

# Initialize form analysis engine
analysis_engine = FormAnalysisEngine()


# Request/Response models
class FormAnalysisRequest(BaseModel):
    form_id: str
    url: HttpUrl
    html_content: str
    screenshot_path: Optional[str] = None


class FormAnalysisResponse(BaseModel):
    form_id: str
    status: str
    message: str


# Background task for form analysis
async def analyze_form_task(request: FormAnalysisRequest):
    """Background task to analyze a form."""
    logger.info(f"Starting form analysis for {request.url}", props={"form_id": request.form_id})
    
    try:
        # Analyze the form
        analysis_result = await analysis_engine.analyze_form(
            form_id=request.form_id,
            url=str(request.url),
            html_content=request.html_content,
            screenshot_path=request.screenshot_path
        )
        
        logger.info(f"Form analysis completed for {request.url}", props={"form_id": request.form_id})
        
        # Create and publish event
        event = Event(
            event_id=str(uuid.uuid4()),
            event_type=EventType.FORM_ANALYZED,
            producer="form-analysis-service",
            payload=FormAnalyzedPayload(
                form_id=request.form_id,
                url=str(request.url),
                fields=analysis_result["fields"],
                has_captcha=analysis_result["has_captcha"],
                multi_step=analysis_result["multi_step"],
                submission_endpoint=analysis_result["submission_endpoint"],
                method=analysis_result["method"],
                success_indicators=analysis_result["success_indicators"],
                error_indicators=analysis_result["error_indicators"],
                metadata=analysis_result["metadata"]
            ).dict()
        )
        
        await event_producer.produce_event(event)
        
        logger.info(f"Published form analysis event", props={
            "form_id": request.form_id,
            "url": str(request.url)
        })
        
    except Exception as e:
        logger.error(f"Error during form analysis: {str(e)}", props={"form_id": request.form_id})


# API endpoints
@app.post("/api/v1/analyze", response_model=FormAnalysisResponse)
async def analyze_form(
    request: FormAnalysisRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """
    Analyze a form structure and fields.
    
    This endpoint initiates a background task to analyze a form.
    """
    logger.info(f"Form analysis requested for {request.url}", props={
        "form_id": request.form_id,
        "user": current_user.username
    })
    
    background_tasks.add_task(analyze_form_task, request)
    
    return FormAnalysisResponse(
        form_id=request.form_id,
        status="processing",
        message=f"Form analysis started for {request.url}"
    )


@app.get("/api/v1/analyze/{form_id}", response_model=FormMetadata)
async def get_analysis_result(
    form_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get the result of a form analysis.
    
    This endpoint returns the analysis result for a form.
    """
    # In a real implementation, you would retrieve the analysis result from a database
    # This is a simplified example
    logger.info(f"Analysis result requested for form {form_id}", props={"user": current_user.username})
    
    # Mock response - in a real implementation, retrieve from database
    return FormMetadata(
        form_id=form_id,
        url="https://example.com/contact",
        title="Contact Form",
        description="Send us a message",
        fields=[
            FormField(
                field_id="name",
                name="name",
                label="Name",
                field_type=FieldType.NAME,
                required=True
            ),
            FormField(
                field_id="email",
                name="email",
                label="Email",
                field_type=FieldType.EMAIL,
                required=True
            ),
            FormField(
                field_id="message",
                name="message",
                label="Message",
                field_type=FieldType.MESSAGE,
                required=True
            )
        ],
        multi_step=False,
        has_captcha=False,
        submission_endpoint="/contact",
        method="POST",
        status=FormStatus.ANALYZED
    )


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}


# Start the event consumer when the app starts
@app.on_event("startup")
async def startup_event():
    """Start the event consumer when the app starts."""
    await event_consumer.start(analyze_form_task)


# Stop the event consumer when the app stops
@app.on_event("shutdown")
async def shutdown_event():
    """Stop the event consumer when the app stops."""
    await event_consumer.stop()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
