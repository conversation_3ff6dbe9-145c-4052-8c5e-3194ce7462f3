import React from 'react';
import { Box, Typography, SxProps, Theme } from '@mui/material';

interface StatsCardProps {
  title: string;
  value: string | number;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  icon?: React.ReactNode;
  animationDelay?: string;
  sx?: SxProps<Theme>;
}

/**
 * StatsCard Component
 * 
 * A reusable card component for displaying statistics with consistent styling
 */
const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  color = 'primary',
  icon,
  animationDelay = '0s',
  sx = {}
}) => {
  return (
    <Box
      sx={{
        p: 3,
        bgcolor: 'background.paper',
        borderRadius: 2,
        boxShadow: 1,
        border: '1px solid',
        borderColor: `${color}.light`,
        position: 'relative',
        overflow: 'hidden',
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 6px 12px rgba(0, 0, 0, 0.1)',
        },
        ...sx
      }}
      className="slide-in"
      style={{ animationDelay }}
    >
      {/* Accent bar */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '4px',
        height: '100%',
        bgcolor: `${color}.main`
      }} />

      {/* Content */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Box>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            {title}
          </Typography>
          <Typography variant="h4" fontWeight="bold">
            {value}
          </Typography>
        </Box>
        
        {icon && (
          <Box sx={{ 
            color: `${color}.main`, 
            opacity: 0.2, 
            fontSize: '2.5rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {icon}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default StatsCard;
