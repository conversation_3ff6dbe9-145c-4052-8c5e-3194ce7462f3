import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Divider,
  Snackbar,
  Alert,
  Paper,
  FormControlLabel,
  Switch,
} from '@mui/material';
import { Save as SaveIcon } from '@mui/icons-material';

/**
 * Lead Data Settings Component
 * 
 * This component allows users to configure default lead data for form submissions.
 */
const LeadDataSettings = () => {
  // Default lead data state
  const [leadData, setLeadData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    message: '',
  });
  
  // Additional fields state
  const [additionalFields, setAdditionalFields] = useState([
    { key: '', value: '' }
  ]);
  
  // Settings state
  const [settings, setSettings] = useState({
    useDefaultMessage: true,
    autoFillForms: true,
  });
  
  // UI state
  const [success, setSuccess] = useState(null);
  const [error, setError] = useState(null);
  
  // Load saved lead data from localStorage on component mount
  useEffect(() => {
    try {
      const savedLeadData = localStorage.getItem('defaultLeadData');
      const savedSettings = localStorage.getItem('formFillSettings');
      const savedAdditionalFields = localStorage.getItem('additionalFormFields');
      
      if (savedLeadData) {
        setLeadData(JSON.parse(savedLeadData));
      }
      
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
      
      if (savedAdditionalFields) {
        setAdditionalFields(JSON.parse(savedAdditionalFields));
      }
    } catch (error) {
      console.error('Error loading saved lead data:', error);
    }
  }, []);
  
  // Handle input change for lead data
  const handleLeadDataChange = (e) => {
    const { name, value } = e.target;
    setLeadData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };
  
  // Handle input change for additional fields
  const handleAdditionalFieldChange = (index, field, value) => {
    const updatedFields = [...additionalFields];
    updatedFields[index][field] = value;
    setAdditionalFields(updatedFields);
  };
  
  // Add a new additional field
  const handleAddField = () => {
    setAdditionalFields([...additionalFields, { key: '', value: '' }]);
  };
  
  // Remove an additional field
  const handleRemoveField = (index) => {
    const updatedFields = [...additionalFields];
    updatedFields.splice(index, 1);
    setAdditionalFields(updatedFields);
  };
  
  // Handle settings change
  const handleSettingsChange = (e) => {
    const { name, checked } = e.target;
    setSettings((prevSettings) => ({
      ...prevSettings,
      [name]: checked,
    }));
  };
  
  // Save lead data to localStorage
  const handleSave = () => {
    try {
      // Filter out empty additional fields
      const filteredAdditionalFields = additionalFields.filter(
        (field) => field.key.trim() !== '' && field.value.trim() !== ''
      );
      
      // Save to localStorage
      localStorage.setItem('defaultLeadData', JSON.stringify(leadData));
      localStorage.setItem('formFillSettings', JSON.stringify(settings));
      localStorage.setItem('additionalFormFields', JSON.stringify(filteredAdditionalFields));
      
      setSuccess('Lead data settings saved successfully!');
    } catch (error) {
      console.error('Error saving lead data:', error);
      setError('Error saving lead data. Please try again.');
    }
  };
  
  // Generate default message based on name and company
  const generateDefaultMessage = () => {
    const name = leadData.name || 'I';
    const company = leadData.company ? ` from ${leadData.company}` : '';
    
    return `Hi, ${name}${company} am interested in learning more about your services. Please contact me at your earliest convenience. Thank you!`;
  };
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        Lead Data Settings
      </Typography>
      <Typography variant="body1" color="textSecondary" paragraph>
        Configure default lead data for form submissions. This information will be used to automatically fill out forms.
      </Typography>
      
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Basic Information
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              label="Full Name"
              name="name"
              value={leadData.name}
              onChange={handleLeadDataChange}
              fullWidth
              margin="normal"
              helperText="Your full name for form submissions"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              label="Email Address"
              name="email"
              type="email"
              value={leadData.email}
              onChange={handleLeadDataChange}
              fullWidth
              margin="normal"
              helperText="Your email address for form submissions"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              label="Phone Number"
              name="phone"
              value={leadData.phone}
              onChange={handleLeadDataChange}
              fullWidth
              margin="normal"
              helperText="Your phone number for form submissions"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              label="Company Name"
              name="company"
              value={leadData.company}
              onChange={handleLeadDataChange}
              fullWidth
              margin="normal"
              helperText="Your company name (if applicable)"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Default Message"
              name="message"
              value={leadData.message || generateDefaultMessage()}
              onChange={handleLeadDataChange}
              fullWidth
              multiline
              rows={4}
              margin="normal"
              helperText="Default message for form submissions"
            />
          </Grid>
        </Grid>
      </Paper>
      
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Additional Fields
        </Typography>
        <Typography variant="body2" color="textSecondary" paragraph>
          Add any additional fields that you commonly use in form submissions.
        </Typography>
        
        {additionalFields.map((field, index) => (
          <Grid container spacing={2} key={index} sx={{ mb: 2 }}>
            <Grid item xs={5}>
              <TextField
                label="Field Name"
                value={field.key}
                onChange={(e) => handleAdditionalFieldChange(index, 'key', e.target.value)}
                fullWidth
                size="small"
                placeholder="e.g., address, zip_code"
              />
            </Grid>
            <Grid item xs={5}>
              <TextField
                label="Field Value"
                value={field.value}
                onChange={(e) => handleAdditionalFieldChange(index, 'value', e.target.value)}
                fullWidth
                size="small"
              />
            </Grid>
            <Grid item xs={2} sx={{ display: 'flex', alignItems: 'center' }}>
              <Button
                variant="outlined"
                color="error"
                size="small"
                onClick={() => handleRemoveField(index)}
                disabled={additionalFields.length === 1}
              >
                Remove
              </Button>
            </Grid>
          </Grid>
        ))}
        
        <Button
          variant="outlined"
          onClick={handleAddField}
          sx={{ mt: 1 }}
        >
          Add Field
        </Button>
      </Paper>
      
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Form Fill Settings
        </Typography>
        
        <FormControlLabel
          control={
            <Switch
              checked={settings.useDefaultMessage}
              onChange={handleSettingsChange}
              name="useDefaultMessage"
            />
          }
          label="Use default message when no message field is provided"
        />
        
        <FormControlLabel
          control={
            <Switch
              checked={settings.autoFillForms}
              onChange={handleSettingsChange}
              name="autoFillForms"
            />
          }
          label="Automatically fill forms with default data"
        />
      </Paper>
      
      <Button
        variant="contained"
        color="primary"
        startIcon={<SaveIcon />}
        onClick={handleSave}
        size="large"
      >
        Save Settings
      </Button>
      
      {/* Success and Error Messages */}
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setSuccess(null)} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>
      
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default LeadDataSettings;
