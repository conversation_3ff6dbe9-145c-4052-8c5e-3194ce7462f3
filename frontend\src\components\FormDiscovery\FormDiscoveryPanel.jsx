import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  TextField,
  Typography,
  Alert,
  Snackbar,
  Paper,
  Chip,
} from '@mui/material';
import { Search as SearchIcon, Send as SendIcon } from '@mui/icons-material';
import formDiscoveryService from '../../services/formDiscoveryService';

/**
 * Form Discovery Panel Component
 *
 * This component provides a UI for discovering and submitting forms.
 */
const FormDiscoveryPanel = () => {
  // Form state
  const [url, setUrl] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [industry, setIndustry] = useState('');
  const [priority, setPriority] = useState(1);
  const [maxPages, setMaxPages] = useState(10);
  const [maxDepth, setMaxDepth] = useState(3);
  const [useAiReasoning, setUseAiReasoning] = useState(true);

  // Lead data state
  const [leadName, setLeadName] = useState('');
  const [leadEmail, setLeadEmail] = useState('');
  const [leadPhone, setLeadPhone] = useState('');
  const [leadMessage, setLeadMessage] = useState('');

  // Load saved lead data from localStorage on component mount
  useEffect(() => {
    try {
      const savedLeadData = localStorage.getItem('defaultLeadData');
      const savedSettings = localStorage.getItem('formFillSettings');
      const savedAdditionalFields = localStorage.getItem('additionalFormFields');

      if (savedLeadData) {
        const parsedData = JSON.parse(savedLeadData);
        setLeadName(parsedData.name || '');
        setLeadEmail(parsedData.email || '');
        setLeadPhone(parsedData.phone || '');
        setLeadMessage(parsedData.message || '');
      }
    } catch (error) {
      console.error('Error loading saved lead data:', error);
    }
  }, []);

  // UI state
  const [loading, setLoading] = useState(false);
  const [discoveryJobId, setDiscoveryJobId] = useState(null);
  const [submissionJobId, setSubmissionJobId] = useState(null);
  const [discoveredForms, setDiscoveredForms] = useState([]);
  const [selectedFormId, setSelectedFormId] = useState(null);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [activeTab, setActiveTab] = useState('discover'); // 'discover' or 'submit'

  // Handle form discovery
  const handleDiscoverForms = async () => {
    if (!url) {
      setError('Please enter a URL');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);
    setDiscoveredForms([]);
    setDiscoveryJobId(null);

    try {
      // Start form discovery
      const result = await formDiscoveryService.discoverForms(url, {
        companyName,
        industry,
        priority,
        maxPages,
        maxDepth,
      });

      setDiscoveryJobId(result.job_id);
      setSuccess(`Form discovery started with job ID: ${result.job_id}`);

      // Poll for discovery completion
      let discoveryStatus;
      let attempts = 0;
      const maxAttempts = 30; // 30 attempts with 2-second delay = 60 seconds max

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2-second delay
        discoveryStatus = await formDiscoveryService.getDiscoveryStatus(result.job_id);

        if (discoveryStatus.completed || discoveryStatus.status === 'failed') {
          break;
        }

        attempts++;
      }

      if (!discoveryStatus.completed) {
        setError('Form discovery timed out');
        setLoading(false);
        return;
      }

      if (discoveryStatus.status === 'failed') {
        setError(`Form discovery failed: ${discoveryStatus.error}`);
        setLoading(false);
        return;
      }

      // Set discovered forms
      setDiscoveredForms(discoveryStatus.forms || []);

      if (discoveryStatus.forms_discovered === 0) {
        setError('No forms discovered');
      } else {
        setSuccess(`Discovered ${discoveryStatus.forms_discovered} forms`);
        // Select the first form by default
        if (discoveryStatus.forms && discoveryStatus.forms.length > 0) {
          setSelectedFormId(discoveryStatus.forms[0].form_id);
        }
      }
    } catch (error) {
      setError(`Error discovering forms: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmitForm = async () => {
    if (!url) {
      setError('Please enter a URL');
      return;
    }

    if (!leadName || !leadEmail) {
      setError('Please enter at least name and email');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);
    setSubmissionJobId(null);

    try {
      // Prepare lead data
      const leadData = {
        name: leadName,
        email: leadEmail,
        phone: leadPhone,
        message: leadMessage,
      };

      // Add additional fields from settings
      try {
        const savedAdditionalFields = localStorage.getItem('additionalFormFields');
        if (savedAdditionalFields) {
          const additionalFields = JSON.parse(savedAdditionalFields);
          additionalFields.forEach(field => {
            if (field.key && field.value) {
              leadData[field.key] = field.value;
            }
          });
        }
      } catch (error) {
        console.error('Error loading additional fields:', error);
      }

      // Submit form
      const result = await formDiscoveryService.submitForm(
        url,
        leadData,
        selectedFormId,
        {
          useAiReasoning,
        }
      );

      setSubmissionJobId(result.job_id);
      setSuccess(`Form submission started with job ID: ${result.job_id}`);

      // Poll for submission completion
      let submissionStatus;
      let attempts = 0;
      const maxAttempts = 30; // 30 attempts with 2-second delay = 60 seconds max

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2-second delay
        submissionStatus = await formDiscoveryService.getSubmissionStatus(result.job_id);

        if (submissionStatus.status === 'completed' || submissionStatus.status === 'failed') {
          break;
        }

        attempts++;
      }

      if (submissionStatus.status === 'failed') {
        setError(`Form submission failed: ${submissionStatus.error}`);
      } else {
        setSuccess(`Form submitted successfully! Fields filled: ${submissionStatus.fields_filled}`);
      }
    } catch (error) {
      setError(`Error submitting form: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle discover and submit in one operation
  const handleDiscoverAndSubmit = async () => {
    if (!url) {
      setError('Please enter a URL');
      return;
    }

    if (!leadName || !leadEmail) {
      setError('Please enter at least name and email');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);
    setDiscoveryJobId(null);
    setSubmissionJobId(null);

    try {
      // Prepare lead data
      const leadData = {
        name: leadName,
        email: leadEmail,
        phone: leadPhone,
        message: leadMessage,
      };

      // Add additional fields from settings
      try {
        const savedAdditionalFields = localStorage.getItem('additionalFormFields');
        if (savedAdditionalFields) {
          const additionalFields = JSON.parse(savedAdditionalFields);
          additionalFields.forEach(field => {
            if (field.key && field.value) {
              leadData[field.key] = field.value;
            }
          });
        }
      } catch (error) {
        console.error('Error loading additional fields:', error);
      }

      // Discover and submit form
      const result = await formDiscoveryService.discoverAndSubmitForm(
        url,
        leadData,
        {
          companyName,
          industry,
          priority,
          maxPages,
          maxDepth,
          useAiReasoning,
        }
      );

      setSuccess(`Form discovered and submitted successfully! Fields filled: ${result.fields_filled}`);
    } catch (error) {
      setError(`Error in discover and submit operation: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Form Discovery & Submission
      </Typography>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ mb: 2 }}>
          <Button
            variant={activeTab === 'discover' ? 'contained' : 'outlined'}
            onClick={() => setActiveTab('discover')}
            sx={{ mr: 1 }}
          >
            Discover Forms
          </Button>
          <Button
            variant={activeTab === 'submit' ? 'contained' : 'outlined'}
            onClick={() => setActiveTab('submit')}
          >
            Submit Form
          </Button>
        </Box>

        <Divider sx={{ mb: 3 }} />

        {/* URL Input */}
        <TextField
          label="Website URL"
          variant="outlined"
          fullWidth
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          placeholder="https://example.com"
          sx={{ mb: 3 }}
        />

        {activeTab === 'discover' && (
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Company Name"
                variant="outlined"
                fullWidth
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Industry"
                variant="outlined"
                fullWidth
                value={industry}
                onChange={(e) => setIndustry(e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={priority}
                  label="Priority"
                  onChange={(e) => setPriority(e.target.value)}
                >
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((value) => (
                    <MenuItem key={value} value={value}>
                      {value}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="Max Pages"
                variant="outlined"
                fullWidth
                type="number"
                value={maxPages}
                onChange={(e) => setMaxPages(parseInt(e.target.value))}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="Max Depth"
                variant="outlined"
                fullWidth
                type="number"
                value={maxDepth}
                onChange={(e) => setMaxDepth(parseInt(e.target.value))}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<SearchIcon />}
                onClick={handleDiscoverForms}
                disabled={loading}
                fullWidth
              >
                {loading ? <CircularProgress size={24} /> : 'Discover Forms'}
              </Button>
            </Grid>
          </Grid>
        )}

        {activeTab === 'submit' && (
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Name"
                variant="outlined"
                fullWidth
                value={leadName}
                onChange={(e) => setLeadName(e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Email"
                variant="outlined"
                fullWidth
                type="email"
                value={leadEmail}
                onChange={(e) => setLeadEmail(e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Phone"
                variant="outlined"
                fullWidth
                value={leadPhone}
                onChange={(e) => setLeadPhone(e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={useAiReasoning}
                    onChange={(e) => setUseAiReasoning(e.target.checked)}
                  />
                }
                label="Use AI Reasoning"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Message"
                variant="outlined"
                fullWidth
                multiline
                rows={4}
                value={leadMessage}
                onChange={(e) => setLeadMessage(e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<SendIcon />}
                onClick={handleSubmitForm}
                disabled={loading}
                fullWidth
              >
                {loading ? <CircularProgress size={24} /> : 'Submit Form'}
              </Button>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleDiscoverAndSubmit}
                disabled={loading}
                fullWidth
              >
                {loading ? <CircularProgress size={24} /> : 'Discover & Submit'}
              </Button>
            </Grid>
          </Grid>
        )}
      </Paper>

      {/* Results Section */}
      {discoveredForms.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Discovered Forms
            </Typography>
            <Grid container spacing={2}>
              {discoveredForms.map((form) => (
                <Grid item xs={12} key={form.form_id}>
                  <Paper
                    sx={{
                      p: 2,
                      border: selectedFormId === form.form_id ? '2px solid #1976d2' : 'none',
                      cursor: 'pointer',
                    }}
                    onClick={() => setSelectedFormId(form.form_id)}
                  >
                    <Typography variant="subtitle1">{form.title || 'Untitled Form'}</Typography>
                    <Typography variant="body2" color="textSecondary">
                      {form.description || 'No description'}
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                      <Chip
                        label={`Fields: ${form.fields ? form.fields.length : 0}`}
                        size="small"
                        sx={{ mr: 1 }}
                      />
                      {form.multi_step && (
                        <Chip label="Multi-step" size="small" sx={{ mr: 1 }} />
                      )}
                      {form.has_captcha && (
                        <Chip label="Has CAPTCHA" size="small" sx={{ mr: 1 }} />
                      )}
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Error and Success Messages */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setSuccess(null)} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default FormDiscoveryPanel;
