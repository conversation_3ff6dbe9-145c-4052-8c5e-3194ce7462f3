import React from 'react';
import { Box, Container, Typography, Breadcrumbs, Link } from '@mui/material';
import { Home as HomeIcon, Search as SearchIcon } from '@mui/icons-material';
import FormDiscoveryPanel from '../components/FormDiscovery';

/**
 * Form Discovery Page
 * 
 * This page displays the Form Discovery Panel.
 */
const FormDiscoveryPage = () => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 3 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            underline="hover"
            color="inherit"
            href="/"
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </Link>
          <Typography
            sx={{ display: 'flex', alignItems: 'center' }}
            color="text.primary"
          >
            <SearchIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Form Discovery
          </Typography>
        </Breadcrumbs>
        
        <FormDiscoveryPanel />
      </Box>
    </Container>
  );
};

export default FormDiscoveryPage;
