version: '3.8'

services:
  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
    ports:
      - "8000:8000"
    environment:
      - FORM_DISCOVERY_SERVICE_URL=http://form-discovery:8001
      - FORM_SUBMISSION_SERVICE_URL=http://form-submission:8002
      - LEAD_MANAGEMENT_SERVICE_URL=http://lead-management:8003
      - RESPONSE_INGESTION_SERVICE_URL=http://response-ingestion:8004
      - CONVERSATION_MANAGEMENT_SERVICE_URL=http://conversation-management:8005
      - AI_ORCHESTRATOR_SERVICE_URL=http://ai-orchestrator:8006
      - ANALYTICS_SERVICE_URL=http://analytics:8007
    depends_on:
      - form-discovery
      - form-submission
    networks:
      - lead-generation-network

  # Form Discovery Service
  form-discovery:
    build:
      context: .
      dockerfile: ./form-discovery/Dockerfile
    ports:
      - "8001:8001"
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - FORM_DISCOVERED_TOPIC=form.discovered
      - REDIS_URL=redis://redis:6379/0
      - MONGODB_URI=mongodb://mongodb:27017/form-discovery
    depends_on:
      - kafka
      - redis
      - mongodb
    networks:
      - lead-generation-network

  # Form Submission Service
  form-submission:
    build:
      context: .
      dockerfile: ./form-submission/Dockerfile
    ports:
      - "8002:8002"
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - FORM_SUBMITTED_TOPIC=form.submitted
      - REDIS_URL=redis://redis:6379/0
      - MONGODB_URI=mongodb://mongodb:27017/form-submission
    depends_on:
      - kafka
      - redis
      - mongodb
    networks:
      - lead-generation-network

  # Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.3.0
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
    networks:
      - lead-generation-network

  kafka:
    image: confluentinc/cp-kafka:7.3.0
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    depends_on:
      - zookeeper
    networks:
      - lead-generation-network

  # Redis
  redis:
    image: redis:7.0
    ports:
      - "6379:6379"
    networks:
      - lead-generation-network

  # MongoDB
  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - lead-generation-network

networks:
  lead-generation-network:
    driver: bridge

volumes:
  mongodb_data:
