"""
Unified Form Agent - Combines Form Discovery and Submission
----------------------------------------------------------
- Integrates LangGraph for workflow orchestration
- Uses AI reasoning for form analysis and filling
- Provides a unified interface for form discovery and submission
"""
import asyncio
import datetime
import json
import os
import re
import time
import uuid
from typing import Dict, List, Any, Optional, TypedDict
from urllib.parse import urlparse

from langgraph.graph import Graph, StateGraph, START, END
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger

# Load environment variables
load_dotenv()

# Initialize logger
logger = get_service_logger("form-discovery", "form-agent")

# --- State Schema ---
class FormAgentState(TypedDict):
    """State for the form agent workflow."""
    url: str
    lead_data: Dict[str, Any]
    form_id: Optional[str]
    discovered_forms: Optional[List[Dict[str, Any]]]
    selected_form: Optional[Dict[str, Any]]
    llm_decision: Optional[str]
    critical_fields: Optional[Dict[str, str]]
    optional_fields: Optional[Dict[str, str]]
    confidence_score: Optional[float]
    fill_result: Optional[str]
    field_coverage: Optional[Dict[str, Any]]
    error: Optional[str]
    retry_count: Optional[int]
    domain: Optional[str]
    job_id: Optional[str]

class FormAgent:
    """
    Unified agent for form discovery and submission.
    """

    def __init__(self, use_ai_reasoning: bool = True):
        """
        Initialize the form agent.

        Args:
            use_ai_reasoning: Whether to use AI reasoning for form analysis and filling
        """
        self.logger = logger
        self.use_ai_reasoning = use_ai_reasoning
        self.results = {}  # In-memory storage for results (would use a database in production)

        # Initialize LangGraph
        self._initialize_langgraph()

    def _initialize_langgraph(self):
        """Initialize the LangGraph workflow."""
        # Define the workflow graph
        builder = StateGraph(FormAgentState)

        # Add nodes to the workflow
        builder.add_node("discover_forms", self._discover_forms_node)
        builder.add_node("select_form", self._select_form_node)
        builder.add_node("llm_reasoning", self._llm_reasoning_node)
        builder.add_node("should_retry", self._should_retry_node)
        builder.add_node("llm_retry", self._llm_retry_node)
        builder.add_node("fill_form", self._fill_form_node)

        # Define the edges for the workflow
        builder.add_edge(START, "discover_forms")
        builder.add_edge("discover_forms", "select_form")
        builder.add_edge("select_form", "llm_reasoning")
        builder.add_edge("llm_reasoning", "should_retry")

        # Branch based on should_retry decision
        builder.add_conditional_edges(
            "should_retry",
            lambda state: state.get("should_retry", False),
            {
                True: "llm_retry",
                False: "fill_form"
            }
        )

        # From llm_retry back to fill_form
        builder.add_edge("llm_retry", "fill_form")

        # Complete the workflow
        builder.add_edge("fill_form", END)

        # Compile the graph
        self.workflow = builder.compile()

    async def _discover_forms_node(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Discover forms on the website.

        Args:
            state: Current state

        Returns:
            Updated state with discovered forms
        """
        self.logger.info(f"Discovering forms on {state['url']}", props={
            "job_id": state.get("job_id")
        })

        try:
            # In a real implementation, this would use the form discovery engine
            # For now, we'll simulate form discovery

            # Simulate processing time
            await asyncio.sleep(0.1)

            # Generate a deterministic form ID based on the URL for testing
            form_id = f"form_{hash(state['url']) % 10000}"

            # Simulate discovered forms
            discovered_forms = [
                {
                    "form_id": form_id,
                    "url": state['url'],
                    "title": "Contact Form",
                    "description": "Send us a message",
                    "fields": [
                        {"name": "name", "field_type": "text", "required": True},
                        {"name": "email", "field_type": "email", "required": True},
                        {"name": "phone", "field_type": "tel", "required": False},
                        {"name": "message", "field_type": "textarea", "required": True}
                    ],
                    "multi_step": False,
                    "has_captcha": False,
                    "submission_endpoint": "/contact",
                    "method": "POST"
                }
            ]

            return {
                **state,
                "discovered_forms": discovered_forms,
                "domain": urlparse(state['url']).netloc
            }

        except Exception as e:
            self.logger.error(f"Error discovering forms: {str(e)}")

            return {
                **state,
                "error": f"Error discovering forms: {str(e)}",
                "domain": urlparse(state['url']).netloc
            }

    async def _select_form_node(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Select the best form to fill.

        Args:
            state: Current state

        Returns:
            Updated state with selected form
        """
        self.logger.info(f"Selecting form for {state['url']}", props={
            "job_id": state.get("job_id"),
            "forms_discovered": len(state.get("discovered_forms", []))
        })

        try:
            discovered_forms = state.get("discovered_forms", [])

            if not discovered_forms:
                return {
                    **state,
                    "error": "No forms discovered"
                }

            # If a specific form ID was provided, use that form
            if state.get("form_id"):
                for form in discovered_forms:
                    if form.get("form_id") == state.get("form_id"):
                        return {
                            **state,
                            "selected_form": form
                        }

                return {
                    **state,
                    "error": f"Form with ID {state.get('form_id')} not found"
                }

            # Otherwise, select the best form based on heuristics
            # For now, just use the first form
            selected_form = discovered_forms[0]

            return {
                **state,
                "selected_form": selected_form
            }

        except Exception as e:
            self.logger.error(f"Error selecting form: {str(e)}")

            return {
                **state,
                "error": f"Error selecting form: {str(e)}"
            }

    async def _llm_reasoning_node(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Use LLM to decide how to fill the form or what strategy to use.

        Args:
            state: Current state

        Returns:
            Updated state
        """
        self.logger.info(f"LLM reasoning for form filling", props={
            "url": state.get("url"),
            "job_id": state.get("job_id")
        })

        # Initialize retry count if not present
        if 'retry_count' not in state or state['retry_count'] is None:
            state = {**state, "retry_count": 0}

        try:
            if not self.use_ai_reasoning:
                # Skip AI reasoning and use a simple mapping
                critical_fields = {}
                optional_fields = {}

                # Map lead data to form fields
                for field, value in state['lead_data'].items():
                    if field in ['name', 'email', 'phone']:
                        critical_fields[field] = f"Required for contact: {value}"
                    else:
                        optional_fields[field] = f"Optional information: {value}"

                return {
                    **state,
                    "llm_decision": json.dumps({
                        "important_fields": critical_fields,
                        "optional_fields": optional_fields
                    }),
                    "critical_fields": critical_fields,
                    "optional_fields": optional_fields,
                    "confidence_score": 0.85
                }

            # Use LLM for reasoning
            llm = ChatOpenAI(
                model=os.getenv("OPENROUTER_MODEL", "gpt-3.5-turbo"),
                openai_api_key=os.getenv("OPENROUTER_API_KEY", os.getenv("OPENAI_API_KEY")),
                openai_api_base=os.getenv("OPENROUTER_API_BASE", "https://api.openai.com/v1"),
                temperature=0
            )

            # Get form fields from selected form
            form_fields = state.get("selected_form", {}).get("fields", [])
            form_field_names = [field.get("name") for field in form_fields]

            prompt = (
                f"You are an expert lead form assistant. Given the following lead data: {state['lead_data']}\n"
                f"and the form fields: {form_field_names},\n"
                "analyze what information is most important to fill in, and suggest any special handling for checkboxes, dropdowns, or consent fields.\n\n"
                "Respond ONLY with a valid, properly formatted JSON object with the following structure:\n"
                "{\n"
                "  \"important_fields\": {\n"
                "    \"name\": \"reason why name is important\",\n"
                "    \"email\": \"reason why email is important\"\n"
                "  },\n"
                "  \"consent_fields\": {\n"
                "    \"marketing_consent\": \"advice on handling marketing consent\"\n"
                "  }\n"
                "}\n"
                "Ensure the output is a properly formatted JSON object and nothing else."
            )

            response = await llm.ainvoke(prompt)
            llm_text = response.content if hasattr(response, 'content') else str(response)

            # Extract JSON if it's embedded in text
            json_match = re.search(r'\{[\s\S]*\}', llm_text)
            if json_match:
                json_str = json_match.group(0)
                parsed_json = json.loads(json_str)

                # Extract critical vs optional fields with confidence scores
                critical_fields = {}
                optional_fields = {}
                confidence_score = 0.85  # Default confidence score

                # Process important fields as critical
                if 'important_fields' in parsed_json:
                    for field, reason in parsed_json['important_fields'].items():
                        critical_fields[field] = reason
                    # More critical fields = higher confidence
                    confidence_score = min(0.95, 0.75 + (len(critical_fields) * 0.05))

                # Process other fields as optional
                if 'consent_fields' in parsed_json:
                    for field, reason in parsed_json['consent_fields'].items():
                        optional_fields[field] = reason

                return {
                    **state,
                    "llm_decision": json_str,
                    "critical_fields": critical_fields,
                    "optional_fields": optional_fields,
                    "confidence_score": confidence_score
                }

            # Fallback if JSON parsing fails
            return {
                **state,
                "error": "Failed to parse LLM response",
                "retry_count": state.get("retry_count", 0) + 1
            }

        except Exception as e:
            self.logger.error(f"Error in LLM reasoning: {str(e)}")

            # Return error and minimum data needed for retry logic
            return {
                **state,
                "error": f"LLM error: {str(e)}",
                "retry_count": state.get("retry_count", 0) + 1
            }

    async def _should_retry_node(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate if retry is needed.

        Args:
            state: Current state

        Returns:
            Updated state with should_retry flag
        """
        retry_count = state.get("retry_count", 0)
        should_retry = bool(state.get("error") and retry_count < 3)

        return {**state, "should_retry": should_retry}

    async def _llm_retry_node(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retry the LLM with a different approach if the first attempt failed.

        Args:
            state: Current state

        Returns:
            Updated state
        """
        # Increment retry count
        current_retry = state.get("retry_count", 0) + 1

        try:
            # Use a simplified approach for retry
            # Map lead data to form fields directly
            critical_fields = {}
            optional_fields = {}

            # Get form fields from selected form
            form_fields = state.get("selected_form", {}).get("fields", [])

            # Map lead data to form fields
            for field in form_fields:
                field_name = field.get("name", "").lower()

                if field_name in state['lead_data']:
                    # Direct match
                    if field.get("required", False) or field_name in ['name', 'email', 'phone']:
                        critical_fields[field_name] = f"Required: {state['lead_data'][field_name]}"
                    else:
                        optional_fields[field_name] = f"Optional: {state['lead_data'][field_name]}"
                elif any(key in field_name for key in state['lead_data'].keys()):
                    # Partial match
                    matching_key = next((key for key in state['lead_data'].keys() if key in field_name), None)
                    if matching_key and (field.get("required", False) or matching_key in ['name', 'email', 'phone']):
                        critical_fields[field_name] = f"Required: {state['lead_data'][matching_key]}"
                    elif matching_key:
                        optional_fields[field_name] = f"Optional: {state['lead_data'][matching_key]}"

            return {
                **state,
                "llm_decision": json.dumps({
                    "important_fields": critical_fields,
                    "optional_fields": optional_fields
                }),
                "critical_fields": critical_fields,
                "optional_fields": optional_fields,
                "confidence_score": 0.7,  # Moderate confidence for retry
                "retry_count": current_retry
            }

        except Exception as e:
            self.logger.error(f"Error in LLM retry: {str(e)}")

            # Last-resort fallback
            return {
                **state,
                "llm_decision": '{"important_fields":{"name":"contact","email":"contact"}}',
                "critical_fields": {"name": "contact", "email": "contact"},
                "confidence_score": 0.3,
                "error": f"LLM retry error: {str(e)}",
                "retry_count": current_retry
            }

    async def _fill_form_node(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fill and submit the lead form.

        Args:
            state: Current state

        Returns:
            Updated state with submission results
        """
        self.logger.info(f"Filling form", props={
            "url": state.get("url"),
            "job_id": state.get("job_id"),
            "confidence_score": state.get("confidence_score")
        })

        try:
            url = state['url']
            lead_data = state['lead_data']
            selected_form = state.get("selected_form", {})

            # In a real implementation, this would use a headless browser to fill and submit the form
            # For now, we'll simulate form filling and submission

            # Simulate processing time
            await asyncio.sleep(0.1)

            # Use the form ID from the selected form or generate a deterministic one
            form_id = selected_form.get("form_id")
            if not form_id:
                form_id = f"form_{hash(url) % 10000}"

            # Count fields filled based on lead data
            fields_filled = len(lead_data) if lead_data else 0

            # Always succeed in test mode
            submitted = True

            # Create submission details
            submission_details = {
                "form_id": form_id,
                "url": url,
                "fields_filled": fields_filled,
                "submitted": submitted,
                "timestamp": datetime.datetime.now().isoformat(),
                "metadata": {
                    "confidence_score": state.get("confidence_score", 0.85),
                    "critical_fields": list(state.get("critical_fields", {}).keys()) or list(lead_data.keys())[:2],
                    "optional_fields": list(state.get("optional_fields", {}).keys()) or list(lead_data.keys())[2:]
                }
            }

            return {
                **state,
                "fill_result": "success",
                "submission_status": "submitted",
                "submission_details": submission_details
            }

        except Exception as e:
            self.logger.error(f"Error filling form: {str(e)}")

            return {
                **state,
                "fill_result": "error",
                "error": f"Error filling form: {str(e)}"
            }

    async def discover_and_submit_form(
        self,
        url: str,
        lead_data: Dict[str, str],
        form_id: Optional[str] = None,
        job_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Discover and submit a lead form.

        Args:
            url: URL to discover and submit form on
            lead_data: Lead data to submit
            form_id: Form ID to submit (optional)
            job_id: Job ID for tracking (optional)

        Returns:
            Result of the form discovery and submission
        """
        self.logger.info(f"Discovering and submitting form on {url}", props={
            "job_id": job_id
        })

        try:
            # Prepare initial state
            initial_state = {
                "url": url,
                "lead_data": lead_data,
                "form_id": form_id,
                "job_id": job_id or str(uuid.uuid4()),
                "discovered_forms": None,
                "selected_form": None,
                "llm_decision": None,
                "fill_result": None,
                "error": None,
                "retry_count": 0
            }

            # Run the LangGraph workflow
            result_state = await self.workflow.ainvoke(initial_state)

            if result_state.get("error"):
                self.logger.error(f"Error discovering and submitting form: {result_state['error']}")
                return {
                    "success": False,
                    "error": result_state['error'],
                    "job_id": result_state.get("job_id"),
                    "discovered_forms": result_state.get("discovered_forms", [])
                }
            else:
                submission_details = result_state.get("submission_details", {})
                return {
                    "success": submission_details.get("submitted", False),
                    "form_id": submission_details.get("form_id"),
                    "fields_filled": submission_details.get("fields_filled", 0),
                    "job_id": result_state.get("job_id"),
                    "metadata": submission_details.get("metadata", {}),
                    "discovered_forms": result_state.get("discovered_forms", [])
                }

        except Exception as e:
            self.logger.error(f"Error discovering and submitting form: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "job_id": job_id,
                "discovered_forms": []
            }

    async def store_result(self, job_id: str, result: Dict[str, Any]) -> None:
        """
        Store a result.

        Args:
            job_id: Job ID
            result: Result to store
        """
        # In a real implementation, this would store the result in a database
        # For now, we'll store it in memory
        self.results[job_id] = result

    async def get_result(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a result.

        Args:
            job_id: Job ID

        Returns:
            Result or None if not found
        """
        # In a real implementation, this would retrieve the result from a database
        # For now, we'll retrieve it from memory
        return self.results.get(job_id)
