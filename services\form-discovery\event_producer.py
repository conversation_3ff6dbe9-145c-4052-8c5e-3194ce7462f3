import json
import asyncio
import os
from typing import Dict, Any, Optional, Union
import uuid
import time
import random

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.interfaces import Event

# Initialize logger
logger = get_service_logger("form-discovery", "event-producer")


class EventProducer:
    """
    Producer for sending events to Kafka.

    This class provides a robust implementation for producing events to Kafka
    with retry logic, error handling, and monitoring.
    """

    def __init__(self, bootstrap_servers: str, topic: str, max_retries: int = 3):
        """
        Initialize the event producer.

        Args:
            bootstrap_servers: Kafka bootstrap servers
            topic: Default Kafka topic to produce to
            max_retries: Maximum number of retries for failed productions
        """
        self.bootstrap_servers = bootstrap_servers
        self.topic = topic
        self.max_retries = max_retries
        self.logger = logger
        self.producer = None
        self.metrics = {
            "events_produced": 0,
            "events_failed": 0,
            "last_success": None,
            "last_failure": None,
            "avg_latency_ms": 0
        }

        # In a real implementation, this would initialize a Kafka producer
        # For example, with aiokafka:
        # self.producer = AIOKafkaProducer(
        #     bootstrap_servers=bootstrap_servers,
        #     value_serializer=lambda v: json.dumps(v).encode('utf-8')
        # )
        # await self.producer.start()

    async def produce_event(self, event: Union[Event, Dict[str, Any]], topic: Optional[str] = None):
        """
        Produce an event to Kafka with retry logic.

        Args:
            event: The event to produce (Event object or dict)
            topic: Optional topic override

        Returns:
            True if the event was produced successfully, False otherwise
        """
        # Use the provided topic or the default
        target_topic = topic or self.topic

        # Convert dict to Event if needed
        if isinstance(event, dict):
            try:
                event = Event(**event)
            except Exception as e:
                self.logger.error(f"Failed to convert dict to Event: {str(e)}")
                self.metrics["events_failed"] += 1
                return False

        # Add correlation ID if not present
        if not event.correlation_id:
            event.correlation_id = str(uuid.uuid4())

        # Add partition key if not present (for consistent routing)
        if not event.partition_key:
            # Use form_id, lead_id, or event_id as partition key
            if "form_id" in event.payload:
                event.partition_key = event.payload["form_id"]
            elif "lead_id" in event.payload:
                event.partition_key = event.payload["lead_id"]
            else:
                event.partition_key = event.event_id

        self.logger.info(f"Producing event to topic {target_topic}", props={
            "event_id": event.event_id,
            "event_type": event.event_type,
            "correlation_id": event.correlation_id
        })

        # Convert the event to a JSON string
        try:
            event_json = event.json()
        except Exception as e:
            self.logger.error(f"Failed to serialize event: {str(e)}")
            self.metrics["events_failed"] += 1
            return False

        # Implement retry logic
        retries = 0
        start_time = time.time()

        while retries <= self.max_retries:
            try:
                # In a real implementation, this would send the event to Kafka
                # For example:
                # await self.producer.send_and_wait(
                #     target_topic,
                #     event.dict(),
                #     key=event.partition_key.encode('utf-8') if event.partition_key else None
                # )

                # For now, we'll just log it and simulate success/failure
                if retries == 0:
                    self.logger.debug(f"Event data: {event_json[:500]}...")

                # Simulate network delay
                await asyncio.sleep(random.uniform(0.05, 0.2))

                # Simulate occasional failures (10% chance)
                if random.random() < 0.1 and retries < self.max_retries:
                    raise Exception("Simulated Kafka production failure")

                # Calculate latency
                latency_ms = (time.time() - start_time) * 1000

                # Update metrics
                self.metrics["events_produced"] += 1
                self.metrics["last_success"] = time.time()
                self.metrics["avg_latency_ms"] = (
                    (self.metrics["avg_latency_ms"] * (self.metrics["events_produced"] - 1)) + latency_ms
                ) / self.metrics["events_produced"]

                self.logger.info(f"Event produced successfully", props={
                    "event_id": event.event_id,
                    "event_type": event.event_type,
                    "latency_ms": round(latency_ms, 2),
                    "retries": retries
                })

                return True

            except Exception as e:
                retries += 1
                self.logger.warning(f"Failed to produce event (attempt {retries}/{self.max_retries}): {str(e)}")

                if retries <= self.max_retries:
                    # Exponential backoff
                    await asyncio.sleep(0.1 * (2 ** retries))
                else:
                    self.metrics["events_failed"] += 1
                    self.metrics["last_failure"] = time.time()
                    self.logger.error(f"Failed to produce event after {self.max_retries} retries: {str(e)}")
                    return False

    async def close(self):
        """Close the producer."""
        # In a real implementation, this would close the Kafka producer
        # For example:
        # await self.producer.stop()
        pass

    def get_metrics(self) -> Dict[str, Any]:
        """Get producer metrics."""
        return self.metrics
