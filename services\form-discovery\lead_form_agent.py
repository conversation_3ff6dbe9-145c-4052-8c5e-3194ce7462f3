"""
LangGraph-based Lead Form Agent with LLM Reasoning
--------------------------------------------------
- Uses LangGraph for workflow orchestration
- Integrates OpenAI LLM for reasoning
- Includes placeholder for lead form crawling/filling logic
"""
import asyncio
from langgraph.graph import StateGraph, START, END
from langchain_openai import ChatOpenAI
from typing import Dict, Any
import os
from dotenv import load_dotenv
import json
import re

load_dotenv()

# --- State Schema ---
from typing import TypedDict, Optional

class LeadFormState(TypedDict):
    """State for the lead form workflow."""
    url: str
    lead_data: Dict[str, Any]
    llm_decision: Optional[str]
    critical_fields: Optional[Dict[str, str]]
    optional_fields: Optional[Dict[str, str]]
    confidence_score: Optional[float]
    fill_result: Optional[str]
    field_coverage: Optional[Dict[str, Any]]
    error: Optional[str]
    retry_count: Optional[int]
    domain: Optional[str]

# --- Node: LLM Reasoning ---
async def llm_reasoning_node(state: LeadFormState) -> Dict[str, Any]:
    # print('[DEBUG] Entered llm_reasoning_node with state:', state)
    """Use LLM to decide how to fill the form or what strategy to use."""
    # Extract domain from URL for domain-specific logging
    from urllib.parse import urlparse
    parsed_url = urlparse(state['url'])
    domain = parsed_url.netloc
    state_update = {"domain": domain}
    
    # Initialize retry count if not present
    if 'retry_count' not in state or state['retry_count'] is None:
        state_update["retry_count"] = 0
    
    llm = ChatOpenAI(
        model=os.getenv("OPENROUTER_MODEL"),
        openai_api_key=os.getenv("OPENROUTER_API_KEY"),
        openai_api_base="https://openrouter.ai/api/v1",
        temperature=0
    )
    prompt = (
        f"You are an expert lead form assistant. Given the following lead data: {state['lead_data']}\n"
        f"and the target website: {state['url']}, "
        "analyze what information is most important to fill in, and suggest any special handling for checkboxes, dropdowns, or consent fields.\n\n"
        "Respond ONLY with a valid, properly formatted JSON object with the following structure:\n"
        "{{\n"
        "  \"important_fields\": {{\n"
        "    \"name\": \"reason why name is important\",\n"
        "    \"email\": \"reason why email is important\"\n"
        "  }},\n"
        "  \"consent_fields\": {{\n"
        "    \"marketing_consent\": \"advice on handling marketing consent\"\n"
        "  }}\n"
        "}}\n"
        "Ensure the output is a properly formatted JSON object and nothing else."
    )
    try:
        response = await llm.ainvoke(prompt)
        llm_text = response.content if hasattr(response, 'content') else str(response)
        
        # Extract JSON if it's embedded in text
        # Look for JSON pattern
        json_match = re.search(r'\{[\s\S]*\}', llm_text)
        if json_match:
            json_str = json_match.group(0)
            try:
                # Validate JSON by parsing it
                parsed_json = json.loads(json_str)
                
                # Extract critical vs optional fields with confidence scores
                critical_fields = {}
                optional_fields = {}
                confidence_score = 0.85  # Default confidence score
                
                # Process important fields as critical
                if 'important_fields' in parsed_json:
                    for field, reason in parsed_json['important_fields'].items():
                        critical_fields[field] = reason
                    # More critical fields = higher confidence
                    confidence_score = min(0.95, 0.75 + (len(critical_fields) * 0.05))
                
                # Process other fields as optional
                if 'consent_fields' in parsed_json:
                    for field, reason in parsed_json['consent_fields'].items():
                        optional_fields[field] = reason
                
                # Use the validated JSON string with additional metadata
                result = {
                    **state,
                    "llm_decision": json_str,
                    "critical_fields": critical_fields,
                    "optional_fields": optional_fields,
                    "confidence_score": confidence_score
                }
                assert 'url' in result and 'lead_data' in result, f"Returned state missing required keys: {result}"
                # print('[DEBUG] llm_reasoning_node returning:', result)
                for k, v in result.items():
                    # print(f'[DEBUG] result key: {k}, type: {type(v)}')
                    pass
                return result
            except json.JSONDecodeError:
                # If parsing fails, use a fallback structure
                fallback_json = {
                    "important_fields": {
                        "name": "Primary contact information",
                        "email": "Primary contact method",
                        "phone": "Alternative contact method",
                        "message": "Details about the request"
                    },
                    "consent_fields": {
                        "marketing_consent": "Recommended to check for standard communications"
                    }
                }
                # Lower confidence for fallback
                result = {
                    **state,
                    "llm_decision": json.dumps(fallback_json),
                    "critical_fields": {
                        "name": "Primary contact information",
                        "email": "Primary contact method",
                        "phone": "Alternative contact method"
                    },
                    "optional_fields": {
                        "message": "Details about the request",
                        "marketing_consent": "Standard communications"
                    },
                    "confidence_score": 0.6  # Lower confidence for fallback
                }
                assert 'url' in result and 'lead_data' in result, f"Returned state missing required keys: {result}"
                # print('[DEBUG] llm_reasoning_node returning (fallback):', result)
                for k, v in result.items():
                    # print(f'[DEBUG] result key: {k}, type: {type(v)}')
                    pass
                return result
        else:
            # No JSON found, use fallback
            fallback_json = {
                "important_fields": {
                    "name": "Primary contact information",
                    "email": "Primary contact method",
                    "phone": "Alternative contact method",
                    "message": "Details about the request"
                },
                "consent_fields": {
                    "marketing_consent": "Recommended to check for standard communications"
                }
            }
            result = {
                **state,
                "llm_decision": json.dumps(fallback_json),
                "critical_fields": {
                    "name": "Primary contact information",
                    "email": "Primary contact method",
                    "phone": "Alternative contact method"
                },
                "optional_fields": {
                    "message": "Details about the request",
                    "marketing_consent": "Standard communications"
                },
                "confidence_score": 0.5  # Lower confidence for fallback
            }
            # print('[DEBUG] llm_reasoning_node returning (no JSON found):', result)
            return result
    except Exception as e:
        fallback_json = {
            "important_fields": {
                "name": "Primary contact information",
                "email": "Primary contact method",
                "phone": "Alternative contact method",
                "message": "Details about the request"
            }
        }
        # Return error and minimum data needed for retry logic
        result = {
            **state,
            "llm_decision": json.dumps(fallback_json),
            "critical_fields": {
                "name": "Primary contact information",
                "email": "Primary contact method"
            },
            "optional_fields": {
                "phone": "Alternative contact method",
                "message": "Details about the request"
            },
            "confidence_score": 0.3,  # Very low confidence
            "error": f"LLM error: {e}",
            "retry_count": state.get("retry_count", 0) + 1  # Increment retry count
        }
        # print('[DEBUG] llm_reasoning_node returning (exception):', result)
        return result

# --- Node: Evaluate if retry needed ---
async def should_retry_node(state: LeadFormState) -> Dict[str, Any]:
    # print('[DEBUG] Entered should_retry_node with state:', state)
    retry_count = state.get("retry_count", 0)
    should_retry = bool(state.get("error") and retry_count < 3)
    # print(f'[DEBUG] should_retry_node returning: {{"should_retry": {should_retry}}}')
    return {**state, "should_retry": should_retry}


# --- Node: LLM Retry Logic ---
async def llm_retry_node(state: LeadFormState) -> Dict[str, Any]:
    # print('[DEBUG] Entered llm_retry_node with state:', state)
    """Retry the LLM with a different approach if the first attempt failed."""
    # Increment retry count
    current_retry = state.get("retry_count", 0) + 1
    
    # Try a more structured prompt on retry
    llm = ChatOpenAI(
        model=os.getenv("OPENROUTER_MODEL"),
        openai_api_key=os.getenv("OPENROUTER_API_KEY"),
        openai_api_base="https://openrouter.ai/api/v1",
        temperature=0
    )
    retry_prompt = (
        f"RETRY ATTEMPT #{current_retry}: You are an expert lead form assistant. "
        f"Given the following lead data: {state['lead_data']}\n"
        f"and the target website: {state['url']}, "
        "provide a SIMPLIFIED analysis of what information should be filled in the form.\n\n"
        "Respond with ONLY this exact JSON format, no other text:\n"
        "{{\n"
        "  \"important_fields\": {{\n"
        "    \"name\": \"required for contact\",\n"
        "    \"email\": \"required for contact\"\n"
        "  }},\n"
        "  \"consent_fields\": {{\n"
        "    \"marketing_consent\": \"typically needed\"\n"
        "  }}\n"
        "}}\n"
    )
    
    try:
        response = await llm.ainvoke(retry_prompt)
        llm_text = response.content if hasattr(response, 'content') else str(response)
        
        # Extract and process JSON
        json_match = re.search(r'\{[\s\S]*\}', llm_text)
        if json_match:
            json_str = json_match.group(0)
            try:
                parsed_json = json.loads(json_str)
                
                # Extract fields with simplified approach
                critical_fields = {}
                optional_fields = {}
                
                if 'important_fields' in parsed_json:
                    critical_fields = parsed_json['important_fields']
                if 'consent_fields' in parsed_json:
                    optional_fields = parsed_json['consent_fields']
                
                result = {
                    "llm_decision": json_str,
                    "critical_fields": critical_fields,
                    "optional_fields": optional_fields,
                    "confidence_score": 0.7,  # Moderate confidence for retry
                    "retry_count": current_retry
                }
                # print('[DEBUG] llm_retry_node returning:', result)
                return result
            except json.JSONDecodeError:
                # Ultra-minimal fallback
                result = {
                    "llm_decision": '{"important_fields":{"name":"contact","email":"contact"}}',
                    "critical_fields": {"name": "contact", "email": "contact"},
                    "confidence_score": 0.4,
                    "retry_count": current_retry
                }
                # print('[DEBUG] llm_retry_node returning (exception):', result)
                return result
        else:
            # Ultra-minimal fallback
            result = {
                "llm_decision": '{"important_fields":{"name":"contact","email":"contact"}}',
                "critical_fields": {"name": "contact", "email": "contact"},
                "confidence_score": 0.4,
                "retry_count": current_retry
            }
    except Exception as e:
        # Last-resort fallback
        return {
            "llm_decision": '{"important_fields":{"name":"contact","email":"contact"}}',
            "critical_fields": {"name": "contact", "email": "contact"},
            "confidence_score": 0.3,
            "error": f"LLM retry error: {e}",
            "retry_count": current_retry
        }

from fill_lead_form import fill_lead_form_node

# --- Build the workflow graph ---
graph = StateGraph(LeadFormState)
graph.add_edge(START, "llm_reasoning")

# Add all nodes to the workflow
graph.add_node("llm_reasoning", llm_reasoning_node)
graph.add_node("should_retry", should_retry_node)
graph.add_node("llm_retry", llm_retry_node)
graph.add_node("fill_form", fill_lead_form_node)

# Define the edges for the workflow with retry logic
graph.add_edge("llm_reasoning", "should_retry")

# Ensure no stale/duplicate edges for 'should_retry'
if hasattr(graph, 'remove_edges_from'):
    graph.remove_edges_from([e for e in getattr(graph, 'edges', []) if e[0] == "should_retry"])
# Branch based on should_retry decision
graph.add_conditional_edges(
    "should_retry",
    lambda state: state.get("should_retry", False),
    {
        True: "llm_retry",
        False: "fill_form"
    }
)

# From llm_retry back to fill_form
graph.add_edge("llm_retry", "fill_form")

# Complete the workflow
graph.add_edge("fill_form", END)

# Handle terminal errors by ending the workflow


# print('[DEBUG] Registered nodes:', graph.nodes.keys())
# print('[DEBUG] Registered edges:', graph.edges)
for attr in dir(graph):
    if attr.startswith('edge') or attr.startswith('transition'):
        # print(f'[DEBUG] graph.{attr}:', getattr(graph, attr))
        pass
app = graph.compile()

pass

# --- Entrypoint for running the agent ---
async def run_lead_form_agent_async(url: str, lead_data: dict):
    initial_state = {"url": url, "lead_data": lead_data, "llm_decision": None, "fill_result": None, "error": None}
    result_state = await app.ainvoke(initial_state)
    if result_state.get("error"):
        # print(f"[ERROR] {result_state['error']}")
        pass
    else:
        pass

def run_lead_form_agent(url: str, lead_data: dict):
    """Synchronous wrapper for the async function"""
    asyncio.run(run_lead_form_agent_async(url, lead_data))

# Function for external import
async def run_lead_form_agent_async(url: str, lead_data: dict):
    """Async function to run the lead form agent workflow with the given URL and lead data.
    
    Args:
        url: The URL of the website with the lead form
        lead_data: Dictionary containing the lead information to fill in the form
        
    Returns:
        The final state after workflow completion
    """
    initial_state = {"url": url, "lead_data": lead_data, "llm_decision": None, "fill_result": None, "error": None}
    result_state = await app.ainvoke(initial_state)
    return result_state

# Note: run_lead_form_agent is already defined above

if __name__ == "__main__":

    url = "http://www.hahnroofing.com"
    lead_data = {
        "name": "John Smith",
        "email": "<EMAIL>",
        "phone": "************",
        "message": "Hello, I am interested in learning more about your restoration services."
    }
    run_lead_form_agent(url, lead_data)
