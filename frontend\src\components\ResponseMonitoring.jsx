import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  TablePagination,
  Chip,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Tooltip,
  CircularProgress,
  Card,
  CardContent,
  Grid
} from '@mui/material';
import { 
  Search as SearchIcon,
  Refresh as RefreshIcon,
  AccessTime as AccessTimeIcon,
  Phone as PhoneIcon, 
  Email as EmailIcon, 
  Sms as SmsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Grade as GradeIcon,
  FilterList as FilterListIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';

// Mock data for leads with completed audits
const mockLeadsData = Array.from({ length: 100 }, (_, index) => {
  const channels = ['phone', 'sms', 'email', 'NO_RESPONSE'];
  const grades = ['A+', 'A', 'B', 'C', 'D', 'F'];
  const companies = [
    'Acme Corp', 'TechSolutions Inc', 'Global Industries', 'Summit Partners', 
    'Horizon Services', 'Elite Consulting', 'Premier Builders', 'Innovative Systems',
    'Reliable Contractors', 'Advanced Solutions'
  ];
  
  const randomChannel = channels[Math.floor(Math.random() * channels.length)];
  const randomGrade = grades[Math.floor(Math.random() * grades.length)];
  const randomCompany = companies[Math.floor(Math.random() * companies.length)];
  
  // Generate a date within the last 30 days
  const date = new Date();
  date.setDate(date.getDate() - Math.floor(Math.random() * 30));
  
  // Generate a score based on the grade
  let score;
  switch (randomGrade) {
    case 'A+': score = Math.floor(Math.random() * (125 - 115 + 1)) + 115; break;
    case 'A': score = Math.floor(Math.random() * (114 - 100 + 1)) + 100; break;
    case 'B': score = Math.floor(Math.random() * (99 - 80 + 1)) + 80; break;
    case 'C': score = Math.floor(Math.random() * (79 - 60 + 1)) + 60; break;
    case 'D': score = Math.floor(Math.random() * (59 - 30 + 1)) + 30; break;
    case 'F': score = Math.floor(Math.random() * 29) + 1; break;
    default: score = 0;
  }
  
  return {
    id: `lead-${index + 1}`,
    company: randomCompany,
    website: `https://www.${randomCompany.toLowerCase().replace(/\s+/g, '')}.com`,
    contactName: `Contact ${index + 1}`,
    email: `contact${index + 1}@${randomCompany.toLowerCase().replace(/\s+/g, '')}.com`,
    phone: `(${Math.floor(Math.random() * 900) + 100}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
    submissionDate: date.toISOString(),
    completionDate: new Date(date.getTime() + (48 * 60 * 60 * 1000)).toISOString(),
    bestChannel: randomChannel,
    grade: randomGrade,
    score: score,
    responseRate: Math.random(),
    avgResponseTime: Math.floor(Math.random() * 3600) + 60, // Between 1 minute and 1 hour in seconds
    status: 'Completed'
  };
});

// Helper function to get color based on grade
const getGradeColor = (grade) => {
  switch (grade) {
    case 'A+': return '#4caf50'; // Green
    case 'A': return '#8bc34a'; // Light Green
    case 'B': return '#ffeb3b'; // Yellow
    case 'C': return '#ff9800'; // Orange
    case 'D': return '#f44336'; // Red
    case 'F': return '#9e9e9e'; // Grey
    default: return '#9e9e9e'; // Grey
  }
};

// Helper function to get icon for channel
const getChannelIcon = (channel) => {
  switch (channel) {
    case 'phone': return <PhoneIcon />;
    case 'sms': return <SmsIcon />;
    case 'email': return <EmailIcon />;
    case 'NO_RESPONSE': return <CancelIcon />;
    default: return null;
  }
};

// Helper function to format date
const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
  return new Date(dateString).toLocaleDateString(undefined, options);
};

// Helper function to format time in seconds to human-readable format
const formatTime = (seconds) => {
  if (seconds < 60) return `${seconds}s`;
  if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
  return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
};

const ResponseMonitoring = () => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [leads, setLeads] = useState(mockLeadsData);
  
  // Summary stats
  const totalLeads = leads.length;
  const gradeDistribution = leads.reduce((acc, lead) => {
    acc[lead.grade] = (acc[lead.grade] || 0) + 1;
    return acc;
  }, {});
  
  const avgScore = leads.reduce((sum, lead) => sum + lead.score, 0) / totalLeads;
  
  // Filter leads based on search term
  const filteredLeads = leads.filter(lead => 
    lead.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lead.contactName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lead.website.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };
  
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };
  
  const handleRefresh = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLeads(mockLeadsData);
      setLoading(false);
    }, 1000);
  };
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Response Monitoring Dashboard
      </Typography>
      
      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Total Audits</Typography>
              <Typography variant="h3">{totalLeads}</Typography>
              <Typography variant="body2" color="text.secondary">Completed in the last 30 days</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Average Score</Typography>
              <Typography variant="h3">{avgScore.toFixed(1)}</Typography>
              <Typography variant="body2" color="text.secondary">Across all audited companies</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Grade Distribution</Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 1 }}>
                {['A+', 'A', 'B', 'C', 'D', 'F'].map(grade => (
                  <Box key={grade} sx={{ textAlign: 'center' }}>
                    <Typography 
                      variant="h5" 
                      sx={{ 
                        color: getGradeColor(grade),
                        fontWeight: 'bold'
                      }}
                    >
                      {gradeDistribution[grade] || 0}
                    </Typography>
                    <Typography variant="body2">{grade}</Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Search and Filter Bar */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <TextField
          placeholder="Search companies, contacts, or websites..."
          variant="outlined"
          size="small"
          value={searchTerm}
          onChange={handleSearch}
          sx={{ width: 350 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            )
          }}
        />
        
        <Box>
          <Tooltip title="Refresh data">
            <IconButton onClick={handleRefresh} disabled={loading}>
              {loading ? <CircularProgress size={24} /> : <RefreshIcon />}
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Filter results">
            <IconButton>
              <FilterListIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Export to CSV">
            <IconButton>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
      
      {/* Leads Table */}
      <TableContainer component={Paper} sx={{ mb: 2 }}>
        <Table sx={{ minWidth: 650 }} size="small">
          <TableHead>
            <TableRow sx={{ bgcolor: '#f5f5f5' }}>
              <TableCell><Typography variant="subtitle2">Company</Typography></TableCell>
              <TableCell><Typography variant="subtitle2">Website</Typography></TableCell>
              <TableCell><Typography variant="subtitle2">Contact</Typography></TableCell>
              <TableCell><Typography variant="subtitle2">Submission Date</Typography></TableCell>
              <TableCell><Typography variant="subtitle2">Best Channel</Typography></TableCell>
              <TableCell><Typography variant="subtitle2">Grade</Typography></TableCell>
              <TableCell><Typography variant="subtitle2">Score</Typography></TableCell>
              <TableCell><Typography variant="subtitle2">Avg. Response</Typography></TableCell>
              <TableCell><Typography variant="subtitle2">Actions</Typography></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredLeads
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((lead) => (
                <TableRow key={lead.id} hover>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      {lead.company}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ color: 'primary.main' }}>
                      {lead.website.replace('https://www.', '')}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">{lead.contactName}</Typography>
                    <Typography variant="caption" color="text.secondary">{lead.email}</Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">{formatDate(lead.submissionDate)}</Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {getChannelIcon(lead.bestChannel)}
                      <Typography variant="body2" sx={{ ml: 1, textTransform: 'capitalize' }}>
                        {lead.bestChannel === 'NO_RESPONSE' ? 'None' : lead.bestChannel}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={lead.grade} 
                      size="small"
                      sx={{ 
                        bgcolor: getGradeColor(lead.grade),
                        color: 'white',
                        fontWeight: 'bold',
                        minWidth: 40
                      }} 
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">{lead.score}</Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <AccessTimeIcon 
                        fontSize="small" 
                        sx={{ 
                          mr: 0.5,
                          color: lead.avgResponseTime < 300 ? 'success.main' : 
                                 lead.avgResponseTime < 1800 ? 'warning.main' : 'error.main'
                        }} 
                      />
                      <Typography variant="body2">
                        {formatTime(lead.avgResponseTime)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Tooltip title="View Audit Report">
                      <IconButton 
                        component={Link} 
                        to={`/audit-report?id=${lead.id}`}
                        size="small"
                      >
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* Pagination */}
      <TablePagination
        rowsPerPageOptions={[10, 25, 50, 100]}
        component="div"
        count={filteredLeads.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Box>
  );
};

export default ResponseMonitoring;
