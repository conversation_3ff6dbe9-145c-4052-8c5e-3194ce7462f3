from typing import Dict, Any, List, Tuple, Optional
from bs4 import BeautifulSoup
from bs4.element import Tag
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig
import datetime
import os
import re
import urllib.parse


# Helper function to determine if a form is likely a lead form
def is_likely_lead_form(form: Tag) -> bool:
    """Analyze a form to determine if it's likely a lead generation form."""
    # Check form attributes
    form_id = form.get('id', '').lower()
    form_class = ' '.join(form.get('class', [])) if isinstance(form.get('class'), list) else str(form.get('class', ''))
    form_action = form.get('action', '').lower()
    form_method = form.get('method', '').lower()
    
    # Count different types of fields
    inputs = form.find_all('input')
    textareas = form.find_all('textarea')
    selects = form.find_all('select')
    
    # Check for text inputs - exclude hidden, checkbox, radio
    text_inputs = [inp for inp in inputs if inp.get('type', 'text').lower() in ['text', 'email', 'tel', 'number']]
    
    # Check for common lead form field patterns
    has_name_field = any(inp.get('name', '').lower() in ['name', 'firstname', 'first-name', 'fullname', 'full-name'] or 
                      inp.get('id', '').lower() in ['name', 'firstname', 'first-name', 'fullname', 'full-name'] or
                      inp.get('placeholder', '').lower() in ['name', 'your name', 'full name'] for inp in inputs)
    
    has_email_field = any(inp.get('name', '').lower() in ['email', 'e-mail', 'emailaddress', 'email-address'] or 
                       inp.get('id', '').lower() in ['email', 'e-mail', 'emailaddress'] or
                       inp.get('placeholder', '').lower() in ['email', 'your email', 'email address'] or
                       inp.get('type', '').lower() == 'email' for inp in inputs)
    
    has_phone_field = any(inp.get('name', '').lower() in ['phone', 'phonenumber', 'phone-number', 'tel', 'telephone'] or 
                       inp.get('id', '').lower() in ['phone', 'phonenumber', 'tel'] or
                       inp.get('placeholder', '').lower() in ['phone', 'your phone', 'phone number'] or
                       inp.get('type', '').lower() == 'tel' for inp in inputs)
    
    # Text context might indicate lead form
    form_text = form.get_text().lower()
    lead_context_terms = ['contact', 'quote', 'estimate', 'consultation', 'free', 'get in touch', 'message', 'send']
    context_match = any(term in form_text for term in lead_context_terms)
    
    # Look for submit buttons with common lead form text
    submit_buttons = form.find_all(['button', 'input[type="submit"]'])
    submit_texts = []
    for button in submit_buttons:
        if button.name == 'button':
            submit_texts.append(button.get_text().strip().lower())
        else:  # input[type="submit"]
            submit_texts.append(button.get('value', '').lower())
    
    lead_button_terms = ['submit', 'send', 'contact', 'quote', 'estimate', 'free', 'get', 'request']
    button_match = any(any(term in btn_text for term in lead_button_terms) for btn_text in submit_texts)
    
    # Set of rules to determine if it's a lead form
    is_lead_form = False
    
    # Rule 1: If it has name AND email fields, it's likely a lead form
    if has_name_field and has_email_field:
        is_lead_form = True
    
    # Rule 2: If it has a context match and at least one contact field
    elif context_match and (has_name_field or has_email_field or has_phone_field):
        is_lead_form = True
    
    # Rule 3: If it has multiple text inputs and button with lead-related text
    elif len(text_inputs) >= 2 and button_match:
        is_lead_form = True
    
    # Rule 4: Check form attributes for lead form indicators
    elif ('contact' in form_id or 'lead' in form_id or 'quote' in form_id or 
          'contact' in form_class or 'lead' in form_class or 'quote' in form_class or
          'contact' in form_action or 'lead' in form_action or 'quote' in form_action):
        is_lead_form = True
    
    # Rule 5: If it's a single-field form, it's likely not a lead form (e.g., search)
    if len(text_inputs) <= 1 and len(form.find_all('*')) < 5 and not has_email_field:
        is_lead_form = False
    
    return is_lead_form

# Helper to handle zip code gates
async def handle_zip_gate(crawler: AsyncWebCrawler, soup: BeautifulSoup, lead_data: Dict[str, Any]) -> bool:
    """Check for and handle zip code gates or other single-field initial forms."""
    # Look for simple forms with just 1-2 fields
    simple_forms = []
    for form in soup.find_all('form'):
        inputs = form.find_all('input')
        visible_inputs = [inp for inp in inputs if inp.get('type', '').lower() not in ['hidden', 'submit', 'button']]
        
        # Look for zip code fields specifically
        zip_inputs = [inp for inp in inputs if any(zip_term in (inp.get('name', '') + inp.get('id', '') + inp.get('placeholder', '')).lower() 
                                             for zip_term in ['zip', 'postal', 'postalcode', 'zipcode'])]
        
        if (len(visible_inputs) <= 2 and zip_inputs) or (len(visible_inputs) == 1 and len(inputs) <= 3):
            simple_forms.append((form, zip_inputs or visible_inputs))
    
    if not simple_forms:
        return False
    
    # Try to handle the first simple form found
    form, fields = simple_forms[0]
    try:
        # Generate JS to fill the form
        js_lines = []
        
        # Fill zip field if found
        zip_code = lead_data.get('zip', '')
        if not zip_code and 'phone' in lead_data:
            # Default to a common zip code if none provided
            zip_code = '90210'  
        
        # Fill the field with appropriate data
        field = fields[0]
        field_name = field.get('name')
        field_type = field.get('type', 'text').lower()
        field_id = field.get('id')
        
        if field_name and 'zip' in field_name.lower() and zip_code:
            js_lines.append(f"document.getElementsByName('{field_name}')[0].value = '{zip_code}';")
        elif field_name:
            # For other fields, try to use zip but fall back to phone prefix if needed
            value = zip_code or lead_data.get('phone', '')[:5] or '90210'
            js_lines.append(f"document.getElementsByName('{field_name}')[0].value = '{value}';")
        elif field_id:
            value = zip_code or lead_data.get('phone', '')[:5] or '90210'
            js_lines.append(f"document.getElementById('{field_id}').value = '{value}';")
        
        # Find and click submit button
        js_lines.append("""
        // Find and click the submit button
        const submitButtons = Array.from(document.querySelectorAll('button, input[type="submit"], a.btn, .button'));
        const button = submitButtons.find(btn => {
            const text = (btn.textContent || btn.value || '').toLowerCase();
            return text.includes('submit') || text.includes('go') || text.includes('next') || 
                   text.includes('continue') || text.includes('get') || text.includes('start');
        });
        if (button) button.click();
        else if (document.forms[0]) document.forms[0].submit();
        """)
        
        # Execute the JS
        # print(f"[ZIP GATE] Attempting to submit zip code gate with {zip_code or 'default value'}")
        run_cfg = CrawlerRunConfig(js_code='\n'.join(js_lines), wait_for=None)
        await crawler.arun(config=run_cfg)
        return True
    except Exception as e:
        # print(f"[ZIP GATE] Error handling zip gate: {e}")
        return False

# Helper to click buttons that might reveal forms
async def reveal_hidden_forms(crawler: AsyncWebCrawler, soup: BeautifulSoup) -> bool:
    """Look for and click buttons that might reveal contact/lead forms."""
    # Look for buttons/links that might open contact forms
    contact_buttons = []
    
    # Button and anchor elements
    for elem in soup.find_all(['button', 'a', 'div', 'span']):
        elem_text = elem.get_text().strip().lower()
        elem_class = ' '.join(elem.get('class', [])) if isinstance(elem.get('class'), list) else str(elem.get('class', ''))
        elem_id = elem.get('id', '').lower()
        elem_href = elem.get('href', '').lower() if elem.name == 'a' else ''
        
        # Check for contact/quote related text and classes
        contact_terms = ['contact', 'quote', 'free estimate', 'get started', 'get in touch', 'request', 'form']
        
        is_contact_elem = any(term in elem_text for term in contact_terms) or \
                         any(term in elem_class for term in contact_terms) or \
                         any(term in elem_id for term in contact_terms) or \
                         any(term in elem_href for term in contact_terms)
        
        if is_contact_elem:
            contact_buttons.append(elem)
    
    if not contact_buttons:
        return False
    
    # Try to interact with the first contact button
    try:
        # Get the element selector (prefer ID, then class, then create an XPath)
        button = contact_buttons[0]
        button_id = button.get('id')
        button_classes = button.get('class', [])
        button_text = button.get_text().strip()
        
        js_code = """
        // Find and click elements that might reveal forms
        function findAndClickElement() {        
            let found = false;
        """    
        
        if button_id:
            js_code += f"""    
            // Try by ID
            const elemById = document.getElementById('{button_id}');
            if (elemById) {{
                elemById.click();
                found = true;
                console.log('[REVEAL] Clicked element by ID: {button_id}');
            }}
            """
        
        if button_classes:
            class_selector = f".{'.'.join(button_classes)}" if isinstance(button_classes, list) else f".{button_classes}"
            js_code += f"""
            // Try by class
            if (!found) {{
                const elemsByClass = document.querySelectorAll('{class_selector}');
                if (elemsByClass.length > 0) {{
                    elemsByClass[0].click();
                    found = true;
                    console.log('[REVEAL] Clicked element by class: {class_selector}');
                }}
            }}
            """
        
        if button_text:
            js_code += f"""
            // Try by text content
            if (!found) {{
                const allElements = document.querySelectorAll('button, a, div, span');
                for (const elem of allElements) {{
                    if (elem.textContent && elem.textContent.trim().includes('{button_text}')) {{
                        elem.click();
                        found = true;
                        console.log('[REVEAL] Clicked element by text: {button_text}');
                        break;
                    }}
                }}
            }}
            """
        
        js_code += """
            // Try all contact/quote related elements as fallback
            if (!found) {                
                const contactTerms = ['contact', 'quote', 'estimate', 'get started', 'get in touch'];
                const allElements = document.querySelectorAll('button, a, div.button, span.button, .btn');
                
                for (const elem of allElements) {
                    const text = elem.textContent ? elem.textContent.toLowerCase() : '';
                    const id = elem.id ? elem.id.toLowerCase() : '';
                    const className = elem.className ? elem.className.toLowerCase() : '';
                    
                    const hasContactTerm = contactTerms.some(term => 
                        text.includes(term) || id.includes(term) || className.includes(term)
                    );
                    
                    if (hasContactTerm) {
                        elem.click();
                        found = true;
                        console.log('[REVEAL] Clicked contact element as fallback');
                        break;
                    }
                }
            }
            
            return found;
        }
        
        // Attempt to find and click
        const result = findAndClickElement();
        console.log('[REVEAL] Click result:', result);
        """
        
        # print(f"[REVEAL] Attempting to click element to reveal hidden forms")
        run_cfg = CrawlerRunConfig(js_code=js_code, wait_for=None)
        await crawler.arun(config=run_cfg)
        # Wait a moment for any modals/popups to appear
        await crawler.arun(config=CrawlerRunConfig(js_code="setTimeout(() => {}, 1500);", wait_for=None))
        return True
    except Exception as e:
        # print(f"[REVEAL] Error trying to reveal hidden forms: {e}")
        return False

# Helper to find contact or quote pages to navigate to
def find_contact_or_quote_page(soup: BeautifulSoup, current_url: str) -> Optional[str]:
    """Find contact or quote pages in the navigation that we should visit."""
    contact_links = []
    
    # Find all links
    for link in soup.find_all('a'):
        href = link.get('href')
        if not href:
            continue
            
        # Convert relative URLs to absolute
        if href.startswith('/'):
            # Extract base URL to build absolute URL
            parsed_url = urllib.parse.urlparse(current_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            href = f"{base_url}{href}"
        elif not href.startswith(('http://', 'https://')):
            # Handle relative URLs without leading slash
            if current_url.endswith('/'):
                href = f"{current_url}{href}"
            else:
                href = f"{current_url}/{href}"
        
        # Skip anchors, javascript, mailto, etc.
        if href.startswith(('#', 'javascript:', 'mailto:', 'tel:')):
            continue
            
        # Skip external domains
        parsed_current = urllib.parse.urlparse(current_url)
        parsed_href = urllib.parse.urlparse(href)
        if parsed_current.netloc != parsed_href.netloc:
            continue
        
        # Check the link text and URL for contact/quote related terms
        link_text = link.get_text().strip().lower()
        contact_terms = ['contact', 'get in touch', 'reach out', 'talk to us',
                        'quote', 'estimate', 'free quote', 'get quote',
                        'consultation', 'appointment', 'schedule', 'book now']
        
        url_path = parsed_href.path.lower()
        
        if any(term in link_text for term in contact_terms) or \
           any(term in url_path for term in ['contact', 'quote', 'estimate', 'form']):
            contact_links.append((href, link_text))
    
    # Sort links by relevance
    scored_links = []
    for href, text in contact_links:
        score = 0
        if 'contact' in text or 'contact' in href:
            score += 10
        if 'quote' in text or 'quote' in href or 'estimate' in text or 'estimate' in href:
            score += 8
        if 'form' in href:
            score += 5
        if 'free' in text:
            score += 3
        scored_links.append((href, score))
    
    # Return the highest scored link or None
    if scored_links:
        scored_links.sort(key=lambda x: x[1], reverse=True)
        return scored_links[0][0]
    
    return None

# --- Node: Fill and Submit Lead Form ---
async def fill_lead_form_node(state: Dict[str, Any]) -> Dict[str, Any]:
    try:
        import traceback
        # print('[DEBUG] Entered fill_lead_form_node')
        url = state['url']
        # Create a random ID for the job
        import uuid
        job_id = str(uuid.uuid4())[:8]
        # print('[DEBUG] Entered fill_lead_form_node')
        # Prepare the audit directories and output paths
        audit_dir = os.path.join(os.path.dirname(os.path.realpath(__file__)), 'audit')
        os.makedirs(audit_dir, exist_ok=True)



        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")




        submission_details = {}
        
        # Configure browser with validation
        try:
            # print("Before browser configuration")
            browser_cfg = BrowserConfig(browser_type="firefox", headless=True, verbose=True)
            # print("After browser configuration")
            # Add validation to make sure browser configuration is valid
            # This is important for catching configuration issues early
            if not hasattr(browser_cfg, 'browser_type') or browser_cfg.browser_type not in ["firefox", "chrome", "edge"]:
                return {**state, "fill_result": "error", "error": "Invalid browser type configuration"}
            
            # You could add additional validation for browser executable paths if needed
            # For example, if browser_cfg has a property for binary_path:
            # if hasattr(browser_cfg, 'binary_path') and not os.path.exists(browser_cfg.binary_path):
            #     return {**state, "fill_result": "error", "error": f"Browser binary not found at {browser_cfg.binary_path}"}
        except Exception as browser_error:
            return {**state, "fill_result": "error", "error": f"Browser setup failed: {str(browser_error)}"}
        
        lead_data = state['lead_data']
        
        # Track visited URLs to avoid loops
        visited_urls = set([url])
        navigation_path = [url]
        max_navigation_depth = 3  # Maximum number of pages to navigate through
        
        input_name_map = {
            "name": ["name", "full_name", "fullname", "fname", "firstname", "first_name"],
            "email": ["email", "e-mail", "emailaddress", "email_address"],
            "phone": ["phone", "tel", "telephone", "phonenumber", "phone_number"],
            "message": ["message", "comments", "description", "body", "content", "offername", "setappointment", "consentmessage"],
            "address": ["address", "street", "addr", "location"],
            "city": ["city", "town"],
            "state": ["state", "province"],
            "zip": ["zip", "zipcode", "postal", "postalcode"],
        }
        browser_cfg = BrowserConfig(
            browser_type="firefox",
            headless=True,
            verbose=True
        )
        run_cfg = CrawlerRunConfig(url=url, wait_for=None)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        submission_details = {
            "url": url,
            "timestamp": timestamp,
            "fields_filled": [],
            "success": False,
            "form_id": None
        }
        async with AsyncWebCrawler() as crawler:
            try:
                visited_urls = set()
                max_hops = 2  # Try up to 2 additional relevant subpages
                hops = 0
                current_url = url
                while hops <= max_hops:
                    result = await crawler.arun(url=current_url, browser_config=browser_cfg, config=run_cfg)
                    html = result.html
                    soup = BeautifulSoup(html, "html.parser")
                    forms = soup.find_all("form")
                    visited_urls.add(current_url)

                    # Detect lead form
                    lead_form = None
                    for form in forms:
                        if is_likely_lead_form(form):
                            lead_form = form
                            break

                    if lead_form:
                        # --- Fill the form fields ---
                        # Placeholder for actual fill logic
                        fill_status = "success"
                        submission_status = "submitted"
                        return {**state, "fill_result": fill_status, "submission_status": submission_status, "final_url": current_url}

                    # If not found and we have hops left, try to find a contact/quote/estimate page
                    if hops < max_hops:
                        next_url = find_contact_or_quote_page(soup, current_url)
                        if next_url and next_url not in visited_urls:
                            current_url = next_url
                            hops += 1
                            continue
                    # No more hops or no next page to try
                    break

                # If we get here, no lead form was found after crawling
                return {**state, "fill_result": "error", "error": f"No lead form found after checking {hops+1} page(s)."}

            except Exception as e:
                if "Execution context was destroyed" in str(e):
                    return {**state, "fill_result": "error", "error": "Page navigation interrupted form fill (execution context destroyed)"}
                return {**state, "fill_result": "error", "error": f"Exception in fill_lead_form_node: {e}"}

            
            # Log the navigation journey
            # print(f"[NAVIGATION] Path: {' -> '.join(navigation_path)}")
            
            # Update submission URL to final page where form was found
            submission_details['url'] = current_url
            
            # If still no forms found after navigation, exit
            if not forms:
                return {"error": f"No forms found after navigating {len(navigation_path)} pages. Path: {' -> '.join(navigation_path)}"}
            
            # Smart form scoring to identify the most likely lead form
            # print(f"[FORMS] Found {len(forms)} forms on page. Scoring each...")
            form_scores = []
            
            for i, form in enumerate(forms):
                form_id = form.get('id', f'form_{i}')
                form_action = form.get('action', '')
                form_method = form.get('method', 'get').lower()
                form_class = form.get('class', [])
                form_classes = ' '.join(form_class) if isinstance(form_class, list) else str(form_class)
                
                # Extract all inputs and their types
                inputs = form.find_all('input')
                textareas = form.find_all('textarea')
                selects = form.find_all('select')
                buttons = form.find_all(['button', 'input[type="submit"]'])
                
                # Count different types of fields
                text_inputs = [inp for inp in inputs if inp.get('type', 'text').lower() in ['text', 'email', 'tel', 'number']]
                hidden_inputs = [inp for inp in inputs if inp.get('type', '').lower() == 'hidden']
                checkbox_inputs = [inp for inp in inputs if inp.get('type', '').lower() == 'checkbox']
                
                # Extract form text for context
                form_text = form.get_text().lower()
                button_texts = []
                for button in buttons:
                    if button.name == 'button':
                        button_texts.append(button.get_text().strip().lower())
                    else:  # input[type="submit"]
                        button_texts.append(button.get('value', '').lower())
                
                # Initialize score
                score = 0
                
                # Scoring based on form attributes
                if 'post' in form_method:  # POST is more likely for lead forms
                    score += 10
                if any(term in form_action.lower() for term in ['contact', 'quote', 'estimate', 'lead', 'submit', 'form']):
                    score += 15
                if any(term in form_classes.lower() for term in ['contact', 'lead', 'form', 'quote']):
                    score += 10
                
                # Scoring based on input fields
                score += min(len(text_inputs) * 5, 20)  # More text inputs = better (up to 4)
                score += min(len(textareas), 2) * 10  # Textareas are good for messages/comments
                if len(text_inputs) >= 3:  # Forms with 3+ text fields are more likely lead forms
                    score += 15
                
                # Negative factors for search forms
                if len(text_inputs) == 1 and len(inputs) <= 2:  # Likely a search box
                    score -= 20
                if len(form.find_all('*')) < 5:  # Very simple forms are likely not lead forms
                    score -= 10
                
                # Check for name/email/phone field patterns
                name_pattern = any(inp.get('name', '').lower() in ['name', 'firstname', 'first-name', 'fullname', 'full-name'] or 
                                 inp.get('id', '').lower() in ['name', 'firstname', 'first-name', 'fullname', 'full-name'] or
                                 inp.get('placeholder', '').lower() in ['name', 'your name', 'full name'] for inp in inputs)
                
                email_pattern = any(inp.get('name', '').lower() in ['email', 'e-mail', 'emailaddress', 'email-address'] or 
                                 inp.get('id', '').lower() in ['email', 'e-mail', 'emailaddress'] or
                                 inp.get('placeholder', '').lower() in ['email', 'your email', 'email address'] or
                                 inp.get('type', '').lower() == 'email' for inp in inputs)
                
                phone_pattern = any(inp.get('name', '').lower() in ['phone', 'phonenumber', 'phone-number', 'tel', 'telephone'] or 
                                 inp.get('id', '').lower() in ['phone', 'phonenumber', 'tel'] or
                                 inp.get('placeholder', '').lower() in ['phone', 'your phone', 'phone number'] or
                                 inp.get('type', '').lower() == 'tel' for inp in inputs)
                
                # Add score for common lead form field patterns
                if name_pattern:
                    score += 20
                if email_pattern:
                    score += 20
                if phone_pattern:
                    score += 20
                if name_pattern and email_pattern:  # Forms with both name and email are very likely lead forms
                    score += 25
                
                # Check button text for lead form indicators
                positive_button_terms = ['submit', 'send', 'contact', 'quote', 'estimate', 'free', 'get', 'request', 'schedule']
                if any(any(term in btn_text for term in positive_button_terms) for btn_text in button_texts):
                    score += 15
                
                # Check form context
                positive_context_terms = ['contact', 'get in touch', 'free quote', 'estimate', 'consultation', 'schedule', 'appointment']
                if any(term in form_text for term in positive_context_terms):
                    score += 15
                
                # Log the form details for debugging
                # print(f"[FORM #{i}] ID={form_id}, Fields={len(inputs)}+{len(textareas)}+{len(selects)}, Score={score}")
                # print(f"  - Action: {form_action[:50]}")
                # print(f"  - Method: {form_method}")
                # print(f"  - Inputs: {len(text_inputs)} text, {len(hidden_inputs)} hidden, {len(checkbox_inputs)} checkbox")
                # print(f"  - Buttons: {button_texts[:3]}")
                
                form_scores.append((form, score, i))
            
            # Sort forms by score in descending order
            form_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Select the highest scoring form
            if form_scores:
                target_form, best_score, form_index = form_scores[0]
                form_id = target_form.get('id', f'form_{form_index}')
                # print(f"[FORM SELECTION] Chose form #{form_index} with score {best_score} as the target lead form")
            else:
                target_form = forms[0]  # Fallback to first form if scoring failed
                form_id = target_form.get('id', 'form_0')
                # print("[FORM SELECTION] Falling back to first form (no scores calculated)")
            
            submission_details["form_id"] = form_id
            js_lines = []
            field_order = ["name", "email", "phone", "address", "message"]
            all_inputs = target_form.find_all('input')
            used_inputs = set()
            for idx, (field_key, field_value) in enumerate(lead_data.items()):
                field_aliases = input_name_map.get(field_key, [field_key])
                found = False
                matched_by = None
                matched_elem = None
                for alias in field_aliases:
                    elements = target_form.find_all('input', attrs={'name': alias})
                    if elements:
                        matched_by = 'name'
                    if not elements:
                        elements = [e for e in target_form.find_all('input') if e.has_attr('name') and alias in e['name'].lower()]
                        if elements:
                            matched_by = 'partial_name'
                    if not elements:
                        elements = target_form.find_all('input', attrs={'id': alias})
                        if elements:
                            matched_by = 'id'
                    if not elements:
                        elements = target_form.find_all('input', attrs={'placeholder': alias})
                        if elements:
                            matched_by = 'placeholder'
                    if not elements:
                        elements = [e for e in target_form.find_all('input') if e.has_attr('aria-label') and alias in e['aria-label'].lower()]
                        if elements:
                            matched_by = 'aria-label'
                    if not elements:
                        elements = [e for e in target_form.find_all('input') if e.has_attr('title') and alias in e['title'].lower()]
                        if elements:
                            matched_by = 'title'
                    if not elements:
                        labels = target_form.find_all('label')
                        for label in labels:
                            label_text = label.get_text().strip().lower()
                            if alias in label_text:
                                label_for = label.get('for')
                                if label_for:
                                    label_elem = target_form.find('input', attrs={'id': label_for})
                                    if label_elem:
                                        elements = [label_elem]
                                        matched_by = 'label_for'
                                        break
                                label_input = label.find('input')
                                if label_input:
                                    elements = [label_input]
                                    matched_by = 'label_child'
                                    break
                    if not elements:
                        regex = re.compile(re.escape(alias), re.IGNORECASE)
                        elements = [e for e in target_form.find_all('input') if any(regex.search(str(e.get(attr,''))) for attr in ['name','id','placeholder','aria-label','title'])]
                        if elements:
                            matched_by = 'fuzzy_attr'
                    elements = [e for e in elements if e not in used_inputs]
                    if elements:
                        elem = elements[0]
                        used_inputs.add(elem)
                        input_type = elem.get('type', 'text').lower()
                        input_name = elem.get('name') or elem.get('id', f'input_{alias}')
                        if input_type in ['text', 'email', 'tel', 'hidden']:
                            if input_name:
                                js_lines.append(f"document.getElementsByName('{input_name}')[0].value = '{field_value}';")
                                if elem.get('id'):
                                    js_lines.append(f"document.getElementById('{elem.get('id')}').value = '{field_value}';")
                            else:
                                js_lines.append(f"document.querySelectorAll('input[type=\"{input_type}\"]')[0].value = '{field_value}';")
                            submission_details["fields_filled"].append({
                                "name": input_name or alias,
                                "type": input_type,
                                "value": field_value,
                                "matched_by": matched_by
                            })
                            found = True
                            break
                if not found:
                    for alias in field_aliases:
                        elements = target_form.find_all('textarea', attrs={'name': alias})
                        if elements:
                            js_lines.append(f"document.getElementsByName('{alias}')[0].value = '{field_value}';")
                            submission_details["fields_filled"].append({
                                "name": alias,
                                "type": "textarea",
                                "value": field_value,
                                "matched_by": "textarea_name"
                            })
                            found = True
                            break
                if not found:
                    unused_inputs = [e for e in all_inputs if e not in used_inputs and e.get('type','text') in ['text','email','tel'] and not e.has_attr('disabled') and e.get('style','').find('display:none')==-1]
                    if unused_inputs:
                        elem = unused_inputs[0]
                        used_inputs.add(elem)
                        input_name = elem.get('name') or elem.get('id', f'input_{field_key}')
                        js_lines.append(f"document.getElementsByName('{input_name}')[0].value = '{field_value}';")
                        submission_details["fields_filled"].append({
                            "name": input_name or field_key,
                            "type": elem.get('type','text'),
                            "value": field_value,
                            "matched_by": "order_fallback"
                        })
            checkbox_fields = [inp.get('name') for inp in target_form.find_all('input', attrs={'type': 'checkbox'})]
            for checkbox in checkbox_fields:
                if checkbox and checkbox.lower() in ["agree", "consent", "terms", "policy", "marketing_consent"]:
                    js_lines.append(f"document.getElementsByName('{checkbox}')[0].checked = true;")
                    submission_details["fields_filled"].append({
                        "name": checkbox,
                        "type": "checkbox",
                        "value": "checked"
                    })
            for select in target_form.find_all('select'):
                select_name = select.get('name')
                if select_name:
                    options = select.find_all('option')
                    for option in options:
                        if option.get('value') and option.get('value') != "":
                            js_lines.append(f"document.getElementsByName('{select_name}')[0].value = '{option.get('value')}';")
                            submission_details["fields_filled"].append({
                                "name": select_name,
                                "type": "select",
                                "value": option.get('value')
                            })
                            break
                    if len(options) > 1:
                        js_lines.append(f"document.getElementsByName('{select_name}')[0].selectedIndex = 1;")
            if target_form.get('id'):
                js_lines.append(f"document.getElementById('{target_form.get('id')}').submit();")
            else:
                for i, form in enumerate(forms):
                    if form == target_form:
                        js_lines.append(f"document.forms[{i}].submit();")
                        break
                else:
                    js_lines.append("document.forms[0].submit();")
            
            # Look for any next step button that might need to be clicked after submitting initial data
            next_step_detection = """
            // After form filling, look for and click any 'next' or 'continue' buttons if they exist
            setTimeout(function() {
                const nextButtons = Array.from(document.querySelectorAll('button, input[type="submit"], a.btn, .button, .btn')).filter(el => {
                    const text = (el.textContent || el.value || '').toLowerCase();
                    const classes = (el.className || '').toLowerCase();
                    return text.includes('next') || text.includes('continue') || text.includes('submit') || 
                           classes.includes('next') || classes.includes('continue') || classes.includes('submit');
                });
                if (nextButtons.length > 0) {
                    console.log('[AGENT] Clicking next/continue button: ' + nextButtons[0].outerHTML);
                    nextButtons[0].click();
                }
            }, 1000);
            """
            try:
                # print('[DEBUG] Entering try block for form filling/submitting.')
                # Fill the form fields (no submit yet)
                await crawler.arun(
                    url=url,
                    config=run_cfg,
                    browser_config=browser_cfg,
                )
                # --- Now submit the form ---
                submit_js = None
                form_id = submission_details.get('form_id') or (target_form.get('id') if target_form else None)
                if form_id:
                    submit_js = f"document.getElementById('{form_id}').submit();"
                await crawler.arun(
                    url=url,
                    config=CrawlerRunConfig(js_code=submit_js, wait_for=None),
                    browser_config=browser_cfg,
                )
                submission_details["success"] = True
            except Exception as e:
                submission_details["error"] = f"Error filling/submitting form: {str(e)}"
                # print(f"[ERROR] Error filling/submitting form: {str(e)}")
            
            # Build result message
            filled_fields_msg = "\n".join([
                f"- {f['name']} ({f['type']}): {f['value']}" for f in submission_details["fields_filled"]
            ])
            fill_result = (
                f"[CRAWL4AI] Form fields filled and submitted on {url}.\n"
                f"Filled {len(submission_details['fields_filled'])} fields:\n{filled_fields_msg}"
            )

            # --- Always write a CSV entry for submitted actions ---
            import csv
            csv_dir = os.path.join(os.path.dirname(os.path.realpath(__file__)), 'form_submissions')
            os.makedirs(csv_dir, exist_ok=True)
            csv_path = os.path.join(csv_dir, 'form_submissions.csv')
            csv_exists = os.path.exists(csv_path)
            csv_fields = [
                'timestamp', 'url', 'form_id', 'fields_count', 'success', 'error', 'mode'
            ]
            csv_row = {
                'timestamp': datetime.datetime.now().strftime("%Y%m%d_%H%M%S"),
                'url': url,
                'form_id': submission_details.get('form_id', 'unknown'),
                'fields_count': len(submission_details.get('fields_filled', [])),
                'success': submission_details.get('success', False),


                'error': submission_details.get('error', ''),
                'mode': 'submitted'
            }
            with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=csv_fields)
                if not csv_exists:
                    writer.writeheader()
                writer.writerow(csv_row)
            # print(f"[CSV] Wrote submitted entry to {csv_path}: {csv_row}")

            return {**state, "fill_result": fill_result, "submission_details": submission_details}

    except Exception as e:
        return {**state, "fill_result": "error", "error": str(e)}
