# Response Monitoring System

This module provides functionality to monitor and analyze responses from various channels (SMS, email, voice) for the lead generation system.

## Overview

The Response Monitoring System consists of the following components:

1. **Response Monitor**: Coordinates monitoring across different channels
2. **Email Agent**: Monitors email responses via IMAP
3. **SMS Agent**: Monitors SMS responses via Telnyx webhook
4. **Match Infer Graph**: LangGraph-based agent for entity extraction and lead matching
5. **Lead Context Store**: Loads and manages lead data for matching
6. **Audit Logger**: Logs response monitoring results
7. **Response Audit**: Generates audit reports for lead responses

## Setup

### Prerequisites

- Python 3.8+
- Supabase account with tables for leads and responses
- OpenAI API key for LLM-based processing
- Email account with IMAP access (for email monitoring)
- Telnyx account (for SMS monitoring)

### Environment Variables

Create a `.env` file with the following variables:

```
# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Email Configuration
EMAIL_USERNAME=your_email_username
EMAIL_APP_PASSWORD=your_email_app_password
EMAIL_IMAP_SERVER=imap.gmail.com

# Telnyx API Key
TELNYX_API_KEY=your_telnyx_api_key
```

### Installation

1. Install dependencies:

```bash
pip install -r requirements.txt
```

2. Set up the database tables (if not already created):

```bash
python -c "from lead_context_store import LeadContextStore; LeadContextStore().create_tables()"
```

## Usage

### Command-Line Interface

The system provides a command-line interface for running the response monitoring system:

```bash
# Start monitoring
python run_monitor.py monitor --email --interval 60

# Generate report
python run_monitor.py report --days 30

# Check status
python run_monitor.py status
```

### API

The system also provides a FastAPI interface for integration with the frontend:

```bash
# Start the API server
python api.py
```

The API will be available at http://localhost:8005 with the following endpoints:

- `GET /health`: Health check endpoint
- `GET /status`: Get monitoring status
- `POST /start`: Start monitoring
- `POST /stop`: Stop monitoring
- `GET /report`: Generate response audit report
- `GET /dashboard`: Get dashboard statistics

## Components

### Response Monitor

The Response Monitor coordinates monitoring across different channels and provides a unified interface for response tracking and analysis.

```python
from response_monitor import ResponseMonitor

# Initialize response monitor
monitor = ResponseMonitor(
    email_config=email_config,
    openai_api_key=openai_api_key,
    enable_audit_logging=True
)

# Start email monitoring
monitor.start_email_monitoring(interval=60)
```

### Email Agent

The Email Agent monitors email responses via IMAP and processes them using the Match Infer Graph.

```python
from email_agent import EmailAgent

# Initialize email agent
agent = EmailAgent(
    username=email_username,
    password=email_password,
    imap_server=imap_server
)

# Fetch emails
emails = agent.fetch_emails(mailbox="INBOX", search_criteria="UNSEEN")
```

### SMS Agent

The SMS Agent monitors SMS responses via Telnyx webhook and processes them using the Match Infer Graph.

```python
from flask import Flask, request, jsonify
from match_infer_graph import MatchInferGraph

app = Flask(__name__)
sms_agent = MatchInferGraph()

@app.route('/sms_webhook', methods=['POST'])
def sms_webhook():
    data = request.json
    # Process SMS data
    result = sms_agent.process_message(data)
    return jsonify({"status": "ok", "result": result})
```

### Response Audit

The Response Audit generates audit reports for lead responses, analyzing response times, channels, and effectiveness.

```python
from analytics.response_audit import ResponseAudit

# Initialize response audit
audit = ResponseAudit()

# Generate response time report
report = audit.generate_response_time_report(start_date, end_date)
```

## Database Schema

The system uses the following Supabase tables:

### leads

Stores lead information:

- `id`: UUID primary key
- `first_name`: Lead first name
- `last_name`: Lead last name
- `email`: Lead email
- `phone`: Lead phone number
- `company`: Lead company
- `created_at`: Timestamp when the lead was created
- ...

### responses

Stores response information:

- `id`: UUID primary key
- `lead_id`: Foreign key to leads table
- `form_submission_id`: Foreign key to form_submissions table
- `channel`: Response channel (email, sms, voice)
- `content`: Response content
- `received_at`: Timestamp when the response was received
- `sender_email`: Sender email
- `sender_phone`: Sender phone
- `sender_name`: Sender name
- `match_confidence`: Confidence score for lead matching
- `sentiment_score`: Sentiment score for the response
- `quality_score`: Quality score for the response
- `metadata`: Additional metadata

### form_submissions

Stores form submission information:

- `id`: UUID primary key
- `lead_id`: Foreign key to leads table
- `website_id`: Foreign key to websites table
- `submission_time`: Timestamp when the form was submitted
- `status`: Submission status
- `form_data`: Form data submitted
- `metadata`: Additional metadata

## License

This project is licensed under the MIT License - see the LICENSE file for details.
