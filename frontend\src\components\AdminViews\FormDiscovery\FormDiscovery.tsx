import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

// Mock data for demonstration
const mockForms = [
  {
    id: '1',
    url: 'https://example.com/contact',
    title: 'Contact Form',
    discoveredAt: '2023-05-01T14:30:00Z',
    status: 'discovered',
    fieldCount: 5,
    hasCaptcha: true,
    multiStep: false,
  },
  {
    id: '2',
    url: 'https://example.com/support',
    title: 'Support Request',
    discoveredAt: '2023-05-02T09:15:00Z',
    status: 'analyzed',
    fieldCount: 7,
    hasCaptcha: false,
    multiStep: true,
  },
  {
    id: '3',
    url: 'https://example.com/feedback',
    title: 'Feedback Form',
    discoveredAt: '2023-05-02T16:45:00Z',
    status: 'submitted',
    fieldCount: 4,
    hasCaptcha: false,
    multiStep: false,
  },
];

// Styled components
const FormDiscoveryContainer = styled.div`
  padding: 20px;
`;

const Header = styled.h1`
  font-size: 24px;
  margin-bottom: 20px;
`;

const DiscoveryForm = styled.div`
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
`;

const FormGroup = styled.div`
  margin-bottom: 15px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
`;

const Button = styled.button`
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  
  &:hover {
    background-color: #45a049;
  }
`;

const FormsContainer = styled.div`
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const FormsHeader = styled.h2`
  font-size: 18px;
  margin-bottom: 20px;
`;

const FormsTable = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.th`
  text-align: left;
  padding: 12px;
  border-bottom: 2px solid #ddd;
`;

const TableRow = styled.tr`
  &:nth-child(even) {
    background-color: #f9f9f9;
  }
  
  &:hover {
    background-color: #f1f1f1;
  }
`;

const TableCell = styled.td`
  padding: 12px;
  border-bottom: 1px solid #ddd;
`;

const StatusBadge = styled.span<{ status: string }>`
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  background-color: ${props => {
    switch (props.status) {
      case 'discovered': return '#e3f2fd';
      case 'analyzed': return '#e8f5e9';
      case 'submitted': return '#fff3e0';
      case 'failed': return '#ffebee';
      default: return '#f5f5f5';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'discovered': return '#1976d2';
      case 'analyzed': return '#388e3c';
      case 'submitted': return '#f57c00';
      case 'failed': return '#d32f2f';
      default: return '#757575';
    }
  }};
`;

const Badge = styled.span<{ type: string }>`
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-right: 5px;
  background-color: ${props => props.type === 'captcha' ? '#e8eaf6' : '#fff3e0'};
  color: ${props => props.type === 'captcha' ? '#3f51b5' : '#ff9800'};
`;

const ActionButton = styled.button`
  background-color: transparent;
  color: #2196f3;
  border: none;
  cursor: pointer;
  margin-right: 10px;
  
  &:hover {
    text-decoration: underline;
  }
`;

const FormDiscovery: React.FC = () => {
  const [url, setUrl] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [industry, setIndustry] = useState('');
  const [priority, setPriority] = useState('1');
  const [forms, setForms] = useState(mockForms);
  const [isLoading, setIsLoading] = useState(false);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    
    // In a real implementation, this would call the API
    // For now, we'll simulate an API call
    setTimeout(() => {
      // Add a new mock form
      const newForm = {
        id: String(forms.length + 1),
        url,
        title: `Form on ${new URL(url).hostname}`,
        discoveredAt: new Date().toISOString(),
        status: 'discovered',
        fieldCount: Math.floor(Math.random() * 10) + 3,
        hasCaptcha: Math.random() > 0.5,
        multiStep: Math.random() > 0.7,
      };
      
      setForms([newForm, ...forms]);
      setIsLoading(false);
      
      // Reset form
      setUrl('');
      setCompanyName('');
      setIndustry('');
      setPriority('1');
    }, 2000);
  };
  
  return (
    <FormDiscoveryContainer>
      <Header>Form Discovery</Header>
      
      <DiscoveryForm>
        <form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="url">Website URL</Label>
            <Input
              type="url"
              id="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              required
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="companyName">Company Name (Optional)</Label>
            <Input
              type="text"
              id="companyName"
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              placeholder="Acme Inc."
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="industry">Industry (Optional)</Label>
            <Input
              type="text"
              id="industry"
              value={industry}
              onChange={(e) => setIndustry(e.target.value)}
              placeholder="Technology"
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="priority">Priority</Label>
            <Select
              id="priority"
              value={priority}
              onChange={(e) => setPriority(e.target.value)}
            >
              <option value="1">High</option>
              <option value="2">Medium</option>
              <option value="3">Low</option>
            </Select>
          </FormGroup>
          
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Discovering...' : 'Discover Forms'}
          </Button>
        </form>
      </DiscoveryForm>
      
      <FormsContainer>
        <FormsHeader>Discovered Forms</FormsHeader>
        
        <FormsTable>
          <thead>
            <tr>
              <TableHeader>URL</TableHeader>
              <TableHeader>Title</TableHeader>
              <TableHeader>Discovered At</TableHeader>
              <TableHeader>Status</TableHeader>
              <TableHeader>Fields</TableHeader>
              <TableHeader>Features</TableHeader>
              <TableHeader>Actions</TableHeader>
            </tr>
          </thead>
          <tbody>
            {forms.map(form => (
              <TableRow key={form.id}>
                <TableCell>{form.url}</TableCell>
                <TableCell>{form.title}</TableCell>
                <TableCell>{new Date(form.discoveredAt).toLocaleString()}</TableCell>
                <TableCell>
                  <StatusBadge status={form.status}>
                    {form.status}
                  </StatusBadge>
                </TableCell>
                <TableCell>{form.fieldCount}</TableCell>
                <TableCell>
                  {form.hasCaptcha && <Badge type="captcha">CAPTCHA</Badge>}
                  {form.multiStep && <Badge type="multistep">Multi-step</Badge>}
                </TableCell>
                <TableCell>
                  <ActionButton>View</ActionButton>
                  <ActionButton>Analyze</ActionButton>
                  <ActionButton>Submit</ActionButton>
                </TableCell>
              </TableRow>
            ))}
          </tbody>
        </FormsTable>
      </FormsContainer>
    </FormDiscoveryContainer>
  );
};

export default FormDiscovery;
