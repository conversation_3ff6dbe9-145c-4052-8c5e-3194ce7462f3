from typing import Dict, Any, Optional, List
import json
import os
from supabase import create_client, Client
from rapidfuzz import fuzz, process

class LeadContextStore:
    """
    Loads and manages previous form submissions, indexed by domain, email, or phone.
    Now fetches leads from Supabase instead of CSV or JSON.
    """
    def __init__(self):
        try:
            from supabase import create_client, Client
        except ImportError:
            raise ImportError("You must install supabase-py to use LeadContextStore with Supabase.")
        import os
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in environment.")
        self.client = create_client(self.supabase_url, self.supabase_key)

    def all(self) -> List[Dict[str, Any]]:
        leads = self.client.table("leads").select("*").execute()
        return leads.data if hasattr(leads, 'data') else leads

    def find_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        if not email:
            return None
        res = self.client.table("leads").select("*").eq("email", email).limit(1).execute()
        data = res.data if hasattr(res, 'data') else res
        return data[0] if data else None

    def find_by_phone(self, phone: str) -> Optional[Dict[str, Any]]:
        if not phone:
            return None
        res = self.client.table("leads").select("*").eq("phone", phone).limit(1).execute()
        data = res.data if hasattr(res, 'data') else res
        return data[0] if data else None

    def get_domain(self, value: str) -> Optional[str]:
        """
        Extract the root domain from a website URL or email address. Removes http(s), www, paths, and trailing slashes.
        Examples:
            https://www.example.com/ -> example.com
            http://example.com -> example.com
            www.example.com -> example.com
            example.com/path -> example.com
            <EMAIL> -> example.com
        """
        if not value or not isinstance(value, str):
            return None
        value = value.strip().lower()
        if '@' in value:
            value = value.split('@')[-1]
        if value.startswith('http://'):
            value = value[len('http://'):]
        elif value.startswith('https://'):
            value = value[len('https://'):]
        if value.startswith('www.'):
            value = value[4:]
        value = value.split('/')[0]
        value = value.rstrip('/')
        return value if value else None

    def find_by_domain(self, domain: str) -> Optional[Dict[str, Any]]:
        """
        Find a lead by matching the domain to the lead's website or lead email domain.
        """
        if not domain:
            return None
        domain = self.get_domain(domain)
        leads = self.all()
        for lead in leads:
            # Try website field
            website = lead.get("website")
            website_domain = self.get_domain(website) if website else None
            if website_domain and website_domain == domain:
                return lead
            # Try email field
            lead_email = lead.get("email")
            email_domain = self.get_domain(lead_email) if lead_email else None
            if email_domain and email_domain == domain:
                return lead
        return None

    def find_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        if not name:
            return None
        name = name.strip().lower()
        leads = self.all()
        for lead in leads:
            full_name = (lead.get("first_name", "") + " " + lead.get("last_name", "")).strip().lower()
            if full_name and name == full_name:
                return lead
            # Also match against a 'name' field if present
            if "name" in lead and lead["name"].strip().lower() == name:
                return lead
        return None

    def find_by_company(self, company: str) -> Optional[Dict[str, Any]]:
        if not company:
            return None
        company = company.strip().lower()
        leads = self.all()
        for lead in leads:
            lead_company = lead.get("company", "").strip().lower()
            if lead_company and company == lead_company:
                return lead
        return None

    def find_fuzzy_lead(self, name: Optional[str] = None, company: Optional[str] = None, domain: Optional[str] = None, threshold: int = 80) -> Optional[Dict[str, Any]]:
        """
        Fuzzy match a lead by name, company, or domain. Domain matching uses the end of the email address or website URL.
        """
        from rapidfuzz import fuzz
        leads = self.all()
        best_score = 0
        best_lead = None
        domain = self.get_domain(domain) if domain else None
        for lead in leads:
            score = 0
            lead_name = lead.get("name") or (lead.get("first_name", "") + " " + lead.get("last_name", "")).strip()
            lead_company = lead.get("company")
            lead_website = lead.get("website")
            lead_email = lead.get("email")
            # Name matching
            if name and lead_name:
                lead_name_str = lead_name if isinstance(lead_name, str) else str(lead_name)
                score += fuzz.token_set_ratio(name.lower(), lead_name_str.lower())
            # Company matching
            if company and lead_company:
                lead_company_str = lead_company if isinstance(lead_company, str) else str(lead_company)
                score += fuzz.token_set_ratio(company.lower(), lead_company_str.lower())
            # Domain matching (from website or email)
            if domain:
                website_domain = self.get_domain(lead_website) if lead_website else None
                email_domain = self.get_domain(lead_email) if lead_email else None
                if website_domain:
                    score += fuzz.ratio(domain, website_domain)
                if email_domain:
                    score += fuzz.ratio(domain, email_domain)
            if score > best_score and score >= threshold:
                best_score = score
                best_lead = lead
        print(f"[DEBUG] Fuzzy match result: {best_lead} with score {best_score}")
        return best_lead

    def add_lead(self, lead: Dict[str, Any]):
        self.client.table("leads").insert([lead]).execute()
