"""
Response Monitor Module

This module coordinates the monitoring of responses across different channels (SMS, email, voice)
and provides a unified interface for response tracking and analysis.
"""
import os
import time
import threading
import json
from typing import Dict, Any, List, Optional, Union, Callable
from datetime import datetime

# Import agent modules
try:
    from email_agent import EmailAgent
    from sms_agent import SMSAgent
    from match_infer_graph import MatchInferGraph
    from lead_context_store import LeadContextStore
    from audit_logger import AuditLogger
except ImportError:
    # Handle relative imports when running as a module
    from .email_agent import EmailAgent
    from .sms_agent import SMSAgent
    from .match_infer_graph import MatchInferGraph
    from .lead_context_store import LeadContextStore
    from .audit_logger import AuditLogger

class ResponseMonitor:
    """
    Coordinates response monitoring across different channels.
    """
    
    def __init__(self, 
                 email_config: Optional[Dict[str, Any]] = None,
                 sms_config: Optional[Dict[str, Any]] = None,
                 openai_api_key: Optional[str] = None,
                 enable_audit_logging: bool = True,
                 log_dir: str = "./logs"):
        """
        Initialize the response monitor.
        
        Args:
            email_config: Configuration for email monitoring
            sms_config: Configuration for SMS monitoring
            openai_api_key: OpenAI API key for LLM-based processing
            enable_audit_logging: Whether to enable audit logging
            log_dir: Directory for log files
        """
        self.email_config = email_config or {}
        self.sms_config = sms_config or {}
        self.openai_api_key = openai_api_key or os.environ.get("OPENAI_API_KEY")
        self.enable_audit_logging = enable_audit_logging
        self.log_dir = log_dir
        
        # Initialize components
        self.lead_store = LeadContextStore()
        self.match_infer_agent = MatchInferGraph(openai_api_key=self.openai_api_key)
        
        # Initialize audit logger if enabled
        self.audit_logger = None
        if self.enable_audit_logging:
            self.audit_logger = AuditLogger(
                log_dir=self.log_dir,
                csv_filename="response_audit_log.csv"
            )
        
        # Initialize agents
        self.email_agent = None
        self.sms_agent = None
        
        # Initialize monitoring threads
        self.email_monitor_thread = None
        self.sms_monitor_thread = None
        self.monitoring_active = False
    
    def initialize_email_agent(self):
        """Initialize the email agent with configuration."""
        if not self.email_config:
            print("[WARN] Email agent not initialized: Missing configuration")
            return False
        
        try:
            username = self.email_config.get("username")
            password = self.email_config.get("password")
            imap_server = self.email_config.get("imap_server", "imap.gmail.com")
            imap_port = self.email_config.get("imap_port", 993)
            
            if not username or not password:
                print("[WARN] Email agent not initialized: Missing username or password")
                return False
            
            self.email_agent = EmailAgent(
                username=username,
                password=password,
                imap_server=imap_server,
                imap_port=imap_port
            )
            
            # Test connection
            if not self.email_agent.connect_imap():
                print("[ERROR] Failed to connect to IMAP server")
                return False
            
            print(f"[INFO] Email agent initialized successfully: {username}@{imap_server}")
            return True
        except Exception as e:
            print(f"[ERROR] Failed to initialize email agent: {e}")
            return False
    
    def initialize_sms_agent(self):
        """Initialize the SMS agent with configuration."""
        # SMS agent is implemented as a webhook server, so we don't need to initialize it here
        # This is just a placeholder for future SMS agent initialization if needed
        return True
    
    def process_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a response from any channel.
        
        Args:
            response_data: Response data
            
        Returns:
            Processing result
        """
        try:
            # Process the response using the match-infer agent
            result = self.match_infer_agent.process_message(response_data)
            
            # Log the result if audit logging is enabled
            if self.audit_logger:
                self.audit_logger.log_result(result)
            
            return result
        except Exception as e:
            print(f"[ERROR] Error processing response: {e}")
            return {
                "error": str(e),
                "status": "failed",
                "message": response_data
            }
    
    def start_email_monitoring(self, 
                              interval: int = 60, 
                              mailbox: str = "INBOX",
                              search_criteria: str = "UNSEEN"):
        """
        Start monitoring emails.
        
        Args:
            interval: Polling interval in seconds
            mailbox: Mailbox to monitor
            search_criteria: Search criteria for emails
        """
        if not self.email_agent:
            if not self.initialize_email_agent():
                print("[ERROR] Failed to initialize email agent. Email monitoring not started.")
                return False
        
        def email_monitor_worker():
            print(f"[INFO] Starting email monitoring: {mailbox}, interval={interval}s")
            while self.monitoring_active:
                try:
                    emails = self.email_agent.fetch_emails(
                        mailbox=mailbox,
                        search_criteria=search_criteria,
                        lead_store=self.lead_store
                    )
                    
                    print(f"[INFO] Fetched {len(emails)} new emails")
                    
                    for email in emails:
                        self.process_response(email)
                    
                    time.sleep(interval)
                except Exception as e:
                    print(f"[ERROR] Error in email monitoring: {e}")
                    time.sleep(interval)
        
        self.monitoring_active = True
        self.email_monitor_thread = threading.Thread(target=email_monitor_worker)
        self.email_monitor_thread.daemon = True
        self.email_monitor_thread.start()
        
        return True
    
    def stop_monitoring(self):
        """Stop all monitoring threads."""
        self.monitoring_active = False
        
        if self.email_monitor_thread:
            self.email_monitor_thread.join(timeout=5)
        
        if self.sms_monitor_thread:
            self.sms_monitor_thread.join(timeout=5)
        
        if self.email_agent:
            self.email_agent.close()
        
        print("[INFO] Response monitoring stopped")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """
        Get the status of monitoring threads.
        
        Returns:
            Status information
        """
        return {
            "monitoring_active": self.monitoring_active,
            "email_monitoring": self.email_monitor_thread is not None and self.email_monitor_thread.is_alive(),
            "sms_monitoring": self.sms_monitor_thread is not None and self.sms_monitor_thread.is_alive(),
            "email_agent_initialized": self.email_agent is not None,
            "sms_agent_initialized": self.sms_agent is not None,
            "audit_logging_enabled": self.audit_logger is not None
        }


# Example usage
if __name__ == "__main__":
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("[WARN] python-dotenv not installed; skipping .env loading.")
    
    # Get email credentials from environment
    email_config = {
        "username": os.environ.get("EMAIL_USERNAME"),
        "password": os.environ.get("EMAIL_APP_PASSWORD"),
        "imap_server": os.environ.get("EMAIL_IMAP_SERVER", "imap.gmail.com")
    }
    
    # Initialize response monitor
    monitor = ResponseMonitor(
        email_config=email_config,
        openai_api_key=os.environ.get("OPENAI_API_KEY"),
        enable_audit_logging=True
    )
    
    # Start email monitoring
    monitor.start_email_monitoring(interval=60)
    
    try:
        # Keep the main thread running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("[INFO] Stopping response monitoring...")
        monitor.stop_monitoring()
