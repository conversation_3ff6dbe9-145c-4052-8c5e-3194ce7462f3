import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

// Mock data for demonstration
const mockData = [
  { name: '<PERSON>', submissions: 65, responses: 42, conversions: 28 },
  { name: 'Feb', submissions: 59, responses: 39, conversions: 25 },
  { name: 'Mar', submissions: 80, responses: 48, conversions: 31 },
  { name: 'Apr', submissions: 81, responses: 55, conversions: 35 },
  { name: 'May', submissions: 56, responses: 38, conversions: 24 },
  { name: '<PERSON>', submissions: 55, responses: 43, conversions: 28 },
  { name: 'Jul', submissions: 40, responses: 32, conversions: 19 },
];

// Mock response threads
const mockResponseThreads = [
  {
    id: '1',
    leadName: '<PERSON>',
    company: 'Acme Inc.',
    lastResponse: 'Thank you for your quick response. I would like to schedule a demo.',
    timestamp: '2023-05-01T14:30:00Z',
    status: 'active',
    channel: 'email',
  },
  {
    id: '2',
    leadName: '<PERSON>',
    company: 'XYZ Corp',
    lastResponse: 'I received your proposal and will review it with my team.',
    timestamp: '2023-05-02T09:15:00Z',
    status: 'active',
    channel: 'email',
  },
  {
    id: '3',
    leadName: 'Michael Brown',
    company: 'Global Solutions',
    lastResponse: 'Please send me more information about your enterprise plan.',
    timestamp: '2023-05-02T16:45:00Z',
    status: 'active',
    channel: 'sms',
  },
];

// Styled components
const DashboardContainer = styled.div`
  padding: 20px;
`;

const Header = styled.h1`
  font-size: 24px;
  margin-bottom: 20px;
`;

const MetricsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const MetricCard = styled.div`
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const MetricTitle = styled.h3`
  font-size: 14px;
  color: #666;
  margin: 0 0 10px 0;
`;

const MetricValue = styled.div`
  font-size: 24px;
  font-weight: bold;
`;

const ChartContainer = styled.div`
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  height: 400px;
`;

const ResponseThreadsContainer = styled.div`
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const ThreadsHeader = styled.h2`
  font-size: 18px;
  margin-bottom: 20px;
`;

const ThreadsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const ThreadItem = styled.div`
  padding: 15px;
  border-radius: 8px;
  background-color: #f9f9f9;
  border-left: 4px solid #4caf50;
`;

const ThreadHeader = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
`;

const LeadName = styled.div`
  font-weight: bold;
`;

const ThreadTimestamp = styled.div`
  font-size: 12px;
  color: #666;
`;

const ThreadCompany = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
`;

const ThreadMessage = styled.div`
  font-size: 14px;
`;

const ThreadStatus = styled.span<{ status: string }>`
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-left: 10px;
  background-color: ${props => props.status === 'active' ? '#e6f7e6' : '#f7e6e6'};
  color: ${props => props.status === 'active' ? '#4caf50' : '#f44336'};
`;

const ThreadChannel = styled.span<{ channel: string }>`
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-left: 10px;
  background-color: ${props => {
    switch (props.channel) {
      case 'email': return '#e3f2fd';
      case 'sms': return '#e8f5e9';
      case 'voice': return '#fff3e0';
      default: return '#f5f5f5';
    }
  }};
  color: ${props => {
    switch (props.channel) {
      case 'email': return '#1976d2';
      case 'sms': return '#388e3c';
      case 'voice': return '#f57c00';
      default: return '#757575';
    }
  }};
`;

const Dashboard: React.FC = () => {
  const [metrics, setMetrics] = useState({
    totalSubmissions: 0,
    totalResponses: 0,
    conversionRate: 0,
    avgResponseTime: 0,
  });
  
  const [chartData, setChartData] = useState(mockData);
  const [responseThreads, setResponseThreads] = useState(mockResponseThreads);
  
  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we'll use mock data
    
    // Calculate metrics from mock data
    const totalSubmissions = mockData.reduce((sum, item) => sum + item.submissions, 0);
    const totalResponses = mockData.reduce((sum, item) => sum + item.responses, 0);
    const totalConversions = mockData.reduce((sum, item) => sum + item.conversions, 0);
    const conversionRate = Math.round((totalConversions / totalSubmissions) * 100);
    
    setMetrics({
      totalSubmissions,
      totalResponses,
      conversionRate,
      avgResponseTime: 3.5, // Mock value in hours
    });
    
  }, []);
  
  return (
    <DashboardContainer>
      <Header>Dashboard</Header>
      
      <MetricsContainer>
        <MetricCard>
          <MetricTitle>Total Form Submissions</MetricTitle>
          <MetricValue>{metrics.totalSubmissions}</MetricValue>
        </MetricCard>
        
        <MetricCard>
          <MetricTitle>Total Responses</MetricTitle>
          <MetricValue>{metrics.totalResponses}</MetricValue>
        </MetricCard>
        
        <MetricCard>
          <MetricTitle>Conversion Rate</MetricTitle>
          <MetricValue>{metrics.conversionRate}%</MetricValue>
        </MetricCard>
        
        <MetricCard>
          <MetricTitle>Avg. Response Time</MetricTitle>
          <MetricValue>{metrics.avgResponseTime} hours</MetricValue>
        </MetricCard>
      </MetricsContainer>
      
      <ChartContainer>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={chartData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="submissions" stroke="#8884d8" activeDot={{ r: 8 }} />
            <Line type="monotone" dataKey="responses" stroke="#82ca9d" />
            <Line type="monotone" dataKey="conversions" stroke="#ffc658" />
          </LineChart>
        </ResponsiveContainer>
      </ChartContainer>
      
      <ResponseThreadsContainer>
        <ThreadsHeader>Recent Response Threads</ThreadsHeader>
        
        <ThreadsList>
          {responseThreads.map(thread => (
            <ThreadItem key={thread.id}>
              <ThreadHeader>
                <LeadName>
                  {thread.leadName}
                  <ThreadStatus status={thread.status}>
                    {thread.status}
                  </ThreadStatus>
                  <ThreadChannel channel={thread.channel}>
                    {thread.channel}
                  </ThreadChannel>
                </LeadName>
                <ThreadTimestamp>
                  {new Date(thread.timestamp).toLocaleString()}
                </ThreadTimestamp>
              </ThreadHeader>
              <ThreadCompany>{thread.company}</ThreadCompany>
              <ThreadMessage>{thread.lastResponse}</ThreadMessage>
            </ThreadItem>
          ))}
        </ThreadsList>
      </ResponseThreadsContainer>
    </DashboardContainer>
  );
};

export default Dashboard;
