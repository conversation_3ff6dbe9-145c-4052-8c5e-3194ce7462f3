import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Tabs,
  Tab,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  CircularProgress,
  Alert,
  Paper,
  Divider,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Switch
} from '@mui/material';
import {
  Search as SearchIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Business as BusinessIcon,
  Language as LanguageIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Facebook as FacebookIcon,
  LinkedIn as LinkedInIcon,
  Instagram as InstagramIcon,
  YouTube as YouTubeIcon,
  FilterList as FilterListIcon,
  Settings as SettingsIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
  Stop as StopIcon
} from '@mui/icons-material';
import formDiscoveryService from '../../services/formDiscoveryService';
import supabaseService from '../../services/supabaseService';
import leadEnrichmentService from '../../services/leadEnrichmentService';

/**
 * Form Discovery and Submission Panel Component
 *
 * This component combines form discovery and submission functionality into a single interface.
 * It allows users to:
 * 1. Discover and submit forms for a single website (with lead enrichment if needed)
 * 2. Submit forms in bulk for multiple leads
 */
const FormDiscoveryAndSubmissionPanel = () => {
  // Tab state
  const [activeTab, setActiveTab] = useState(0);

  // Single website mode state
  const [singleWebsiteUrl, setSingleWebsiteUrl] = useState('');
  const [singleWebsiteCompanyName, setSingleWebsiteCompanyName] = useState('');
  const [singleWebsiteIndustry, setSingleWebsiteIndustry] = useState('');
  const [singleWebsitePriority, setSingleWebsitePriority] = useState(5);
  const [singleWebsiteMaxPages, setSingleWebsiteMaxPages] = useState(10);
  const [singleWebsiteMaxDepth, setSingleWebsiteMaxDepth] = useState(3);
  const [singleWebsiteUseAiReasoning, setSingleWebsiteUseAiReasoning] = useState(true);
  const [singleWebsiteLeadData, setSingleWebsiteLeadData] = useState({
    name: '',
    email: '',
    phone: '',
    message: ''
  });
  const [singleWebsiteEnrichLead, setSingleWebsiteEnrichLead] = useState(true);
  const [singleWebsiteExistingLead, setSingleWebsiteExistingLead] = useState(null);
  const [singleWebsiteLoading, setSingleWebsiteLoading] = useState(false);
  const [singleWebsiteError, setSingleWebsiteError] = useState(null);
  const [singleWebsiteSuccess, setSingleWebsiteSuccess] = useState(null);
  const [singleWebsiteDiscoveredForms, setSingleWebsiteDiscoveredForms] = useState([]);
  const [singleWebsiteSelectedFormId, setSingleWebsiteSelectedFormId] = useState(null);

  // Bulk submission mode state
  const [importedLeads, setImportedLeads] = useState([]);
  const [selectedLeads, setSelectedLeads] = useState([]);
  const [filteredLeads, setFilteredLeads] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [targetWebsites, setTargetWebsites] = useState([{ url: '', selected: true }]);
  const [submissionConfig, setSubmissionConfig] = useState({
    delayBetweenSubmissions: 2000, // milliseconds
    maxConcurrentSubmissions: 3,
    maxRetries: 3,
    timeout: 30000, // milliseconds
    useAiReasoning: true,
    allowDuplicateSubmissions: false,
  });
  const [bulkSubmissionLoading, setBulkSubmissionLoading] = useState(false);
  const [bulkSubmissionError, setBulkSubmissionError] = useState(null);
  const [bulkSubmissionSuccess, setBulkSubmissionSuccess] = useState(null);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [loadingStatus, setLoadingStatus] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [submissionQueue, setSubmissionQueue] = useState([]);
  const [activeSubmissions, setActiveSubmissions] = useState([]);
  const [completedSubmissions, setCompletedSubmissions] = useState([]);
  const [failedSubmissions, setFailedSubmissions] = useState([]);
  const [progress, setProgress] = useState(0);
  const [submittedCombinations, setSubmittedCombinations] = useState(new Set());

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Function to fetch leads from Supabase
  const fetchLeadsFromSupabase = async () => {
    try {
      setBulkSubmissionLoading(true);
      setLoadingProgress(0);
      setLoadingStatus('Preparing to load leads...');

      // Fetch all leads from Supabase with batching and progress tracking
      const { leads, total } = await supabaseService.getAllLeads({}, 1000, (progress) => {
        setLoadingProgress(progress.progress);
        setLoadingStatus(`Loading leads: ${progress.loaded} of ${progress.total} (${progress.progress}%) - Batch ${progress.page}/${progress.totalPages}`);
      });

      setImportedLeads(leads);
      setFilteredLeads(leads);
      setBulkSubmissionSuccess(`Successfully loaded ${leads.length} leads from database.`);
    } catch (error) {
      console.error('Error fetching leads:', error);
      setBulkSubmissionError(`Failed to load leads: ${error.message}`);
    } finally {
      setBulkSubmissionLoading(false);
      setLoadingStatus('');
    }
  };

  // Load leads on component mount for bulk submission mode
  useEffect(() => {
    fetchLeadsFromSupabase();
  }, []);

  // Filter leads when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredLeads(importedLeads);
    } else {
      const lowercasedSearch = searchTerm.toLowerCase();
      const filtered = importedLeads.filter(lead =>
        (lead.first_name && lead.first_name.toLowerCase().includes(lowercasedSearch)) ||
        (lead.last_name && lead.last_name.toLowerCase().includes(lowercasedSearch)) ||
        (lead.name && lead.name.toLowerCase().includes(lowercasedSearch)) ||
        (lead.email && lead.email.toLowerCase().includes(lowercasedSearch)) ||
        (lead.phone && lead.phone.toLowerCase().includes(lowercasedSearch)) ||
        (lead.company && lead.company.toLowerCase().includes(lowercasedSearch)) ||
        (lead.website && lead.website.toLowerCase().includes(lowercasedSearch))
      );
      setFilteredLeads(filtered);
    }
  }, [searchTerm, importedLeads]);

  // Check if a website URL exists in the leads database
  const checkWebsiteInLeads = async (url) => {
    try {
      // Clean the URL for comparison
      const cleanUrl = url.replace(/^https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, '');

      // Search for leads with this website
      const matchingLeads = importedLeads.filter(lead => {
        if (!lead.website) return false;
        const leadWebsite = lead.website.replace(/^https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, '');
        return leadWebsite.includes(cleanUrl) || cleanUrl.includes(leadWebsite);
      });

      if (matchingLeads.length > 0) {
        return matchingLeads[0];
      }

      return null;
    } catch (error) {
      console.error('Error checking website in leads:', error);
      return null;
    }
  };

  // Enrich and create a lead for a website
  const enrichAndCreateLead = async (url, companyName) => {
    try {
      // Create a basic lead object
      const basicLead = {
        website: url,
        company: companyName || '',
      };

      // Enrich the lead
      const enrichedLead = await leadEnrichmentService.enrichLead(basicLead);

      // Create the lead in Supabase
      const leadId = await supabaseService.createLead(enrichedLead);

      // Fetch the created lead
      const createdLead = await supabaseService.getLeadById(leadId);

      return createdLead;
    } catch (error) {
      console.error('Error enriching and creating lead:', error);
      throw error;
    }
  };

  // Handle form discovery and submission for a single website
  const handleSingleWebsiteSubmit = async () => {
    if (!singleWebsiteUrl) {
      setSingleWebsiteError('Please enter a website URL');
      return;
    }

    setSingleWebsiteLoading(true);
    setSingleWebsiteError(null);
    setSingleWebsiteSuccess(null);
    setSingleWebsiteDiscoveredForms([]);
    setSingleWebsiteSelectedFormId(null);
    setSingleWebsiteExistingLead(null);

    try {
      // Check if the website exists in the leads database
      const existingLead = await checkWebsiteInLeads(singleWebsiteUrl);

      let leadToUse = existingLead;

      // If the website doesn't exist and enrichment is enabled, create a new lead
      if (!existingLead && singleWebsiteEnrichLead) {
        setSingleWebsiteSuccess('Website not found in leads database. Enriching and creating a new lead...');
        leadToUse = await enrichAndCreateLead(singleWebsiteUrl, singleWebsiteCompanyName);
        setSingleWebsiteSuccess(`Created new lead for ${leadToUse.company || singleWebsiteUrl}`);
      }

      setSingleWebsiteExistingLead(leadToUse);

      // Prepare lead data for submission
      const leadData = {
        name: singleWebsiteLeadData.name || (leadToUse ? `${leadToUse.first_name || ''} ${leadToUse.last_name || ''}`.trim() : ''),
        email: singleWebsiteLeadData.email || (leadToUse ? leadToUse.email : ''),
        phone: singleWebsiteLeadData.phone || (leadToUse ? leadToUse.phone : ''),
        message: singleWebsiteLeadData.message || 'I am interested in your services. Please contact me for more information.',
      };

      // Discover and submit form
      const result = await formDiscoveryService.discoverAndSubmitForm(
        singleWebsiteUrl,
        leadData,
        {
          companyName: singleWebsiteCompanyName || (leadToUse ? leadToUse.company : ''),
          industry: singleWebsiteIndustry,
          priority: singleWebsitePriority,
          maxPages: singleWebsiteMaxPages,
          maxDepth: singleWebsiteMaxDepth,
          useAiReasoning: singleWebsiteUseAiReasoning,
        }
      );

      setSingleWebsiteSuccess(`Successfully submitted form on ${singleWebsiteUrl}`);

      // If we have discovered forms, store them
      if (result.discovered_forms && result.discovered_forms.length > 0) {
        setSingleWebsiteDiscoveredForms(result.discovered_forms);
        setSingleWebsiteSelectedFormId(result.discovered_forms[0].form_id);
      }

    } catch (error) {
      console.error('Error in form discovery and submission:', error);
      setSingleWebsiteError(`Error: ${error.message}`);
    } finally {
      setSingleWebsiteLoading(false);
    }
  };

  // Prepare submission queue for bulk submission
  const prepareSubmissionQueue = useCallback(() => {
    if (selectedLeads.length === 0) {
      setBulkSubmissionError('Please select at least one lead to submit forms for.');
      return false;
    }

    if (targetWebsites.filter(site => site.selected && site.url.trim()).length === 0) {
      setBulkSubmissionError('Please enter at least one target website.');
      return false;
    }

    // Create submission queue
    const queue = [];
    const skippedDuplicates = [];

    // Get selected leads
    const leadsToSubmit = selectedLeads.map(index => filteredLeads[index]);

    // Get selected websites
    const websitesToSubmit = targetWebsites.filter(site => site.selected && site.url.trim());

    // Create combinations of leads and websites
    for (const lead of leadsToSubmit) {
      for (const website of websitesToSubmit) {
        const combinationKey = `${lead.id}-${website.url}`;

        // Skip if already submitted and duplicates not allowed
        if (submittedCombinations.has(combinationKey) && !submissionConfig.allowDuplicateSubmissions) {
          skippedDuplicates.push({ lead, website: website.url });
          continue;
        }

        // Add to queue
        queue.push({
          lead,
          website: website.url,
          status: 'queued',
          combinationKey
        });
      }
    }

    // Show warning if duplicates were skipped
    if (skippedDuplicates.length > 0) {
      setBulkSubmissionSuccess(`Skipped ${skippedDuplicates.length} duplicate submissions. Enable "Allow Duplicate Submissions" in settings to override.`);
    }

    setSubmissionQueue(queue);
    setActiveSubmissions([]);
    setCompletedSubmissions([]);
    setFailedSubmissions([]);
    setProgress(0);

    return true;
  }, [selectedLeads, filteredLeads, targetWebsites, submittedCombinations, submissionConfig.allowDuplicateSubmissions]);

  // Start the bulk submission process
  const startBulkSubmission = useCallback(async () => {
    if (!prepareSubmissionQueue()) {
      return;
    }

    setIsSubmitting(true);
    setIsPaused(false);
  }, [prepareSubmissionQueue]);

  // Pause the bulk submission process
  const pauseBulkSubmission = () => {
    setIsPaused(true);
  };

  // Resume the bulk submission process
  const resumeBulkSubmission = () => {
    setIsPaused(false);
  };

  // Stop the bulk submission process
  const stopBulkSubmission = () => {
    setIsSubmitting(false);
    setIsPaused(false);
  };

  // Process the submission queue
  useEffect(() => {
    if (!isSubmitting || isPaused || submissionQueue.length === 0) {
      return;
    }

    const processQueue = async () => {
      // Check if we've reached the maximum concurrent submissions
      if (activeSubmissions.length >= submissionConfig.maxConcurrentSubmissions) {
        return;
      }

      // Check if there are items in the queue
      if (submissionQueue.length === 0) {
        return;
      }

      // Get the next item from the queue
      const nextItem = submissionQueue[0];
      const newQueue = submissionQueue.slice(1);
      setSubmissionQueue(newQueue);

      // Add to active submissions
      const activeItem = { ...nextItem, status: 'processing', startTime: new Date() };
      setActiveSubmissions(prev => [...prev, activeItem]);

      try {
        // Prepare lead data for submission
        const leadData = {
          name: `${nextItem.lead.first_name || ''} ${nextItem.lead.last_name || ''}`.trim() || nextItem.lead.name || '',
          email: nextItem.lead.email || '',
          phone: nextItem.lead.phone || '',
          message: 'I am interested in your services. Please contact me for more information.',
        };

        // Prepare options for form discovery and submission
        const formOptions = {
          timeout: submissionConfig.timeout / 1000, // Convert to seconds
          useAiReasoning: submissionConfig.useAiReasoning,
          priority: 5,
          maxPages: 10,
          maxDepth: 3
        };

        // Submit the form
        await formDiscoveryService.discoverAndSubmitForm(
          nextItem.website,
          leadData,
          formOptions
        );

        // Add to completed submissions
        const completedItem = {
          ...activeItem,
          status: 'completed',
          endTime: new Date(),
          duration: new Date() - activeItem.startTime
        };
        setCompletedSubmissions(prev => [...prev, completedItem]);

        // Add to submitted combinations
        setSubmittedCombinations(prev => new Set([...prev, nextItem.combinationKey]));

      } catch (error) {
        console.error(`Error submitting form for ${nextItem.lead.name || nextItem.lead.email} on ${nextItem.website}:`, error);

        // Add to failed submissions
        const failedItem = {
          ...activeItem,
          status: 'failed',
          endTime: new Date(),
          duration: new Date() - activeItem.startTime,
          error: error.message
        };
        setFailedSubmissions(prev => [...prev, failedItem]);
      } finally {
        // Remove from active submissions
        setActiveSubmissions(prev => prev.filter(item => item !== activeItem));

        // Update progress
        const total = newQueue.length + activeSubmissions.length + completedSubmissions.length + failedSubmissions.length + 1;
        const completed = completedSubmissions.length + failedSubmissions.length + 1;
        setProgress(Math.floor((completed / total) * 100));
      }
    };

    // Process the queue
    const interval = setInterval(processQueue, 500);

    return () => clearInterval(interval);
  }, [
    isSubmitting,
    isPaused,
    submissionQueue,
    activeSubmissions,
    completedSubmissions,
    failedSubmissions,
    submissionConfig
  ]);

  return (
    <Box sx={{ width: '100%' }}>
      <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
        <Tab label="Single Website" />
        <Tab label="Bulk Submission" />
      </Tabs>

      {/* Single Website Mode */}
      {activeTab === 0 && (
        <Box>
          <Typography variant="h6" gutterBottom>
            Single Website Submission
          </Typography>

          <Typography variant="body2" paragraph>
            Enter a website URL to discover and submit a form. If the website is not in your leads database, a new lead will be created.
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <TextField
                label="Website URL"
                variant="outlined"
                fullWidth
                value={singleWebsiteUrl}
                onChange={(e) => setSingleWebsiteUrl(e.target.value)}
                placeholder="https://example.com"
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={singleWebsiteEnrichLead}
                    onChange={(e) => setSingleWebsiteEnrichLead(e.target.checked)}
                  />
                }
                label="Enrich & Create Lead if Not Found"
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                label="Company Name (Optional)"
                variant="outlined"
                fullWidth
                value={singleWebsiteCompanyName}
                onChange={(e) => setSingleWebsiteCompanyName(e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Industry (Optional)</InputLabel>
                <Select
                  value={singleWebsiteIndustry}
                  onChange={(e) => setSingleWebsiteIndustry(e.target.value)}
                  label="Industry (Optional)"
                >
                  <MenuItem value="">None</MenuItem>
                  <MenuItem value="construction">Construction</MenuItem>
                  <MenuItem value="healthcare">Healthcare</MenuItem>
                  <MenuItem value="education">Education</MenuItem>
                  <MenuItem value="technology">Technology</MenuItem>
                  <MenuItem value="finance">Finance</MenuItem>
                  <MenuItem value="retail">Retail</MenuItem>
                  <MenuItem value="manufacturing">Manufacturing</MenuItem>
                  <MenuItem value="services">Services</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Lead Information
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <TextField
                label="Name"
                variant="outlined"
                fullWidth
                value={singleWebsiteLeadData.name}
                onChange={(e) => setSingleWebsiteLeadData({...singleWebsiteLeadData, name: e.target.value})}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                label="Email"
                variant="outlined"
                fullWidth
                value={singleWebsiteLeadData.email}
                onChange={(e) => setSingleWebsiteLeadData({...singleWebsiteLeadData, email: e.target.value})}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                label="Phone"
                variant="outlined"
                fullWidth
                value={singleWebsiteLeadData.phone}
                onChange={(e) => setSingleWebsiteLeadData({...singleWebsiteLeadData, phone: e.target.value})}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Message"
                variant="outlined"
                fullWidth
                multiline
                rows={3}
                value={singleWebsiteLeadData.message}
                onChange={(e) => setSingleWebsiteLeadData({...singleWebsiteLeadData, message: e.target.value})}
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>

          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={singleWebsiteLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
              onClick={handleSingleWebsiteSubmit}
              disabled={singleWebsiteLoading || !singleWebsiteUrl}
            >
              {singleWebsiteLoading ? 'Processing...' : 'Discover & Submit Form'}
            </Button>
          </Box>

          {singleWebsiteError && (
            <Alert severity="error" sx={{ mt: 3 }}>
              {singleWebsiteError}
            </Alert>
          )}

          {singleWebsiteSuccess && (
            <Alert severity="success" sx={{ mt: 3 }}>
              {singleWebsiteSuccess}
            </Alert>
          )}

          {singleWebsiteExistingLead && (
            <Paper sx={{ p: 2, mt: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Using Existing Lead:
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <BusinessIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2">
                      <strong>Company:</strong> {singleWebsiteExistingLead.company || 'N/A'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <LanguageIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2">
                      <strong>Website:</strong> {singleWebsiteExistingLead.website || 'N/A'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2">
                      <strong>Email:</strong> {singleWebsiteExistingLead.email || 'N/A'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2">
                      <strong>Phone:</strong> {singleWebsiteExistingLead.phone || 'N/A'}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          )}
        </Box>
      )}

      {/* Bulk Submission Mode */}
      {activeTab === 1 && (
        <Box>
          <Typography variant="h6" gutterBottom>
            Bulk Form Submission
          </Typography>

          <Typography variant="body2" paragraph>
            Submit forms in bulk for your imported leads. Select leads, enter target websites, and start the process.
          </Typography>

          {bulkSubmissionLoading && loadingStatus && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                {loadingStatus}
              </Typography>
              <LinearProgress variant="determinate" value={loadingProgress} />
            </Box>
          )}

          {bulkSubmissionError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {bulkSubmissionError}
            </Alert>
          )}

          {bulkSubmissionSuccess && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {bulkSubmissionSuccess}
            </Alert>
          )}

          {bulkSubmissionLoading && !loadingStatus ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
              <CircularProgress />
            </Box>
          ) : importedLeads.length === 0 ? (
            <Box>
              <Alert severity="warning" sx={{ mb: 3 }}>
                No leads found. Please import leads first using the Lead Import feature.
              </Alert>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchLeadsFromSupabase}
                sx={{ mt: 2 }}
              >
                Refresh Leads
              </Button>
            </Box>
          ) : (
            <Grid container spacing={3}>
              {/* Lead Selection */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, height: '100%' }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Select Leads
                  </Typography>

                  <TextField
                    fullWidth
                    placeholder="Search leads..."
                    variant="outlined"
                    size="small"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    sx={{ mb: 2 }}
                  />

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Button
                      size="small"
                      onClick={() => setSelectedLeads(filteredLeads.map((_, index) => index))}
                    >
                      Select All ({filteredLeads.length})
                    </Button>
                    <Button
                      size="small"
                      onClick={() => setSelectedLeads([])}
                    >
                      Clear Selection
                    </Button>
                  </Box>

                  <Box sx={{ maxHeight: 300, overflow: 'auto', mb: 2 }}>
                    <List dense>
                      {filteredLeads.map((lead, index) => (
                        <ListItem
                          key={lead.id || index}
                          sx={{
                            bgcolor: selectedLeads.includes(index) ? 'action.selected' : 'transparent',
                            borderRadius: 1,
                            mb: 0.5
                          }}
                        >
                          <ListItemIcon>
                            <Checkbox
                              edge="start"
                              checked={selectedLeads.includes(index)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedLeads([...selectedLeads, index]);
                                } else {
                                  setSelectedLeads(selectedLeads.filter(i => i !== index));
                                }
                              }}
                            />
                          </ListItemIcon>
                          <ListItemText
                            primary={`${lead.first_name || ''} ${lead.last_name || ''}`.trim() || lead.name || lead.email || 'Unnamed Lead'}
                            secondary={
                              <React.Fragment>
                                {lead.company && <span>{lead.company} • </span>}
                                {lead.email && <span>{lead.email} • </span>}
                                {lead.phone && <span>{lead.phone}</span>}
                              </React.Fragment>
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>

                  <Typography variant="body2">
                    {selectedLeads.length} of {filteredLeads.length} leads selected
                  </Typography>
                </Paper>
              </Grid>

              {/* Target Websites */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, height: '100%' }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Target Websites
                  </Typography>

                  <Typography variant="body2" paragraph>
                    Enter the websites where you want to submit forms for the selected leads.
                  </Typography>

                  {targetWebsites.map((site, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Checkbox
                        checked={site.selected}
                        onChange={(e) => {
                          const newSites = [...targetWebsites];
                          newSites[index].selected = e.target.checked;
                          setTargetWebsites(newSites);
                        }}
                      />
                      <TextField
                        fullWidth
                        placeholder="https://example.com"
                        variant="outlined"
                        size="small"
                        value={site.url}
                        onChange={(e) => {
                          const newSites = [...targetWebsites];
                          newSites[index].url = e.target.value;
                          setTargetWebsites(newSites);
                        }}
                      />
                      <IconButton
                        color="error"
                        onClick={() => {
                          if (targetWebsites.length > 1) {
                            setTargetWebsites(targetWebsites.filter((_, i) => i !== index));
                          }
                        }}
                        disabled={targetWebsites.length <= 1}
                      >
                        <ErrorIcon />
                      </IconButton>
                    </Box>
                  ))}

                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => setTargetWebsites([...targetWebsites, { url: '', selected: true }])}
                    sx={{ mb: 2 }}
                  >
                    Add Website
                  </Button>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle1" gutterBottom>
                    Submission Settings
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Delay Between Submissions (ms)"
                        type="number"
                        fullWidth
                        size="small"
                        value={submissionConfig.delayBetweenSubmissions}
                        onChange={(e) => setSubmissionConfig({
                          ...submissionConfig,
                          delayBetweenSubmissions: parseInt(e.target.value)
                        })}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Max Concurrent Submissions"
                        type="number"
                        fullWidth
                        size="small"
                        value={submissionConfig.maxConcurrentSubmissions}
                        onChange={(e) => setSubmissionConfig({
                          ...submissionConfig,
                          maxConcurrentSubmissions: parseInt(e.target.value)
                        })}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={submissionConfig.useAiReasoning}
                            onChange={(e) => setSubmissionConfig({
                              ...submissionConfig,
                              useAiReasoning: e.target.checked
                            })}
                          />
                        }
                        label="Use AI Reasoning"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={submissionConfig.allowDuplicateSubmissions}
                            onChange={(e) => setSubmissionConfig({
                              ...submissionConfig,
                              allowDuplicateSubmissions: e.target.checked
                            })}
                          />
                        }
                        label="Allow Duplicate Submissions"
                      />
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>

              {/* Submission Controls */}
              <Grid item xs={12}>
                <Paper sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="subtitle1">
                      Submission Controls
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 2 }}>
                      {isSubmitting ? (
                        <>
                          {isPaused ? (
                            <Button
                              variant="contained"
                              color="primary"
                              startIcon={<PlayArrowIcon />}
                              onClick={resumeBulkSubmission}
                            >
                              Resume
                            </Button>
                          ) : (
                            <Button
                              variant="outlined"
                              color="primary"
                              startIcon={<PauseIcon />}
                              onClick={pauseBulkSubmission}
                            >
                              Pause
                            </Button>
                          )}
                          <Button
                            variant="outlined"
                            color="error"
                            startIcon={<StopIcon />}
                            onClick={stopBulkSubmission}
                          >
                            Stop
                          </Button>
                        </>
                      ) : (
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<PlayArrowIcon />}
                          onClick={startBulkSubmission}
                          disabled={bulkSubmissionLoading || selectedLeads.length === 0}
                        >
                          Start Submission
                        </Button>
                      )}
                    </Box>
                  </Box>

                  {isSubmitting && (
                    <Box sx={{ mt: 2 }}>
                      <LinearProgress variant="determinate" value={progress} sx={{ mb: 1 }} />
                      <Typography variant="body2">
                        Progress: {progress}% •
                        Queued: {submissionQueue.length} •
                        Active: {activeSubmissions.length} •
                        Completed: {completedSubmissions.length} •
                        Failed: {failedSubmissions.length}
                      </Typography>
                    </Box>
                  )}
                </Paper>
              </Grid>

              {/* Submission Results */}
              {(completedSubmissions.length > 0 || failedSubmissions.length > 0) && (
                <Grid item xs={12}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Submission Results
                    </Typography>

                    <Tabs value={0} sx={{ mb: 2 }}>
                      <Tab label={`Completed (${completedSubmissions.length})`} />
                      <Tab label={`Failed (${failedSubmissions.length})`} />
                    </Tabs>

                    <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                      <List dense>
                        {completedSubmissions.map((submission, index) => (
                          <ListItem key={index} sx={{ borderBottom: '1px solid #eee' }}>
                            <ListItemIcon>
                              <CheckCircleIcon color="success" />
                            </ListItemIcon>
                            <ListItemText
                              primary={`${submission.lead.first_name || ''} ${submission.lead.last_name || ''}`.trim() || submission.lead.name || submission.lead.email}
                              secondary={`Submitted to ${submission.website} • Duration: ${Math.round(submission.duration / 1000)}s`}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  </Paper>
                </Grid>
              )}
            </Grid>
          )}
        </Box>
      )}
    </Box>
  );
};

export default FormDiscoveryAndSubmissionPanel;
