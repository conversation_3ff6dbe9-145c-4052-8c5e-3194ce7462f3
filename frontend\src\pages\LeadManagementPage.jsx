import React from 'react';
import { Box } from '@mui/material';
import PageHeader from '../components/common/PageHeader';
import LeadManagement from '../components/LeadManagement';
import { People as PeopleIcon } from '@mui/icons-material';

/**
 * Lead Management Page
 *
 * Comprehensive lead management with audit reports and outreach messages.
 */
const LeadManagementPage = () => {
  return (
    <Box>
      <PageHeader
        title="Lead Management"
        description="View and manage all leads, audit reports, and outreach messages"
        breadcrumbs={[
          { label: 'Lead Management', icon: <PeopleIcon fontSize="inherit" /> }
        ]}
      />

      <LeadManagement />
    </Box>
  );
};

export default LeadManagementPage;
