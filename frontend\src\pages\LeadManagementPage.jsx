import React from 'react';
import { Box, Paper } from '@mui/material';
import PageHeader from '../components/common/PageHeader';
import LeadManagementPanel from '../components/LeadManagement/LeadManagementPanel';
import { People as PeopleIcon } from '@mui/icons-material';

/**
 * Lead Management Page
 * 
 * This page displays the Lead Management Panel.
 */
const LeadManagementPage = () => {
  return (
    <Box>
      <PageHeader
        title="Lead Management"
        description="Manage your leads and track their status"
        breadcrumbs={[
          { label: 'Lead Management', icon: <PeopleIcon fontSize="inherit" /> }
        ]}
      />
      
      <Paper sx={{ p: 3 }}>
        <LeadManagementPanel />
      </Paper>
    </Box>
  );
};

export default LeadManagementPage;
