"""
Mock server for the Form Discovery & Submission Service.
"""
import json
import uuid
from http.server import BaseHTTPRequestHandler, HTTPServer
from urllib.parse import parse_qs, urlparse

# Mock data
discovered_forms = {}
submission_results = {}

class MockHandler(BaseHTTPRequestHandler):
    """
    Mock HTTP handler for the Form Discovery & Submission Service.
    """
    
    def _set_headers(self, status_code=200, content_type="application/json"):
        """Set response headers."""
        self.send_response(status_code)
        self.send_header("Content-type", content_type)
        self.send_header("Access-Control-Allow-Origin", "*")
        self.send_header("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
        self.send_header("Access-Control-Allow-Headers", "Content-Type")
        self.end_headers()
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests."""
        self._set_headers()
    
    def do_GET(self):
        """Handle GET requests."""
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        
        if path.startswith("/api/v1/discover/"):
            # Get discovery status
            job_id = path.split("/")[-1]
            if job_id in discovered_forms:
                self._set_headers()
                self.wfile.write(json.dumps({
                    "job_id": job_id,
                    "status": "completed",
                    "forms_discovered": 1,
                    "completed": True,
                    "error": None
                }).encode())
            else:
                self._set_headers(404)
                self.wfile.write(json.dumps({
                    "detail": f"Job {job_id} not found"
                }).encode())
        
        elif path.startswith("/api/v1/submit/"):
            # Get submission status
            job_id = path.split("/")[-1]
            if job_id in submission_results:
                self._set_headers()
                self.wfile.write(json.dumps({
                    "job_id": job_id,
                    "url": submission_results[job_id]["url"],
                    "form_id": submission_results[job_id]["form_id"],
                    "fields_filled": submission_results[job_id]["fields_filled"],
                    "submitted": True,
                    "status": "completed",
                    "error": None,
                    "processing_time": 1.5,
                    "submission_timestamp": "2023-05-05T12:00:00",
                    "metadata": {
                        "confidence_score": 0.85,
                        "critical_fields": ["name", "email"],
                        "optional_fields": ["phone", "message"]
                    }
                }).encode())
            else:
                self._set_headers(404)
                self.wfile.write(json.dumps({
                    "detail": f"Job {job_id} not found"
                }).encode())
        
        elif path == "/health":
            # Health check
            self._set_headers()
            self.wfile.write(json.dumps({
                "status": "healthy"
            }).encode())
        
        else:
            # Not found
            self._set_headers(404)
            self.wfile.write(json.dumps({
                "detail": "Not found"
            }).encode())
    
    def do_POST(self):
        """Handle POST requests."""
        content_length = int(self.headers["Content-Length"])
        post_data = self.rfile.read(content_length).decode("utf-8")
        
        try:
            data = json.loads(post_data)
        except json.JSONDecodeError:
            self._set_headers(400)
            self.wfile.write(json.dumps({
                "detail": "Invalid JSON"
            }).encode())
            return
        
        if self.path == "/api/v1/discover":
            # Discover forms
            job_id = str(uuid.uuid4())
            discovered_forms[job_id] = {
                "url": data["url"],
                "forms": [
                    {
                        "form_id": f"form_{hash(data['url']) % 10000}",
                        "url": data["url"],
                        "title": "Contact Form",
                        "description": "Send us a message",
                        "fields": [
                            {"name": "name", "field_type": "text", "required": True},
                            {"name": "email", "field_type": "email", "required": True},
                            {"name": "phone", "field_type": "tel", "required": False},
                            {"name": "message", "field_type": "textarea", "required": True}
                        ],
                        "multi_step": False,
                        "has_captcha": False,
                        "submission_endpoint": "/contact",
                        "method": "POST"
                    }
                ]
            }
            
            self._set_headers()
            self.wfile.write(json.dumps({
                "job_id": job_id,
                "status": "processing",
                "message": f"Form discovery started for {data['url']}"
            }).encode())
        
        elif self.path == "/api/v1/submit":
            # Submit form
            job_id = str(uuid.uuid4())
            submission_results[job_id] = {
                "url": data["url"],
                "form_id": data.get("form_id", f"form_{hash(data['url']) % 10000}"),
                "fields_filled": len(data["lead_data"])
            }
            
            self._set_headers()
            self.wfile.write(json.dumps({
                "job_id": job_id,
                "status": "processing",
                "message": f"Form submission started for {data['url']}"
            }).encode())
        
        else:
            # Not found
            self._set_headers(404)
            self.wfile.write(json.dumps({
                "detail": "Not found"
            }).encode())

def run_server(port=8001):
    """Run the mock server."""
    server_address = ("", port)
    httpd = HTTPServer(server_address, MockHandler)
    print(f"Starting mock server on port {port}...")
    httpd.serve_forever()

if __name__ == "__main__":
    run_server()
