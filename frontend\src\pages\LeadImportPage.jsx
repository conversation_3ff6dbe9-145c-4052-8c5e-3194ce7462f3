import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Breadcrumbs,
  Link,
  Paper,
  Tabs,
  Tab,
  Button,
  Divider,
  Alert,
  CircularProgress,
  Snackbar,
} from '@mui/material';
import {
  Home as HomeIcon,
  CloudUpload as UploadIcon,
  List as ListIcon,
  Delete as DeleteIcon,
  CloudDone as CloudDoneIcon,
  Save as SaveIcon,
  Business as BusinessIcon,
  Language as LanguageIcon,
} from '@mui/icons-material';
import CSVImportProcessor from '../components/LeadImport';
import SingleCompanyImporter from '../components/LeadImport/SingleCompanyImporter';

/**
 * Lead Import Page
 *
 * This page allows users to import leads from CSV files and manage imported leads.
 */
const LeadImportPage = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [importedLeads, setImportedLeads] = useState([]);
  const [allLeads, setAllLeads] = useState([]);
  const [leadCount, setLeadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(null);
  const [error, setError] = useState(null);
  const [leadsInDatabase, setLeadsInDatabase] = useState(false);
  const [checkingDatabase, setCheckingDatabase] = useState(false);

  // Load imported leads from localStorage
  useEffect(() => {
    try {
      const savedLeads = localStorage.getItem('importedLeads');
      if (savedLeads) {
        const parsedLeads = JSON.parse(savedLeads);
        setImportedLeads(parsedLeads.slice(0, 10)); // Show only first 10 for preview
        setAllLeads(parsedLeads); // Store all leads
        setLeadCount(parsedLeads.length);

        // Check if leads are in database when tab changes to Imported Leads
        if (activeTab === 1) {
          checkLeadsInDatabase(parsedLeads);
        }
      }
    } catch (error) {
      console.error('Error loading imported leads:', error);
    }
  }, [activeTab]);

  // Check if leads are already in the database
  const checkLeadsInDatabase = async (leads) => {
    if (!leads || leads.length === 0) return;

    try {
      setCheckingDatabase(true);

      // Import supabaseService
      const supabaseService = (await import('../services/supabaseService')).default;

      // Check if any leads with the same email exist in the database
      // This is a simple check - in a real app, you might want to do a more sophisticated check
      const emails = leads.map(lead => lead.email).filter(Boolean);

      if (emails.length === 0) {
        setLeadsInDatabase(false);
        setCheckingDatabase(false);
        return;
      }

      // Get leads from database
      const result = await supabaseService.getLeads({
        search: emails[0] // Just check the first email as a simple test
      });

      console.log('Database check result:', result);

      // If we found any leads with the same email, assume they're already in the database
      setLeadsInDatabase(result.leads.length > 0);
      setCheckingDatabase(false);
    } catch (error) {
      console.error('Error checking leads in database:', error);
      setCheckingDatabase(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle clear all leads
  const handleClearLeads = () => {
    if (window.confirm('Are you sure you want to delete all imported leads? This action cannot be undone.')) {
      localStorage.removeItem('importedLeads');
      setImportedLeads([]);
      setAllLeads([]);
      setLeadCount(0);
    }
  };

  // Handle upload to Supabase
  const handleUploadToSupabase = async () => {
    try {
      setLoading(true);
      setError(null);

      // Import supabaseService
      const supabaseService = (await import('../services/supabaseService')).default;

      // Create batch info
      const batchInfo = {
        name: 'Manual Upload',
        fileName: 'manual_upload.csv',
        mapping: {},
        settings: {}
      };

      console.log('Uploading leads to Supabase:', allLeads);

      // Save to Supabase
      const result = await supabaseService.createLeads(allLeads, batchInfo);

      console.log('Supabase upload result:', result);

      setSuccess(`Successfully uploaded ${result.leads.length} leads to database.`);
      setLoading(false);
    } catch (error) {
      console.error('Error uploading leads to Supabase:', error);
      setError(`Error uploading leads: ${error.message}`);
      setLoading(false);
    }
  };

  // Handle success message close
  const handleSuccessClose = () => {
    setSuccess(null);
  };

  // Handle error message close
  const handleErrorClose = () => {
    setError(null);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 3 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            underline="hover"
            color="inherit"
            href="/"
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </Link>
          <Typography
            sx={{ display: 'flex', alignItems: 'center' }}
            color="text.primary"
          >
            <UploadIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Lead Import
          </Typography>
        </Breadcrumbs>

        <Typography variant="h4" gutterBottom>
          Lead Import
        </Typography>

        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab icon={<UploadIcon />} label="CSV Import" />
            <Tab icon={<BusinessIcon />} label="Single Company" />
            <Tab
              icon={<ListIcon />}
              label={`Imported Leads (${leadCount})`}
              disabled={leadCount === 0}
            />
          </Tabs>

          <Box sx={{ p: 0 }}>
            {activeTab === 0 && (
              <Box>
                <CSVImportProcessor />
              </Box>
            )}

            {activeTab === 1 && (
              <Box sx={{ p: 3 }}>
                <SingleCompanyImporter />
              </Box>
            )}

            {activeTab === 2 && (
              <Box sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    Imported Leads
                  </Typography>

                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      color={leadsInDatabase ? "warning" : "primary"}
                      startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <CloudDoneIcon />}
                      onClick={handleUploadToSupabase}
                      disabled={loading || leadCount === 0 || checkingDatabase}
                    >
                      {leadsInDatabase ? "Re-Upload to Database" : "Upload to Database"}
                    </Button>
                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<DeleteIcon />}
                      onClick={handleClearLeads}
                      disabled={loading}
                    >
                      Clear All Leads
                    </Button>
                  </Box>
                </Box>

                <Divider sx={{ mb: 2 }} />

                {/* Success and Error Messages */}
                <Snackbar
                  open={!!success}
                  autoHideDuration={6000}
                  onClose={handleSuccessClose}
                  message={success}
                  anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
                />

                <Snackbar
                  open={!!error}
                  autoHideDuration={6000}
                  onClose={handleErrorClose}
                  message={error}
                  anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
                />

                {leadCount > 0 ? (
                  <>
                    <Alert severity="info" sx={{ mb: 2 }}>
                      Showing {importedLeads.length} of {leadCount} imported leads.
                    </Alert>

                    {checkingDatabase && (
                      <Alert severity="info" sx={{ mb: 2 }}>
                        Checking if leads are already in the database...
                      </Alert>
                    )}

                    {!checkingDatabase && leadsInDatabase && (
                      <Alert severity="warning" sx={{ mb: 2 }}>
                        Some of these leads appear to already be in the database. Uploading again may create duplicates.
                      </Alert>
                    )}

                    <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Available Actions
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        <Button variant="contained" color="primary" size="small">
                          Use for Form Discovery
                        </Button>
                        <Button variant="outlined" size="small">
                          Export as CSV
                        </Button>
                        <Button variant="outlined" size="small">
                          Filter Leads
                        </Button>
                      </Box>
                    </Paper>

                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Lead Preview
                      </Typography>
                      <Box sx={{ overflowX: 'auto' }}>
                        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                          <thead>
                            <tr>
                              {Object.keys(importedLeads[0] || {}).map((key, index) => (
                                <th key={index} style={{
                                  textAlign: 'left',
                                  padding: '8px',
                                  borderBottom: '1px solid #ddd',
                                  backgroundColor: '#f5f5f5'
                                }}>
                                  {key}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {importedLeads.map((lead, rowIndex) => (
                              <tr key={rowIndex}>
                                {Object.values(lead).map((value, cellIndex) => (
                                  <td key={cellIndex} style={{
                                    padding: '8px',
                                    borderBottom: '1px solid #eee'
                                  }}>
                                    {value || '-'}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </Box>
                    </Paper>
                  </>
                ) : (
                  <Alert severity="info">
                    No leads have been imported yet. Go to the Import Leads tab to import leads.
                  </Alert>
                )}
              </Box>
            )}
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default LeadImportPage;
