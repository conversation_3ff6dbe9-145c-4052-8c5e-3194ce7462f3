from enum import Enum
from typing import List, Dict, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, HttpUrl, EmailStr


class ResponseChannel(str, Enum):
    EMAIL = "email"
    SMS = "sms"
    VOICE = "voice"
    CHAT = "chat"
    FORM = "form"


class ResponseStatus(str, Enum):
    RECEIVED = "received"
    PROCESSING = "processing"
    ANALYZED = "analyzed"
    ARCHIVED = "archived"


class FormStatus(str, Enum):
    DISCOVERED = "discovered"
    ANALYZED = "analyzed"
    SUBMISSION_PENDING = "submission_pending"
    SUBMITTED = "submitted"
    FAILED = "failed"


class FieldType(str, Enum):
    TEXT = "text"
    EMAIL = "email"
    PHONE = "phone"
    NAME = "name"
    ADDRESS = "address"
    COMPANY = "company"
    MESSAGE = "message"
    CHECKBOX = "checkbox"
    RADIO = "radio"
    SELECT = "select"
    DATE = "date"
    FILE = "file"
    CAPTCHA = "captcha"
    SUBMIT = "submit"
    UNKNOWN = "unknown"


class FormField(BaseModel):
    field_id: str
    name: Optional[str] = None
    label: Optional[str] = None
    field_type: FieldType
    required: bool = False
    options: Optional[List[str]] = None
    validation_pattern: Optional[str] = None
    default_value: Optional[str] = None
    placeholder: Optional[str] = None


class FormMetadata(BaseModel):
    form_id: str
    url: HttpUrl
    title: Optional[str] = None
    description: Optional[str] = None
    fields: List[FormField]
    multi_step: bool = False
    has_captcha: bool = False
    submission_endpoint: Optional[str] = None
    method: str = "POST"
    status: FormStatus = FormStatus.DISCOVERED
    discovered_at: datetime = Field(default_factory=datetime.utcnow)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    screenshot_path: Optional[str] = None
    success_indicators: Optional[List[str]] = None
    error_indicators: Optional[List[str]] = None


class Lead(BaseModel):
    lead_id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    company: Optional[str] = None
    job_title: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    country: Optional[str] = None
    source: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    tags: List[str] = []
    custom_fields: Dict[str, Any] = {}
    consent: bool = False
    consent_timestamp: Optional[datetime] = None
    notes: Optional[str] = None


class FormSubmission(BaseModel):
    submission_id: str
    form_id: str
    lead_id: str
    url: HttpUrl
    submitted_data: Dict[str, Any]
    status: FormStatus
    submitted_at: datetime = Field(default_factory=datetime.utcnow)
    response_received: bool = False
    response_time: Optional[float] = None
    screenshot_before_path: Optional[str] = None
    screenshot_after_path: Optional[str] = None
    success: bool = False
    error_message: Optional[str] = None


class Response(BaseModel):
    response_id: str
    lead_id: str
    submission_id: Optional[str] = None
    channel: ResponseChannel
    content: str
    received_at: datetime = Field(default_factory=datetime.utcnow)
    sender_info: Dict[str, Any] = {}
    status: ResponseStatus = ResponseStatus.RECEIVED
    sentiment_score: Optional[float] = None
    quality_score: Optional[float] = None
    metadata: Dict[str, Any] = {}


class Conversation(BaseModel):
    conversation_id: str
    lead_id: str
    responses: List[str]  # List of response_ids
    started_at: datetime = Field(default_factory=datetime.utcnow)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    status: str = "active"
    channel: ResponseChannel
    metadata: Dict[str, Any] = {}
    tags: List[str] = []
    assigned_to: Optional[str] = None


class AnalyticsMetric(BaseModel):
    metric_id: str
    name: str
    value: Union[float, int, str]
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    dimensions: Dict[str, str] = {}
    metadata: Dict[str, Any] = {}


class AIModelType(str, Enum):
    FORM_DETECTION = "form_detection"
    FIELD_CLASSIFICATION = "field_classification"
    DECISION_MAKING = "decision_making"
    TEXT_GENERATION = "text_generation"
    CAPTCHA_SOLVER = "captcha_solver"


class AIModelRequest(BaseModel):
    request_id: str
    model_type: AIModelType
    input_data: Dict[str, Any]
    context: Dict[str, Any] = {}
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class AIModelResponse(BaseModel):
    response_id: str
    request_id: str
    model_type: AIModelType
    output_data: Dict[str, Any]
    confidence: float
    processing_time: float
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = {}
