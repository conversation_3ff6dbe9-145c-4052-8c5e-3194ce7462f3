# Unified AI-Powered Lead Generation System

A comprehensive platform that combines form discovery, submission, response monitoring, and AI-powered analytics to optimize lead generation and conversion.

## Features

- **Form Discovery & Submission**: Automatically find and submit contact forms across the web
- **Response Monitoring**: Track and analyze multi-channel lead responses
- **AI-Powered Analytics**: Generate insights and optimize conversion outcomes
- **Scalable Architecture**: Microservices-based design for independent scaling

## System Architecture

The system is built using a microservices architecture with the following components:

### Core Services

1. **Form Discovery Service**: Crawls websites to find contact forms using multiple strategies
2. **Form Analysis Service**: Analyzes form structure, fields, and validation patterns
3. **Lead Management Service**: Manages lead data and deduplication
4. **Response Ingestion Service**: Collects responses from multiple channels
5. **Conversation Management Service**: Threads responses into conversations
6. **AI Orchestrator Service**: Coordinates AI models for various tasks
7. **Analytics Service**: Provides insights and metrics

### Infrastructure

- **API Gateway**: Routes requests to appropriate services
- **Event Bus**: Kafka for event-driven communication
- **Databases**: PostgreSQL, MongoDB, Redis, ClickHouse
- **Frontend**: React-based admin dashboard

## Project Structure

```
/
├── services/                       # Microservices
│   ├── form-discovery/             # Form discovery service
│   ├── form-analysis/              # Form analysis service
│   ├── lead-management/            # Lead management service
│   ├── response-ingestion/         # Response ingestion service
│   ├── conversation-management/    # Conversation management service
│   ├── ai-orchestrator/            # AI orchestrator service
│   └── analytics/                  # Analytics service
├── libs/                           # Shared utilities
│   ├── interfaces/                 # Shared data models and contracts
│   ├── auth/                       # Authentication utilities
│   ├── logging/                    # Logging utilities
│   └── ml-models/                  # Shared ML models
├── infra/                          # Infrastructure as code
│   ├── terraform/                  # Terraform configurations
│   └── k8s/                        # Kubernetes manifests
├── gateway/                        # API gateway
└── frontend/                       # React-based admin panel
    ├── src/
    │   ├── components/             # React components
    │   ├── pages/                  # Page components
    │   ├── services/               # API services
    │   ├── utils/                  # Utility functions
    │   └── styles/                 # CSS styles
    └── public/                     # Static assets
```

## Getting Started

### Prerequisites

- Node.js 16+
- Python 3.9+
- Docker and Docker Compose
- Kubernetes (for production deployment)

### Development Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/lead-generation-system.git
   cd lead-generation-system
   ```

2. Install dependencies:
   ```bash
   # Frontend dependencies
   cd frontend
   npm install

   # Backend dependencies
   cd ../services
   pip install -r requirements.txt
   ```

3. Start the development environment with Docker Compose:
   ```bash
   docker-compose up -d
   ```

4. Access the application:
   - Frontend: http://localhost:3000
   - API Gateway: http://localhost:8000
   - Swagger UI: http://localhost:8000/docs

### Running Tests

```bash
# Frontend tests
cd frontend
npm test

# Backend tests
cd services/form-discovery
pytest
```

## Deployment

### Local Deployment with Docker Compose

```bash
docker-compose up -d
```

### Production Deployment with Kubernetes

1. Set up a Kubernetes cluster (e.g., using EKS, GKE, or AKS)
2. Apply the Kubernetes manifests:
   ```bash
   kubectl apply -f infra/k8s/namespace.yaml
   kubectl apply -f infra/k8s/
   ```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Commit your changes: `git commit -am 'Add some feature'`
4. Push to the branch: `git push origin feature/your-feature-name`
5. Submit a pull request

## License

MIT
