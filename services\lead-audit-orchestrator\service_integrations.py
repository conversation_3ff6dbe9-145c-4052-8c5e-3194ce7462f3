"""
Service Integration Layer

This module provides integration with all the existing microservices:
- Lead Management Service (Supabase integration)
- Form Discovery & Submission Service
- Response Monitoring Service
- Analytics Service (Report Generation)
- Lead Enrichment Service
"""

import os
import httpx
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class ServiceIntegration:
    """Integration layer for all microservices"""
    
    def __init__(self):
        # Service URLs from environment variables
        self.form_discovery_url = os.getenv("FORM_DISCOVERY_URL", "http://localhost:8001")
        self.lead_management_url = os.getenv("LEAD_MANAGEMENT_URL", "http://localhost:8003")
        self.response_monitoring_url = os.getenv("RESPONSE_MONITORING_URL", "http://localhost:8002")
        self.analytics_url = os.getenv("ANALYTICS_URL", "http://localhost:8007")
        
        # Supabase configuration
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_service_key = os.getenv("SUPABASE_SERVICE_KEY")
        
        # HTTP client with timeout
        self.client = httpx.AsyncClient(timeout=30.0)
        
        logger.info("Service integration initialized")
    
    async def close(self):
        """Close HTTP client"""
        await self.client.aclose()
    
    # Lead Management Integration
    async def get_leads_for_audit(self, limit: int = 100, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Fetch leads from Supabase that are ready for audit
        
        Args:
            limit: Maximum number of leads to fetch
            filters: Additional filters for lead selection
            
        Returns:
            List of lead records
        """
        try:
            # TODO: Implement direct Supabase integration or call lead management service
            # For now, return mock data
            mock_leads = []
            for i in range(min(limit, 10)):
                mock_leads.append({
                    "id": f"lead_{i}",
                    "first_name": f"John{i}",
                    "last_name": f"Doe{i}",
                    "email": f"john.doe{i}@example.com",
                    "phone": f"555-000-{i:04d}",
                    "company": f"Company {i}",
                    "website": f"https://company{i}.com",
                    "status": "new",
                    "enriched": i % 3 == 0,  # Some leads already enriched
                    "form_submitted": False
                })
            
            logger.info(f"Fetched {len(mock_leads)} leads for audit")
            return mock_leads
            
        except Exception as e:
            logger.error(f"Error fetching leads: {e}")
            return []
    
    async def update_lead_status(self, lead_id: str, status: str, metadata: Dict[str, Any] = None):
        """Update lead status in the database"""
        try:
            # TODO: Call lead management service or update Supabase directly
            logger.info(f"Updated lead {lead_id} status to {status}")
            
        except Exception as e:
            logger.error(f"Error updating lead status: {e}")
    
    # Lead Enrichment Integration
    async def enrich_lead(self, lead_id: str, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enrich lead data using the enrichment service
        
        Args:
            lead_id: Lead identifier
            lead_data: Current lead data
            
        Returns:
            Enriched lead data
        """
        try:
            # Check if enrichment is needed
            if lead_data.get("enriched", False):
                logger.info(f"Lead {lead_id} already enriched, skipping")
                return lead_data
            
            # TODO: Call actual enrichment service
            # For now, simulate enrichment
            await asyncio.sleep(2)  # Simulate processing time
            
            enriched_data = lead_data.copy()
            enriched_data.update({
                "enriched": True,
                "last_enriched_at": datetime.utcnow().isoformat(),
                "facebook_pixel": True,  # Mock enrichment result
                "linkedin": f"https://linkedin.com/company/{lead_data.get('company', '').lower().replace(' ', '-')}",
                "enrichment_score": 85
            })
            
            logger.info(f"Enriched lead {lead_id}")
            return enriched_data
            
        except Exception as e:
            logger.error(f"Error enriching lead {lead_id}: {e}")
            return lead_data
    
    # Form Discovery & Submission Integration
    async def submit_form(self, lead_id: str, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Submit form for a lead using the form discovery service
        
        Args:
            lead_id: Lead identifier
            lead_data: Lead data for form submission
            
        Returns:
            Submission result
        """
        try:
            website = lead_data.get("website")
            if not website:
                raise ValueError(f"No website found for lead {lead_id}")
            
            # Prepare form submission data
            submission_data = {
                "url": website,
                "lead_data": {
                    "name": f"{lead_data.get('first_name', '')} {lead_data.get('last_name', '')}".strip(),
                    "email": lead_data.get("email", ""),
                    "phone": lead_data.get("phone", ""),
                    "message": "I am interested in your services. Please contact me for more information."
                },
                "options": {
                    "timeout": 30,
                    "useAiReasoning": True,
                    "priority": 5,
                    "maxPages": 10,
                    "maxDepth": 3
                }
            }
            
            # Call form discovery service
            response = await self.client.post(
                f"{self.form_discovery_url}/api/v1/discover-and-submit",
                json=submission_data
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"Form submitted for lead {lead_id} at {website}")
                
                # Update lead status
                await self.update_lead_status(lead_id, "form_submitted", {
                    "form_submitted_at": datetime.utcnow().isoformat(),
                    "submission_job_id": result.get("job_id"),
                    "website": website
                })
                
                return {
                    "success": True,
                    "job_id": result.get("job_id"),
                    "website": website,
                    "submitted_at": datetime.utcnow().isoformat()
                }
            else:
                raise Exception(f"Form submission failed with status {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error submitting form for lead {lead_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "website": lead_data.get("website"),
                "submitted_at": datetime.utcnow().isoformat()
            }
    
    # Response Monitoring Integration
    async def start_response_monitoring(self, lead_id: str, lead_data: Dict[str, Any], 
                                      monitoring_hours: int = 48) -> Dict[str, Any]:
        """
        Start response monitoring for a lead
        
        Args:
            lead_id: Lead identifier
            lead_data: Lead data
            monitoring_hours: Hours to monitor for responses
            
        Returns:
            Monitoring configuration
        """
        try:
            monitoring_config = {
                "lead_id": lead_id,
                "email": lead_data.get("email"),
                "phone": lead_data.get("phone"),
                "company": lead_data.get("company"),
                "website": lead_data.get("website"),
                "monitoring_start": datetime.utcnow().isoformat(),
                "monitoring_end": (datetime.utcnow() + timedelta(hours=monitoring_hours)).isoformat(),
                "channels": ["email", "sms", "phone"]
            }
            
            # TODO: Call response monitoring service
            # For now, just log the start
            logger.info(f"Started response monitoring for lead {lead_id} for {monitoring_hours} hours")
            
            return monitoring_config
            
        except Exception as e:
            logger.error(f"Error starting response monitoring for lead {lead_id}: {e}")
            return {"error": str(e)}
    
    async def check_responses(self, lead_id: str) -> List[Dict[str, Any]]:
        """
        Check for responses received for a lead
        
        Args:
            lead_id: Lead identifier
            
        Returns:
            List of responses
        """
        try:
            # TODO: Call response monitoring service
            # For now, simulate response check
            import random
            
            # 30% chance of having a response
            if random.random() < 0.3:
                response_time = datetime.utcnow() - timedelta(
                    minutes=random.randint(15, 1440)  # 15 minutes to 24 hours
                )
                
                channels = ["email", "sms", "phone"]
                channel = random.choice(channels)
                
                return [{
                    "response_id": f"resp_{lead_id}_{int(response_time.timestamp())}",
                    "lead_id": lead_id,
                    "channel": channel,
                    "received_at": response_time.isoformat(),
                    "content": f"Thank you for your inquiry. We'll get back to you soon.",
                    "sender_info": {"company": "Target Company"},
                    "response_time_minutes": random.randint(15, 1440)
                }]
            
            return []  # No responses
            
        except Exception as e:
            logger.error(f"Error checking responses for lead {lead_id}: {e}")
            return []
    
    async def check_early_disqualification(self, lead_id: str, form_submitted_at: datetime) -> bool:
        """
        Check if lead should be disqualified due to response within 10 minutes
        
        Args:
            lead_id: Lead identifier
            form_submitted_at: When the form was submitted
            
        Returns:
            True if lead should be disqualified
        """
        try:
            responses = await self.check_responses(lead_id)
            
            for response in responses:
                response_time = datetime.fromisoformat(response["received_at"].replace("Z", "+00:00"))
                time_diff = response_time - form_submitted_at
                
                # Check if response was within 10 minutes and via call/SMS
                if (time_diff.total_seconds() < 600 and  # 10 minutes
                    response["channel"] in ["phone", "sms"]):
                    logger.info(f"Lead {lead_id} disqualified - {response['channel']} response within 10 minutes")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking early disqualification for lead {lead_id}: {e}")
            return False
    
    # Analytics & Report Generation Integration
    async def generate_audit_report(self, lead_id: str, lead_data: Dict[str, Any], 
                                  responses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate audit report for a lead
        
        Args:
            lead_id: Lead identifier
            lead_data: Lead data
            responses: List of responses received
            
        Returns:
            Generated report data
        """
        try:
            # TODO: Call analytics service for report generation
            # For now, simulate report generation
            
            if not responses:
                # No response case
                report = {
                    "lead_id": lead_id,
                    "company": lead_data.get("company"),
                    "website": lead_data.get("website"),
                    "form_submitted_at": lead_data.get("form_submitted_at"),
                    "monitoring_completed_at": datetime.utcnow().isoformat(),
                    "total_score": 0,
                    "time_score": 0,
                    "channel_bonus": 0,
                    "grade": "F",
                    "feedback": "⚫ Broken. Your sales process is a crime scene. Leads die here.",
                    "punchline": "This is the equivalent of paying $200 for a lead and then ghosting them.",
                    "response_time": "NO_RESPONSE",
                    "channel": "NO_RESPONSE",
                    "qualified": True  # Qualified for our service since they didn't respond quickly
                }
            else:
                # Calculate scores based on first response
                first_response = responses[0]
                response_time_minutes = first_response.get("response_time_minutes", 1440)
                channel = first_response.get("channel", "email")
                
                # Calculate time score
                time_score = self._calculate_time_score(response_time_minutes)
                channel_bonus = self._calculate_channel_bonus(channel)
                total_score = time_score + channel_bonus
                
                grade, feedback = self._get_grade(total_score)
                punchline = self._get_punchline(total_score)
                
                report = {
                    "lead_id": lead_id,
                    "company": lead_data.get("company"),
                    "website": lead_data.get("website"),
                    "form_submitted_at": lead_data.get("form_submitted_at"),
                    "monitoring_completed_at": datetime.utcnow().isoformat(),
                    "total_score": total_score,
                    "time_score": time_score,
                    "channel_bonus": channel_bonus,
                    "grade": grade,
                    "feedback": feedback,
                    "punchline": punchline,
                    "response_time": f"{response_time_minutes} minutes",
                    "channel": channel,
                    "qualified": response_time_minutes > 10 or channel not in ["phone", "sms"]
                }
            
            logger.info(f"Generated audit report for lead {lead_id} - Grade: {report['grade']}")
            return report
            
        except Exception as e:
            logger.error(f"Error generating audit report for lead {lead_id}: {e}")
            return {"error": str(e)}
    
    def _calculate_time_score(self, delay_minutes: float) -> int:
        """Calculate time score based on response delay"""
        if delay_minutes < 5:
            return 100
        elif delay_minutes < 10:
            return 80
        elif delay_minutes < 30:
            return 60
        elif delay_minutes < 60:
            return 40
        elif delay_minutes < 180:
            return 25
        elif delay_minutes < 1440:
            return 10
        else:
            return 5
    
    def _calculate_channel_bonus(self, channel: str) -> int:
        """Calculate channel bonus"""
        channel = channel.lower()
        if "phone" in channel or "call" in channel:
            return 25
        elif "sms" in channel or "text" in channel:
            return 15
        elif "email" in channel:
            return 5
        else:
            return 0
    
    def _get_grade(self, total_score: int) -> tuple:
        """Get grade and feedback"""
        if total_score >= 115:
            return "A+", "🟢 Elite Operator — You dominate your market. Stay sharp."
        elif total_score >= 100:
            return "A", "🟢 Strong but vulnerable — One slip and someone better eats your lunch."
        elif total_score >= 80:
            return "B", "🟠 You're average. Leads are slipping past you daily. Fix it or fall."
        elif total_score >= 60:
            return "C", "🔴 You're slow. You lose to the first responder — always."
        elif total_score >= 30:
            return "D", "🔴 You're wasting ad spend. This is costing you thousands/month."
        else:
            return "F", "⚫ Broken. Your sales process is a crime scene. Leads die here."
    
    def _get_punchline(self, total_score: int) -> str:
        """Get punchline based on score"""
        if total_score < 30:
            return "This is the equivalent of paying $200 for a lead and then ghosting them."
        elif total_score < 60:
            return "Your response speed is costing you 6 out of every 10 deals."
        elif total_score < 80:
            return "If you were in a race with your competitors, you'd be in the parking lot while they're cashing checks."
        elif total_score < 100:
            return "You're not slow. You're invisible."
        else:
            return "You're in the top tier of responders. Keep it up!"

# Global service integration instance
service_integration = ServiceIntegration()
