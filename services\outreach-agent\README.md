# Outreach Agent Service

The Outreach Agent Service generates personalized cold emails and LinkedIn messages based on lead audit reports. It uses OpenAI's GPT-4 to create compelling, data-driven outreach content that references specific audit findings.

## Features

- **AI-Powered Message Generation**: Uses GPT-4 to create personalized outreach messages
- **Multiple Message Types**: Supports both email and LinkedIn message generation
- **Audit-Based Personalization**: References specific audit findings (response time, grade, Facebook pixel status)
- **Tone Customization**: Professional, friendly, or direct tone options
- **Database Integration**: Stores generated messages in Supabase
- **Lead State Tracking**: Updates lead progression through the outreach pipeline

## API Endpoints

### Generate Outreach
```
POST /generate-outreach
```

Generate personalized outreach messages for a lead based on their audit report.

**Request Body:**
```json
{
  "lead_id": "uuid",
  "audit_report_id": "uuid", 
  "message_types": ["email", "linkedin"],
  "tone": "professional",
  "include_audit_link": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Generated 2 outreach messages",
  "generated_messages": [
    {
      "id": "uuid",
      "message_type": "email",
      "subject_line": "Quick question about <PERSON>c<PERSON>'s lead response",
      "message_body": "<PERSON> <PERSON>,\n\nI noticed Acme Roofing is responding to leads in about 36 minutes...",
      "status": "draft"
    }
  ],
  "lead_id": "uuid",
  "audit_report_id": "uuid"
}
```

### Health Check
```
GET /health
```

Returns service health status.

## Message Generation Logic

### Email Generation
- **Subject Line**: References specific company issue (response time, missing pixel, etc.)
- **Personalization**: Uses company name, contact name, and audit findings
- **Length**: Under 150 words for better response rates
- **CTA**: Clear call-to-action for consultation or audit review

### LinkedIn Message Generation  
- **Character Limit**: Under 300 characters (LinkedIn limit)
- **Conversational Tone**: Professional but approachable
- **Soft CTA**: Non-pushy invitation to connect or share insights

### Personalization Data Points
- Company name and contact information
- Response time and grade from audit
- Current vs potential conversion rates
- Facebook Pixel detection status
- Response channel used (phone, SMS, email)

## Example Generated Messages

### Email Example (Poor Performance)
```
Subject: Quick question about Acme Roofing's lead response

Hi John,

I noticed Acme Roofing is responding to leads in about 36 minutes. While you're responding (which is great!), industry leaders who respond in under 5 minutes see 85% conversion rates vs your current 52%.

I ran a quick audit of your response system and found some opportunities that could boost your conversions significantly.

Would you be interested in a 15-minute call to discuss the findings?

Best regards,
Alex
```

### LinkedIn Example (Good Performance)
```
Hi Sarah! Impressed by Fast Response Inc's 4-minute lead response time - you're beating 90% of competitors. I did notice one optimization opportunity with your Facebook Pixel setup. Mind if I share the insights?
```

## Environment Variables

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Supabase Configuration  
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Service Configuration
PORT=8007
```

## Database Schema

The service uses these Supabase tables:

### outreach_messages
- Stores generated email and LinkedIn messages
- Tracks message status (draft, sent, delivered, etc.)
- Records personalization data and AI generation metadata

### audit_reports
- Contains audit findings used for personalization
- Response times, grades, conversion rates
- Facebook Pixel detection results

### lead_states
- Tracks lead progression through outreach pipeline
- Updates when outreach is generated or sent

## Installation & Setup

1. **Install Dependencies**
```bash
pip install -r requirements.txt
```

2. **Set Environment Variables**
```bash
export OPENAI_API_KEY="your_key"
export SUPABASE_URL="your_url"
export SUPABASE_ANON_KEY="your_key"
```

3. **Run the Service**
```bash
uvicorn main:app --host 0.0.0.0 --port 8007
```

4. **Docker Deployment**
```bash
docker build -t outreach-agent .
docker run -p 8007:8007 --env-file .env outreach-agent
```

## Integration with Lead Generation System

The Outreach Agent integrates with:

1. **Lead Management**: Fetches lead contact information
2. **Audit Reports**: Uses audit findings for personalization  
3. **Lead States**: Updates progression tracking
4. **Frontend**: Provides generated messages to UI

## Message Quality Scoring

Generated messages are scored on:
- **Personalization Score** (1-10): How well the message uses specific lead data
- **Relevance Score** (1-10): How relevant the audit findings are to the prospect
- **Tone Score** (1-10): How well the tone matches the requested style

## Future Enhancements

- **A/B Testing**: Test different message variations
- **Response Tracking**: Monitor email opens, clicks, and replies
- **Template Library**: Pre-built templates for different industries
- **Multi-language Support**: Generate messages in different languages
- **Sentiment Analysis**: Analyze prospect responses for follow-up optimization
