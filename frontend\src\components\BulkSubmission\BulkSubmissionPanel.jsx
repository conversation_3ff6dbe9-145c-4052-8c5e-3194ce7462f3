import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Checkbox,
  FormControlLabel,
  Divider,
  Alert,
  CircularProgress,
  Snackbar,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Card,
  CardContent,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Save as SaveIcon,
  CloudDownload as DownloadIcon,
} from '@mui/icons-material';
import formDiscoveryService from '../../services/formDiscoveryService';
import supabaseService from '../../services/supabaseService';

/**
 * Bulk Submission Panel Component
 *
 * This component allows users to submit forms in bulk for imported leads.
 */
const BulkSubmissionPanel = () => {
  // State for leads
  const [importedLeads, setImportedLeads] = useState([]);
  const [selectedLeads, setSelectedLeads] = useState([]);
  const [filteredLeads, setFilteredLeads] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  // State for target websites
  const [targetWebsites, setTargetWebsites] = useState([{ url: '', selected: true }]);

  // State for submission configuration
  const [submissionConfig, setSubmissionConfig] = useState({
    delayBetweenSubmissions: 2000, // milliseconds
    maxConcurrentSubmissions: 3,
    maxRetries: 3,
    timeout: 30000, // milliseconds
    useAiReasoning: true, // When enabled, AI will optimize all form field types (text inputs, dropdowns, checkboxes, etc.) to maximize business response rates
    allowDuplicateSubmissions: false, // Prevent duplicate submissions by default
  });

  // State for lead data settings
  const [leadDataSettings, setLeadDataSettings] = useState({
    defaultLeadData: {
      name: '',
      email: '',
      phone: '',
      company: '',
      message: ''
    },
    additionalFields: [],
    formFillSettings: {
      useDefaultMessage: true,
      autoFillForms: true
    }
  });

  // State for tracking submitted lead-website combinations
  const [submittedCombinations, setSubmittedCombinations] = useState({});

  // State for submission queue
  const [submissionQueue, setSubmissionQueue] = useState([]);
  const [activeSubmissions, setActiveSubmissions] = useState([]);
  const [completedSubmissions, setCompletedSubmissions] = useState([]);
  const [failedSubmissions, setFailedSubmissions] = useState([]);

  // State for submission process
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [progress, setProgress] = useState(0);
  const [activeTab, setActiveTab] = useState(0);

  // UI state
  const [loading, setLoading] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [loadingStatus, setLoadingStatus] = useState('');
  const [success, setSuccess] = useState(null);
  const [error, setError] = useState(null);

  // Function to fetch leads from Supabase in batches
  const fetchLeadsFromSupabase = async () => {
    try {
      setLoading(true);
      setLoadingProgress(0);
      setLoadingStatus('Preparing to load leads...');

      // Fetch all leads from Supabase with batching and progress tracking
      const { leads, total } = await supabaseService.getAllLeads({}, 1000, (progress) => {
        setLoadingProgress(progress.progress);
        setLoadingStatus(`Loading leads: ${progress.loaded} of ${progress.total} (${progress.progress}%) - Batch ${progress.page}/${progress.totalPages}`);
      });

      if (leads && leads.length > 0) {
        setImportedLeads(leads);
        setFilteredLeads(leads);
        setSuccess(`Successfully loaded all ${leads.length} leads from database`);
        console.log(`Loaded all ${leads.length} leads from database`);
      } else {
        setError('No leads found in the database. Please import leads first.');
      }
    } catch (error) {
      console.error('Error loading leads from Supabase:', error);
      setError('Error loading leads from the database. Please try again later.');
    } finally {
      setLoading(false);
      setLoadingStatus('');
    }
  };

  // Load leads from Supabase and lead data settings from localStorage
  useEffect(() => {

    // Load lead data settings from localStorage
    const loadLeadDataSettings = () => {
      try {
        const defaultLeadData = localStorage.getItem('defaultLeadData');
        const additionalFields = localStorage.getItem('additionalFormFields');
        const formFillSettings = localStorage.getItem('formFillSettings');

        setLeadDataSettings({
          defaultLeadData: defaultLeadData ? JSON.parse(defaultLeadData) : {
            name: '',
            email: '',
            phone: '',
            company: '',
            message: ''
          },
          additionalFields: additionalFields ? JSON.parse(additionalFields) : [],
          formFillSettings: formFillSettings ? JSON.parse(formFillSettings) : {
            useDefaultMessage: true,
            autoFillForms: true
          }
        });
      } catch (error) {
        console.error('Error loading lead data settings:', error);
      }
    };

    fetchLeadsFromSupabase();
    loadLeadDataSettings();
  }, []);

  // Filter leads based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredLeads(importedLeads);
      return;
    }

    const lowerSearchTerm = searchTerm.toLowerCase();
    const filtered = importedLeads.filter(lead => {
      // Search in all fields
      return Object.values(lead).some(value =>
        value && value.toString().toLowerCase().includes(lowerSearchTerm)
      );
    });

    setFilteredLeads(filtered);
  }, [searchTerm, importedLeads]);

  // Handle lead selection
  const handleSelectLead = (leadIndex) => {
    setSelectedLeads(prev => {
      if (prev.includes(leadIndex)) {
        return prev.filter(idx => idx !== leadIndex);
      } else {
        return [...prev, leadIndex];
      }
    });
  };

  // Handle select all leads
  const handleSelectAllLeads = (event) => {
    if (event.target.checked) {
      setSelectedLeads(filteredLeads.map((_, index) => index));
    } else {
      setSelectedLeads([]);
    }
  };

  // Handle target website changes
  const handleWebsiteChange = (index, value) => {
    const updatedWebsites = [...targetWebsites];
    updatedWebsites[index].url = value;
    setTargetWebsites(updatedWebsites);
  };

  // Handle website selection
  const handleWebsiteSelection = (index) => {
    const updatedWebsites = [...targetWebsites];
    updatedWebsites[index].selected = !updatedWebsites[index].selected;
    setTargetWebsites(updatedWebsites);
  };

  // Add a new website input
  const handleAddWebsite = () => {
    setTargetWebsites([...targetWebsites, { url: '', selected: true }]);
  };

  // Remove a website input
  const handleRemoveWebsite = (index) => {
    const updatedWebsites = [...targetWebsites];
    updatedWebsites.splice(index, 1);
    setTargetWebsites(updatedWebsites);
  };

  // Handle configuration changes
  const handleConfigChange = (field, value) => {
    setSubmissionConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Prepare the submission queue
  const prepareSubmissionQueue = useCallback(() => {
    if (selectedLeads.length === 0) {
      setError('Please select at least one lead to submit.');
      return false;
    }

    const selectedWebsites = targetWebsites
      .filter(website => website.selected && website.url.trim())
      .map(website => website.url.trim());

    if (selectedWebsites.length === 0) {
      setError('Please enter at least one target website.');
      return false;
    }

    // Create submission queue
    const queue = [];
    const skippedDuplicates = [];

    selectedLeads.forEach(leadIndex => {
      const lead = filteredLeads[leadIndex];

      selectedWebsites.forEach(website => {
        // Create a unique key for this lead-website combination
        const leadEmail = lead.email || '';
        const combinationKey = `${leadEmail.toLowerCase()}-${website.toLowerCase()}`;

        // Check if this combination has already been submitted
        const isDuplicate = submittedCombinations[combinationKey];

        // Skip if it's a duplicate and duplicates are not allowed
        if (isDuplicate && !submissionConfig.allowDuplicateSubmissions) {
          skippedDuplicates.push({ lead, website });
          return; // Skip this combination
        }

        queue.push({
          id: `${leadIndex}-${website.replace(/[^a-zA-Z0-9]/g, '')}`,
          lead,
          website,
          status: 'queued',
          attempts: 0,
          result: null,
          error: null,
          startTime: null,
          endTime: null,
          isDuplicate,
        });
      });
    });

    // Show warning if duplicates were skipped
    if (skippedDuplicates.length > 0) {
      setSuccess(`Skipped ${skippedDuplicates.length} duplicate submissions. Enable "Allow Duplicate Submissions" in settings to override.`);
    }

    setSubmissionQueue(queue);
    setActiveSubmissions([]);
    setCompletedSubmissions([]);
    setFailedSubmissions([]);
    setProgress(0);

    return true;
  }, [selectedLeads, filteredLeads, targetWebsites, submittedCombinations, submissionConfig.allowDuplicateSubmissions]);

  // Start the submission process
  const startSubmission = useCallback(async () => {
    if (!prepareSubmissionQueue()) {
      return;
    }

    setIsSubmitting(true);
    setIsPaused(false);
  }, [prepareSubmissionQueue]);

  // Pause the submission process
  const pauseSubmission = () => {
    setIsPaused(true);
  };

  // Resume the submission process
  const resumeSubmission = () => {
    setIsPaused(false);
  };

  // Stop the submission process
  const stopSubmission = () => {
    setIsSubmitting(false);
    setIsPaused(false);
  };

  // Process the submission queue
  useEffect(() => {
    if (!isSubmitting || isPaused) return;

    const processQueue = async () => {
      // Check if we have reached the maximum concurrent submissions
      if (activeSubmissions.length >= submissionConfig.maxConcurrentSubmissions) {
        return;
      }

      // Get the next item from the queue
      const nextItem = submissionQueue.find(item => item.status === 'queued');

      if (!nextItem) {
        // No more items in the queue
        if (activeSubmissions.length === 0) {
          // All submissions completed
          setIsSubmitting(false);
          setSuccess('All form submissions completed.');
        }
        return;
      }

      // Update the item status
      const updatedItem = {
        ...nextItem,
        status: 'processing',
        startTime: new Date().toISOString(),
        attempts: nextItem.attempts + 1,
      };

      // Update the queue
      setSubmissionQueue(prev =>
        prev.map(item => item.id === updatedItem.id ? updatedItem : item)
      );

      // Add to active submissions
      setActiveSubmissions(prev => [...prev, updatedItem]);

      // Process the submission
      try {
        // Import services
        const formDiscoveryService = (await import('../../services/formDiscoveryService')).default;
        const supabaseService = (await import('../../services/supabaseService')).default;

        // Step 1: Discover forms on the website
        console.log(`Discovering forms on ${updatedItem.website} for lead ${updatedItem.lead.id || 'unknown'}`);

        // Call the form discovery service to find and fill out forms
        console.log(`Discovering and submitting forms on ${updatedItem.website} for lead ${updatedItem.lead.id || 'unknown'}`);

        // Prepare options for form discovery and submission
        const formOptions = {
          timeout: submissionConfig.timeout / 1000, // Convert to seconds
          useAiReasoning: submissionConfig.useAiReasoning,
          priority: 5,
          maxPages: 10,
          maxDepth: 3
        };

        // In a real implementation, this would call the form discovery service
        // Currently we'll simulate the process with a delay
        await new Promise(resolve => setTimeout(resolve, submissionConfig.delayBetweenSubmissions));

        // Log if AI reasoning is being used
        if (submissionConfig.useAiReasoning) {
          console.log('Using AI reasoning to optimize all form field types for maximum response rate');
        }

        // Apply lead data settings if autoFillForms is enabled
        let fieldsToSubmit = {};
        if (leadDataSettings.formFillSettings.autoFillForms) {
          // Start with the lead's existing data
          fieldsToSubmit = {
            name: updatedItem.lead.name || `${updatedItem.lead.first_name || ''} ${updatedItem.lead.last_name || ''}`,
            email: updatedItem.lead.email,
            phone: updatedItem.lead.phone,
            company: updatedItem.lead.company
          };

          // Apply default lead data for any missing fields
          if (!fieldsToSubmit.name || !fieldsToSubmit.name.trim()) {
            fieldsToSubmit.name = leadDataSettings.defaultLeadData.name || '';
          }
          if (!fieldsToSubmit.email || !fieldsToSubmit.email.trim()) {
            fieldsToSubmit.email = leadDataSettings.defaultLeadData.email || '';
          }
          if (!fieldsToSubmit.phone || !fieldsToSubmit.phone.trim()) {
            fieldsToSubmit.phone = leadDataSettings.defaultLeadData.phone || '';
          }
          if (!fieldsToSubmit.company || !fieldsToSubmit.company.trim()) {
            fieldsToSubmit.company = leadDataSettings.defaultLeadData.company || '';
          }

          // Add message if useDefaultMessage is enabled
          if (leadDataSettings.formFillSettings.useDefaultMessage) {
            fieldsToSubmit.message = leadDataSettings.defaultLeadData.message ||
              `Hi, I'm ${fieldsToSubmit.name} from ${fieldsToSubmit.company} and I'm interested in learning more about your services.`;
          }

          // Add additional fields if they exist
          if (leadDataSettings.additionalFields && leadDataSettings.additionalFields.length > 0) {
            leadDataSettings.additionalFields.forEach(field => {
              if (field.key && field.value) {
                fieldsToSubmit[field.key] = field.value;
              }
            });
          }

          // Add AI reasoning metadata to optimize responses rather than using lead information
          if (submissionConfig.useAiReasoning) {
            fieldsToSubmit._aiContext = {
              targetWebsite: updatedItem.website,
              optimizeForResponse: true,
              businessEngagementFocus: true,
              targetUrl: updatedItem.website,
              supportedFieldTypes: [
                'text', 'email', 'tel', 'textarea', 'select',
                'radio', 'checkbox', 'date', 'number', 'url'
              ],
              fieldTypePreferences: {
                // For dropdown/select fields
                select: { preferPositiveOptions: true, avoidExtremeClaims: true },
                // For radio button groups
                radio: { preferModerateOptions: true, contextSensitive: true },
                // For checkbox groups
                checkbox: { selectRelevantOnly: true, avoidSelectingAll: true },
                // For text fields
                text: { optimizeLength: true, avoidOversharing: true }
              }
            };
          }
        } else {
          // If autoFillForms is disabled, just use the lead's existing data
          fieldsToSubmit = {
            name: updatedItem.lead.name || `${updatedItem.lead.first_name || ''} ${updatedItem.lead.last_name || ''}`,
            email: updatedItem.lead.email,
            phone: updatedItem.lead.phone,
            company: updatedItem.lead.company
          };
        }

        // Simulate success or failure (80% success rate for demo)
        const isSuccess = Math.random() < 0.8;

        // Create submission data for database
        const submissionData = {
          lead_id: updatedItem.lead.id,
          website: updatedItem.website,
          form_url: `${updatedItem.website}/contact`,
          form_id: `form_${Math.floor(Math.random() * 10000)}`,
          status: isSuccess ? 'completed' : 'failed',
          submitted_at: new Date().toISOString(),
          fields_submitted: fieldsToSubmit,
          response_data: isSuccess ? { success: true, message: 'Form submitted successfully' } : null,
          error_message: isSuccess ? null : 'Simulated error: Form submission failed',
          attempt_count: updatedItem.attempts,
          success: isSuccess
        };

        // Always save to Supabase - if lead doesn't have an ID, create one first
        try {
          if (!updatedItem.lead.id) {
            // Create the lead in Supabase if it doesn't have an ID yet
            const createdLead = await supabaseService.createLead({
              first_name: updatedItem.lead.first_name || '',
              last_name: updatedItem.lead.last_name || '',
              email: updatedItem.lead.email,
              phone: updatedItem.lead.phone,
              company: updatedItem.lead.company,
              source: 'bulk_form_submission'
            });

            // Update the lead with the new ID in the state
            updatedItem.lead.id = createdLead.id;
            submissionData.lead_id = createdLead.id;
          }

          // Now save the form submission
          await supabaseService.createFormSubmission(submissionData);
        } catch (dbError) {
          console.error('Error saving form submission to database:', dbError);
          // Continue with the process even if database save fails
        }

        if (isSuccess) {
          // Submission succeeded
          const completedItem = {
            ...updatedItem,
            status: 'completed',
            endTime: new Date().toISOString(),
            result: {
              success: true,
              formId: submissionData.form_id,
              fieldsSubmitted: Object.keys(submissionData.fields_submitted).length,
              submissionTime: Math.floor(Math.random() * 2000 + 1000),
            },
          };

          // Track this lead-website combination as submitted
          const leadEmail = updatedItem.lead.email || '';
          const combinationKey = `${leadEmail.toLowerCase()}-${updatedItem.website.toLowerCase()}`;
          setSubmittedCombinations(prev => ({
            ...prev,
            [combinationKey]: true
          }));

          // Update the queue
          setSubmissionQueue(prev =>
            prev.map(item => item.id === completedItem.id ? completedItem : item)
          );

          // Remove from active submissions
          setActiveSubmissions(prev =>
            prev.filter(item => item.id !== completedItem.id)
          );

          // Add to completed submissions
          setCompletedSubmissions(prev => [...prev, completedItem]);
        } else {
          // Submission failed
          const failedItem = {
            ...updatedItem,
            status: updatedItem.attempts >= submissionConfig.maxRetries ? 'failed' : 'queued',
            endTime: new Date().toISOString(),
            error: 'Simulated error: Form submission failed',
          };

          // Update the queue
          setSubmissionQueue(prev =>
            prev.map(item => item.id === failedItem.id ? failedItem : item)
          );

          // Remove from active submissions
          setActiveSubmissions(prev =>
            prev.filter(item => item.id !== failedItem.id)
          );

          // Add to failed submissions if max retries reached
          if (failedItem.status === 'failed') {
            setFailedSubmissions(prev => [...prev, failedItem]);
          }
        }

        // Update progress
        const totalItems = submissionQueue.length;
        const completedItems = completedSubmissions.length + failedSubmissions.length + 1;
        setProgress(Math.floor((completedItems / totalItems) * 100));

      } catch (error) {
        console.error('Error processing submission:', error);

        // Update the item status
        const failedItem = {
          ...updatedItem,
          status: updatedItem.attempts >= submissionConfig.maxRetries ? 'failed' : 'queued',
          endTime: new Date().toISOString(),
          error: `Error: ${error.message}`,
        };

        // Update the queue
        setSubmissionQueue(prev =>
          prev.map(item => item.id === failedItem.id ? failedItem : item)
        );

        // Remove from active submissions
        setActiveSubmissions(prev =>
          prev.filter(item => item.id !== failedItem.id)
        );

        // Add to failed submissions if max retries reached
        if (failedItem.status === 'failed') {
          setFailedSubmissions(prev => [...prev, failedItem]);
        }
      }
    };

    // Process the queue
    const interval = setInterval(processQueue, 500);

    return () => clearInterval(interval);
  }, [
    isSubmitting,
    isPaused,
    submissionQueue,
    activeSubmissions,
    completedSubmissions,
    failedSubmissions,
    submissionConfig
  ]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h5" gutterBottom>
        Bulk Form Submission
      </Typography>

      <Typography variant="body2" paragraph>
        Submit forms in bulk for your imported leads. Select leads, enter target websites, configure submission settings, and start the process.
      </Typography>

      {loading ? (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', my: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <CircularProgress />
            <Typography variant="body1" sx={{ ml: 2, fontWeight: 'medium' }}>
              {loadingStatus || 'Loading leads from database...'}
            </Typography>
          </Box>
          {loadingProgress > 0 && (
            <Box sx={{ width: '100%', maxWidth: 600, mt: 1 }}>
              <LinearProgress variant="determinate" value={loadingProgress} sx={{ height: 10, borderRadius: 5 }} />
              <Typography variant="caption" sx={{ mt: 0.5, display: 'block', textAlign: 'center' }}>
                {loadingProgress}% Complete
              </Typography>
            </Box>
          )}
        </Box>
      ) : importedLeads.length === 0 ? (
        <Box>
          <Alert severity="warning" sx={{ mb: 3 }}>
            No leads found. Please import leads first using the Lead Import feature.
          </Alert>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchLeadsFromSupabase}
            sx={{ mt: 2 }}
          >
            Refresh Leads
          </Button>
        </Box>
      ) : (
        <>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
              <Tab label="Select Leads" />
              <Tab label="Configure Submission" />
              <Tab label="Results" disabled={!isSubmitting && completedSubmissions.length === 0} />
            </Tabs>

            {/* Select Leads Tab */}
            {activeTab === 0 && (
              <Box>
                <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                  <TextField
                    label="Search Leads"
                    variant="outlined"
                    size="small"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    sx={{ mr: 2, flexGrow: 1 }}
                    InputProps={{
                      startAdornment: <SearchIcon sx={{ color: 'action.active', mr: 1 }} />,
                    }}
                  />

                  <Button
                    variant="outlined"
                    startIcon={<FilterIcon />}
                    size="small"
                    sx={{ mr: 2 }}
                  >
                    Filter
                  </Button>

                  <Tooltip title="Refresh leads from database">
                    <Button
                      variant="outlined"
                      startIcon={<RefreshIcon />}
                      size="small"
                      onClick={fetchLeadsFromSupabase}
                      disabled={loading}
                      sx={{ mr: 2 }}
                    >
                      Refresh
                    </Button>
                  </Tooltip>

                  <Typography variant="body2" sx={{ ml: 'auto' }}>
                    {selectedLeads.length} of {filteredLeads.length} leads selected
                  </Typography>
                </Box>

                <TableContainer sx={{ maxHeight: 400 }}>
                  <Table stickyHeader size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell padding="checkbox">
                          <Checkbox
                            indeterminate={selectedLeads.length > 0 && selectedLeads.length < filteredLeads.length}
                            checked={selectedLeads.length > 0 && selectedLeads.length === filteredLeads.length}
                            onChange={handleSelectAllLeads}
                          />
                        </TableCell>
                        <TableCell>Name</TableCell>
                        <TableCell>Email</TableCell>
                        <TableCell>Phone</TableCell>
                        <TableCell>Company</TableCell>
                        <TableCell>Website</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredLeads.map((lead, index) => (
                        <TableRow
                          key={index}
                          hover
                          selected={selectedLeads.includes(index)}
                          onClick={() => handleSelectLead(index)}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={selectedLeads.includes(index)}
                              onChange={() => handleSelectLead(index)}
                              onClick={(e) => e.stopPropagation()}
                            />
                          </TableCell>
                          <TableCell>{lead.name || `${lead.first_name || ''} ${lead.last_name || ''}`}</TableCell>
                          <TableCell>{lead.email}</TableCell>
                          <TableCell>{lead.phone}</TableCell>
                          <TableCell>{lead.company}</TableCell>
                          <TableCell>{lead.website}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}

            {/* Configure Submission Tab */}
            {activeTab === 1 && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Target Websites
                </Typography>

                <Typography variant="body2" paragraph>
                  Enter the websites where you want to discover and submit forms. You can add multiple websites.
                </Typography>

                {targetWebsites.map((website, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Checkbox
                      checked={website.selected}
                      onChange={() => handleWebsiteSelection(index)}
                      size="small"
                    />
                    <TextField
                      label={`Website ${index + 1}`}
                      variant="outlined"
                      size="small"
                      fullWidth
                      value={website.url}
                      onChange={(e) => handleWebsiteChange(index, e.target.value)}
                      placeholder="https://example.com"
                      sx={{ mr: 1 }}
                    />
                    <IconButton
                      size="small"
                      onClick={() => handleRemoveWebsite(index)}
                      disabled={targetWebsites.length === 1}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                ))}

                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleAddWebsite}
                  sx={{ mt: 1 }}
                >
                  Add Website
                </Button>

                <Divider sx={{ my: 3 }} />

                <Typography variant="subtitle1" gutterBottom>
                  Submission Settings
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Delay Between Submissions (ms)"
                      type="number"
                      value={submissionConfig.delayBetweenSubmissions}
                      onChange={(e) => handleConfigChange('delayBetweenSubmissions', parseInt(e.target.value))}
                      fullWidth
                      margin="normal"
                      inputProps={{ min: 500, step: 500 }}
                      helperText="Time to wait between form submissions"
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={submissionConfig.useAiReasoning}
                          onChange={(e) => handleConfigChange('useAiReasoning', e.target.checked)}
                          color="primary"
                        />
                      }
                      label="Use AI reasoning for forms"
                    />
                    <Typography variant="caption" color="textSecondary" sx={{ display: 'block', ml: 2 }}>
                      AI will intelligently answer questions not covered by your data settings
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2, mt: 2, border: '1px solid #e0e0e0', bgcolor: '#f9f9f9' }}>
                      <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                        Lead Data Settings Preview
                      </Typography>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Configuration:
                        </Typography>
                        <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                          • Auto-Fill Forms: {leadDataSettings.formFillSettings.autoFillForms ? '✅ Enabled' : '❌ Disabled'}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          • Use Default Message: {leadDataSettings.formFillSettings.useDefaultMessage ? '✅ Enabled' : '❌ Disabled'}
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Default Data:
                        </Typography>
                        <Grid container spacing={1}>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">
                              • Name: {leadDataSettings.defaultLeadData.name || '(not set)'}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">
                              • Email: {leadDataSettings.defaultLeadData.email || '(not set)'}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">
                              • Phone: {leadDataSettings.defaultLeadData.phone || '(not set)'}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">
                              • Company: {leadDataSettings.defaultLeadData.company || '(not set)'}
                            </Typography>
                          </Grid>
                        </Grid>
                      </Box>

                      {leadDataSettings.additionalFields && leadDataSettings.additionalFields.length > 0 && (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Additional Fields:
                          </Typography>
                          {leadDataSettings.additionalFields.map((field, index) => (
                            <Typography key={index} variant="body2" color="textSecondary">
                              • {field.key}: {field.value}
                            </Typography>
                          ))}
                        </Box>
                      )}

                      <Button
                        size="small"
                        variant="outlined"
                        color="primary"
                        sx={{ mt: 1 }}
                        onClick={() => {
                          window.location.href = '/#/settings';
                        }}
                      >
                        Edit Lead Data Settings
                      </Button>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Max Concurrent Submissions"
                      variant="outlined"
                      size="small"
                      fullWidth
                      type="number"
                      value={submissionConfig.maxConcurrentSubmissions}
                      onChange={(e) => handleConfigChange('maxConcurrentSubmissions', parseInt(e.target.value))}
                      InputProps={{ inputProps: { min: 1, max: 10 } }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Max Retries"
                      variant="outlined"
                      size="small"
                      fullWidth
                      type="number"
                      value={submissionConfig.maxRetries}
                      onChange={(e) => handleConfigChange('maxRetries', parseInt(e.target.value))}
                      InputProps={{ inputProps: { min: 0, max: 5 } }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Timeout (ms)"
                      variant="outlined"
                      size="small"
                      fullWidth
                      type="number"
                      value={submissionConfig.timeout}
                      onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
                      InputProps={{ inputProps: { min: 5000 } }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={submissionConfig.useAiReasoning}
                          onChange={(e) => handleConfigChange('useAiReasoning', e.target.checked)}
                        />
                      }
                      label="Use AI reasoning for form filling"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={submissionConfig.allowDuplicateSubmissions}
                          onChange={(e) => handleConfigChange('allowDuplicateSubmissions', e.target.checked)}
                        />
                      }
                      label="Allow duplicate submissions (submit to the same website more than once)"
                    />
                    {!submissionConfig.allowDuplicateSubmissions && (
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', ml: 4 }}>
                        Duplicate submissions are prevented by tracking lead-website combinations that have already been submitted.
                      </Typography>
                    )}
                    {submissionConfig.allowDuplicateSubmissions && (
                      <Typography variant="caption" color="warning.main" sx={{ display: 'block', ml: 4 }}>
                        Warning: Allowing duplicate submissions may result in the same lead being submitted multiple times to the same website.
                      </Typography>
                    )}
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Results Tab */}
            {activeTab === 2 && (
              <Box>
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={12} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h6">{submissionQueue.length}</Typography>
                      <Typography variant="body2">Total Submissions</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#e8f5e9' }}>
                      <Typography variant="h6" color="success.main">{completedSubmissions.length}</Typography>
                      <Typography variant="body2">Successful</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#ffebee' }}>
                      <Typography variant="h6" color="error.main">{failedSubmissions.length}</Typography>
                      <Typography variant="body2">Failed</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#e3f2fd' }}>
                      <Typography variant="h6" color="info.main">{activeSubmissions.length}</Typography>
                      <Typography variant="body2">In Progress</Typography>
                    </Paper>
                  </Grid>
                </Grid>

                <Box sx={{ mb: 2 }}>
                  <LinearProgress variant="determinate" value={progress} sx={{ height: 10, borderRadius: 5 }} />
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Progress: {progress}% ({completedSubmissions.length + failedSubmissions.length} of {submissionQueue.length})
                  </Typography>
                </Box>

                <Typography variant="subtitle1" gutterBottom>
                  Submission Results
                </Typography>

                <TableContainer sx={{ maxHeight: 300 }}>
                  <Table stickyHeader size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Status</TableCell>
                        <TableCell>Lead</TableCell>
                        <TableCell>Website</TableCell>
                        <TableCell>Result</TableCell>
                        <TableCell>Time</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {[...completedSubmissions, ...activeSubmissions, ...failedSubmissions]
                        .sort((a, b) => {
                          if (a.status === 'processing' && b.status !== 'processing') return -1;
                          if (a.status !== 'processing' && b.status === 'processing') return 1;
                          return 0;
                        })
                        .map((submission) => (
                          <TableRow key={submission.id}>
                            <TableCell>
                              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                                {submission.status === 'completed' && (
                                  <Chip
                                    icon={<SuccessIcon />}
                                    label="Success"
                                    color="success"
                                    size="small"
                                  />
                                )}
                                {submission.status === 'processing' && (
                                  <Chip
                                    icon={<CircularProgress size={16} />}
                                    label="Processing"
                                    color="info"
                                    size="small"
                                  />
                                )}
                                {submission.status === 'failed' && (
                                  <Chip
                                    icon={<ErrorIcon />}
                                    label="Failed"
                                    color="error"
                                    size="small"
                                  />
                                )}
                                {submission.isDuplicate && (
                                  <Tooltip title="This lead has already been submitted to this website before">
                                    <Chip
                                      icon={<WarningIcon />}
                                      label="Duplicate"
                                      color="warning"
                                      size="small"
                                      variant="outlined"
                                    />
                                  </Tooltip>
                                )}
                              </Box>
                            </TableCell>
                            <TableCell>
                              {submission.lead.name || `${submission.lead.first_name || ''} ${submission.lead.last_name || ''}`}
                            </TableCell>
                            <TableCell>{submission.website}</TableCell>
                            <TableCell>
                              {submission.result ? (
                                `${submission.result.fieldsSubmitted} fields submitted`
                              ) : (
                                submission.error || '-'
                              )}
                            </TableCell>
                            <TableCell>
                              {submission.endTime && submission.startTime ? (
                                `${Math.round((new Date(submission.endTime) - new Date(submission.startTime)) / 1000)}s`
                              ) : (
                                '-'
                              )}
                            </TableCell>
                            <TableCell>
                              {submission.status === 'failed' && (
                                <Tooltip title="Retry">
                                  <IconButton size="small">
                                    <RefreshIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}
          </Paper>

          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              onClick={() => setActiveTab(Math.max(0, activeTab - 1))}
              disabled={activeTab === 0 || isSubmitting}
            >
              Previous
            </Button>

            {activeTab < 2 ? (
              <Button
                variant="contained"
                onClick={() => setActiveTab(Math.min(2, activeTab + 1))}
                disabled={activeTab === 0 && selectedLeads.length === 0}
              >
                Next
              </Button>
            ) : (
              <Box>
                {!isSubmitting && completedSubmissions.length === 0 && (
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<StartIcon />}
                    onClick={startSubmission}
                    disabled={selectedLeads.length === 0}
                  >
                    Start Submission
                  </Button>
                )}

                {isSubmitting && !isPaused && (
                  <Button
                    variant="outlined"
                    color="warning"
                    startIcon={<PauseIcon />}
                    onClick={pauseSubmission}
                    sx={{ mr: 1 }}
                  >
                    Pause
                  </Button>
                )}

                {isSubmitting && isPaused && (
                  <Button
                    variant="outlined"
                    color="primary"
                    startIcon={<StartIcon />}
                    onClick={resumeSubmission}
                    sx={{ mr: 1 }}
                  >
                    Resume
                  </Button>
                )}

                {isSubmitting && (
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<StopIcon />}
                    onClick={stopSubmission}
                  >
                    Stop
                  </Button>
                )}
              </Box>
            )}
          </Box>
        </>
      )}

      {/* Success and Error Messages */}
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setSuccess(null)} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BulkSubmissionPanel;
