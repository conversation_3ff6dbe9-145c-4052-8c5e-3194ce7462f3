import React from 'react';
import { render, screen } from '@testing-library/react';
import Dashboard from './Dashboard';

describe('Dashboard Component', () => {
  test('renders dashboard header', () => {
    render(<Dashboard />);
    const headerElement = screen.getByText(/Dashboard/i);
    expect(headerElement).toBeInTheDocument();
  });

  test('renders metrics cards', () => {
    render(<Dashboard />);
    expect(screen.getByText(/Total Form Submissions/i)).toBeInTheDocument();
    expect(screen.getByText(/Total Responses/i)).toBeInTheDocument();
    expect(screen.getByText(/Conversion Rate/i)).toBeInTheDocument();
    expect(screen.getByText(/Avg\. Response Time/i)).toBeInTheDocument();
  });

  test('renders response threads section', () => {
    render(<Dashboard />);
    expect(screen.getByText(/Recent Response Threads/i)).toBeInTheDocument();
  });

  test('renders at least one response thread', () => {
    render(<Dashboard />);
    // Check for a lead name that we know exists in the mock data
    expect(screen.getByText(/John <PERSON>/i)).toBeInTheDocument();
  });
});
