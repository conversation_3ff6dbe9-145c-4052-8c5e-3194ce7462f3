"""
Response Monitoring API

This module provides a FastAPI interface for the response monitoring system.
"""
import os
import sys
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Add parent directory to path to allow importing from sibling modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import response monitoring modules
from response_monitor import ResponseMonitor
from analytics.response_audit import ResponseAudit

# Initialize FastAPI app
app = FastAPI(
    title="Response Monitoring API",
    description="API for monitoring and analyzing lead responses",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize response monitor
monitor = None
audit = None

# Models
class MonitoringStatus(BaseModel):
    monitoring_active: bool
    email_monitoring: bool
    sms_monitoring: bool
    email_agent_initialized: bool
    sms_agent_initialized: bool
    audit_logging_enabled: bool

class ResponseTimeReport(BaseModel):
    report_file: str
    row_count: int
    start_date: str
    end_date: str
    summary: Dict[str, Any]

class ResponseSummary(BaseModel):
    total_leads: int
    responded_leads: int
    response_rate: float
    channels: Dict[str, Dict[str, Any]]

class ChannelStats(BaseModel):
    channel: str
    count: int
    avg_seconds: float
    median_seconds: float
    min_seconds: float
    max_seconds: float
    avg_human: str
    median_human: str
    min_human: str
    max_human: str

class DashboardStats(BaseModel):
    total_leads: int
    responded_leads: int
    response_rate: float
    avg_response_time_seconds: float
    avg_response_time_human: str
    channel_stats: List[ChannelStats]
    date_range: Dict[str, str]

# Initialize components on startup
@app.on_event("startup")
async def startup_event():
    global monitor, audit
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("[WARN] python-dotenv not installed; skipping .env loading.")
    
    # Initialize response audit
    audit = ResponseAudit()
    
    # Initialize response monitor
    email_config = {
        "username": os.environ.get("EMAIL_USERNAME"),
        "password": os.environ.get("EMAIL_APP_PASSWORD"),
        "imap_server": os.environ.get("EMAIL_IMAP_SERVER", "imap.gmail.com")
    }
    
    monitor = ResponseMonitor(
        email_config=email_config,
        openai_api_key=os.environ.get("OPENAI_API_KEY"),
        enable_audit_logging=True
    )

# Shutdown components on shutdown
@app.on_event("shutdown")
async def shutdown_event():
    global monitor
    
    if monitor and monitor.monitoring_active:
        monitor.stop_monitoring()

# API endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

@app.get("/status", response_model=MonitoringStatus)
async def get_status():
    """Get the status of the response monitoring system."""
    global monitor
    
    if not monitor:
        raise HTTPException(status_code=500, detail="Response monitor not initialized")
    
    return monitor.get_monitoring_status()

@app.post("/start")
async def start_monitoring(
    email: bool = Query(True, description="Enable email monitoring"),
    sms: bool = Query(False, description="Enable SMS monitoring"),
    interval: int = Query(60, description="Polling interval in seconds")
):
    """Start response monitoring."""
    global monitor
    
    if not monitor:
        raise HTTPException(status_code=500, detail="Response monitor not initialized")
    
    if monitor.monitoring_active:
        return {"status": "already_running", "message": "Monitoring is already active"}
    
    if email:
        monitor.start_email_monitoring(interval=interval)
    
    return {"status": "started", "email": email, "sms": sms, "interval": interval}

@app.post("/stop")
async def stop_monitoring():
    """Stop response monitoring."""
    global monitor
    
    if not monitor:
        raise HTTPException(status_code=500, detail="Response monitor not initialized")
    
    if not monitor.monitoring_active:
        return {"status": "not_running", "message": "Monitoring is not active"}
    
    monitor.stop_monitoring()
    
    return {"status": "stopped"}

@app.get("/report", response_model=ResponseTimeReport)
async def generate_report(
    days: int = Query(30, description="Number of days to include in the report"),
    background_tasks: BackgroundTasks = None
):
    """Generate a response audit report."""
    global audit
    
    if not audit:
        raise HTTPException(status_code=500, detail="Response audit not initialized")
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    # Generate report
    report = audit.generate_response_time_report(start_date, end_date)
    
    return report

@app.get("/dashboard", response_model=DashboardStats)
async def get_dashboard_stats(
    days: int = Query(30, description="Number of days to include in the stats")
):
    """Get dashboard statistics."""
    global audit
    
    if not audit:
        raise HTTPException(status_code=500, detail="Response audit not initialized")
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    # Generate report
    report = audit.generate_response_time_report(start_date, end_date)
    
    # Extract summary
    summary = report["summary"]
    
    # Calculate overall average response time
    avg_seconds = 0
    total_responses = 0
    
    for channel, stats in summary["channels"].items():
        if channel != "NO_RESPONSE":
            avg_seconds += stats["avg_seconds"] * stats["count"]
            total_responses += stats["count"]
    
    if total_responses > 0:
        avg_seconds = avg_seconds / total_responses
    
    # Format channel stats
    channel_stats = []
    for channel, stats in summary["channels"].items():
        if channel != "NO_RESPONSE":
            channel_stats.append(ChannelStats(
                channel=channel,
                count=stats["count"],
                avg_seconds=stats["avg_seconds"],
                median_seconds=stats["median_seconds"],
                min_seconds=stats["min_seconds"],
                max_seconds=stats["max_seconds"],
                avg_human=stats["avg_human"],
                median_human=stats["median_human"],
                min_human=stats["min_human"],
                max_human=stats["max_human"]
            ))
    
    return DashboardStats(
        total_leads=summary["total_leads"],
        responded_leads=summary["responded_leads"],
        response_rate=summary["response_rate"],
        avg_response_time_seconds=avg_seconds,
        avg_response_time_human=str(timedelta(seconds=int(avg_seconds))),
        channel_stats=channel_stats,
        date_range={
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }
    )

# Run the app
if __name__ == "__main__":
    import uvicorn
    
    # Get port from environment or use default
    port = int(os.environ.get("RESPONSE_MONITOR_PORT", 8005))
    
    print(f"[INFO] Starting Response Monitoring API on port {port}")
    uvicorn.run(app, host="0.0.0.0", port=port)
