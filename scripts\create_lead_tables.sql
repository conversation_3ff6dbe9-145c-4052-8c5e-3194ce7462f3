-- SQL script to create tables for the lead management system

-- Enable RLS (Row Level Security)
ALTER DATABASE postgres SET "app.jwt_secret" TO 'xxqNhALvbilgJeehO2/RQcaGbHUv0jw409tS8CEbWuRr0+x95RwbRC3Zq+aMPXv0OLH2qDnrUoz1IIw/3a9z9w==';

-- Create extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create leads table
CREATE TABLE IF NOT EXISTS leads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Basic lead information
    first_name TEXT,
    last_name TEXT,
    name TEXT, -- Combined name field (for cases where first/last are not separated)
    email TEXT,
    phone TEXT,
    alt_phone TEXT[], -- Array of alternative phone numbers
    company TEXT,
    title TEXT,
    
    -- Address information
    address TEXT,
    city TEXT,
    state TEXT,
    zip TEXT,
    country TEXT DEFAULT 'USA',
    
    -- Website and social media
    website TEXT,
    facebook TEXT,
    linkedin TEXT,
    twitter TEXT,
    instagram TEXT,
    youtube TEXT,
    tiktok TEXT,
    
    -- Facebook pixel information
    facebook_pixel BOOLEAN,
    
    -- Lead source information
    source TEXT,
    source_details JSONB,
    imported_at TIMESTAMP WITH TIME ZONE,
    import_batch_id UUID,
    
    -- Lead status and tracking
    status TEXT DEFAULT 'new',
    enriched BOOLEAN DEFAULT FALSE,
    form_submitted BOOLEAN DEFAULT FALSE,
    last_enriched_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional data
    notes TEXT,
    tags TEXT[],
    custom_fields JSONB,
    
    -- Search index
    search_vector TSVECTOR
);

-- Create import_batches table to track CSV imports
CREATE TABLE IF NOT EXISTS import_batches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    name TEXT,
    file_name TEXT,
    record_count INTEGER,
    successful_count INTEGER,
    failed_count INTEGER,
    status TEXT DEFAULT 'processing',
    mapping JSONB,
    settings JSONB,
    user_id UUID
);

-- Create form_submissions table to track form submissions
CREATE TABLE IF NOT EXISTS form_submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    lead_id UUID REFERENCES leads(id) ON DELETE CASCADE,
    website TEXT NOT NULL,
    form_url TEXT,
    form_id TEXT,
    status TEXT DEFAULT 'pending',
    submitted_at TIMESTAMP WITH TIME ZONE,
    fields_submitted JSONB,
    response_data JSONB,
    error_message TEXT,
    attempt_count INTEGER DEFAULT 0,
    next_attempt_at TIMESTAMP WITH TIME ZONE,
    success BOOLEAN
);

-- Create websites table to track discovered websites and forms
CREATE TABLE IF NOT EXISTS websites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    domain TEXT UNIQUE NOT NULL,
    url TEXT NOT NULL,
    title TEXT,
    description TEXT,
    has_forms BOOLEAN,
    forms_count INTEGER DEFAULT 0,
    forms JSONB,
    facebook_pixel BOOLEAN,
    last_crawled_at TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'discovered',
    notes TEXT
);

-- Create settings table for default form fields and other settings
CREATE TABLE IF NOT EXISTS settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    key TEXT UNIQUE NOT NULL,
    value JSONB,
    description TEXT
);

-- Insert default settings
INSERT INTO settings (key, value, description)
VALUES 
('default_form_fields', 
 '{"name": true, "email": true, "phone": true, "company": false, "message": false}', 
 'Default fields to use for form submissions'),
('enrichment_settings', 
 '{"use_serper": true, "scrape_websites": true, "check_facebook_pixel": true}', 
 'Settings for lead enrichment process'),
('form_submission_settings', 
 '{"max_retries": 3, "delay_between_submissions": 2000, "timeout": 30000}', 
 'Settings for form submission process');

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS leads_email_idx ON leads(email);
CREATE INDEX IF NOT EXISTS leads_phone_idx ON leads(phone);
CREATE INDEX IF NOT EXISTS leads_company_idx ON leads(company);
CREATE INDEX IF NOT EXISTS leads_status_idx ON leads(status);
CREATE INDEX IF NOT EXISTS leads_form_submitted_idx ON leads(form_submitted);
CREATE INDEX IF NOT EXISTS form_submissions_lead_id_idx ON form_submissions(lead_id);
CREATE INDEX IF NOT EXISTS form_submissions_status_idx ON form_submissions(status);
CREATE INDEX IF NOT EXISTS websites_domain_idx ON websites(domain);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update the updated_at field
CREATE TRIGGER update_leads_updated_at
BEFORE UPDATE ON leads
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_form_submissions_updated_at
BEFORE UPDATE ON form_submissions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_websites_updated_at
BEFORE UPDATE ON websites
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_settings_updated_at
BEFORE UPDATE ON settings
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- Create a function to update the search vector
CREATE OR REPLACE FUNCTION leads_search_update()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector = 
        setweight(to_tsvector('english', coalesce(NEW.name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.first_name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.last_name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.email, '')), 'B') ||
        setweight(to_tsvector('english', coalesce(NEW.phone, '')), 'B') ||
        setweight(to_tsvector('english', coalesce(NEW.company, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.website, '')), 'C') ||
        setweight(to_tsvector('english', coalesce(NEW.city, '')), 'D') ||
        setweight(to_tsvector('english', coalesce(NEW.state, '')), 'D');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update the search vector
CREATE TRIGGER leads_search_update
BEFORE INSERT OR UPDATE ON leads
FOR EACH ROW
EXECUTE FUNCTION leads_search_update();

-- Create a GIN index for the search vector
CREATE INDEX IF NOT EXISTS leads_search_idx ON leads USING GIN(search_vector);

-- Enable Row Level Security (RLS)
ALTER TABLE leads ENABLE ROW LEVEL SECURITY;
ALTER TABLE import_batches ENABLE ROW LEVEL SECURITY;
ALTER TABLE form_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE websites ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated users
CREATE POLICY "Allow full access to authenticated users" ON leads
    FOR ALL TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow full access to authenticated users" ON import_batches
    FOR ALL TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow full access to authenticated users" ON form_submissions
    FOR ALL TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow full access to authenticated users" ON websites
    FOR ALL TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow full access to authenticated users" ON settings
    FOR ALL TO authenticated
    USING (true)
    WITH CHECK (true);

-- Create policies for anonymous users (read-only access to certain tables)
CREATE POLICY "Allow read-only access to anonymous users" ON websites
    FOR SELECT TO anon
    USING (true);

CREATE POLICY "Allow read-only access to anonymous users" ON settings
    FOR SELECT TO anon
    USING (true);
