import os
import uuid
from typing import List, Dict, Any, Optional
from fastapi import Fast<PERSON><PERSON>, BackgroundTasks, HTTPException, Depends, status
from pydantic import BaseModel
from datetime import datetime

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.auth import get_current_active_user, User
from libs.interfaces import (
    Conversation, 
    ResponseChannel,
    Event, 
    EventType, 
    ConversationCreatedPayload,
    ConversationUpdatedPayload,
    ResponseReceivedPayload
)

# Import service-specific modules
from .conversation_service import ConversationService
from .event_producer import EventProducer
from .event_consumer import EventConsumer

# Initialize FastAPI app
app = FastAPI(
    title="Conversation Management Service",
    description="Service for managing conversations and threading responses",
    version="0.1.0"
)

# Initialize logger
logger = get_service_logger("conversation-management")

# Initialize event producer
event_producer = EventProducer(
    bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092"),
    topic=os.getenv("CONVERSATION_UPDATED_TOPIC", "conversation.updated")
)

# Initialize event consumer
event_consumer = EventConsumer(
    bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092"),
    topic=os.getenv("RESPONSE_RECEIVED_TOPIC", "response.received"),
    group_id="conversation-management-service"
)

# Initialize conversation service
conversation_service = ConversationService()


# Request/Response models
class CreateConversationRequest(BaseModel):
    lead_id: str
    response_ids: List[str]
    channel: ResponseChannel
    status: str = "active"
    tags: List[str] = []
    assigned_to: Optional[str] = None
    metadata: Dict[str, Any] = {}


class UpdateConversationRequest(BaseModel):
    response_ids: Optional[List[str]] = None
    status: Optional[str] = None
    tags: Optional[List[str]] = None
    assigned_to: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ConversationResponse(BaseModel):
    conversation_id: str
    message: str


# Background task for processing response received events
async def process_response_received(payload: ResponseReceivedPayload):
    """Background task to process a response received event."""
    logger.info(f"Processing response received event for response {payload.response_id}")
    
    try:
        # Get lead ID from payload
        lead_id = payload.lead_id
        
        # Get channel from payload
        channel = payload.channel
        
        # Get response ID from payload
        response_id = payload.response_id
        
        # Find existing conversation for this lead and channel
        conversations = await conversation_service.get_conversations(
            lead_id=lead_id,
            channel=channel,
            status="active"
        )
        
        if conversations:
            # Use the first active conversation
            conversation = conversations[0]
            conversation_id = conversation.conversation_id
            
            # Add response to conversation
            response_ids = conversation.responses + [response_id]
            
            # Update conversation
            await conversation_service.update_conversation(
                conversation_id=conversation_id,
                conversation_data={
                    "responses": response_ids,
                    "last_updated": datetime.utcnow()
                }
            )
            
            # Publish conversation updated event
            event = Event(
                event_id=str(uuid.uuid4()),
                event_type=EventType.CONVERSATION_UPDATED,
                producer="conversation-management-service",
                payload=ConversationUpdatedPayload(
                    conversation_id=conversation_id,
                    lead_id=lead_id,
                    new_response_id=response_id,
                    metadata={}
                ).dict()
            )
            
            await event_producer.produce_event(event)
            
            logger.info(f"Updated conversation {conversation_id} with response {response_id}")
            
        else:
            # Create a new conversation
            conversation_id = str(uuid.uuid4())
            
            # Create conversation data
            conversation_data = {
                "conversation_id": conversation_id,
                "lead_id": lead_id,
                "responses": [response_id],
                "started_at": datetime.utcnow(),
                "last_updated": datetime.utcnow(),
                "status": "active",
                "channel": channel,
                "metadata": {},
                "tags": [],
                "assigned_to": None
            }
            
            # Create conversation
            await conversation_service.create_conversation(conversation_data)
            
            # Publish conversation created event
            event = Event(
                event_id=str(uuid.uuid4()),
                event_type=EventType.CONVERSATION_CREATED,
                producer="conversation-management-service",
                payload=ConversationCreatedPayload(
                    conversation_id=conversation_id,
                    lead_id=lead_id,
                    initial_response_id=response_id,
                    channel=channel,
                    metadata={}
                ).dict()
            )
            
            await event_producer.produce_event(event)
            
            logger.info(f"Created conversation {conversation_id} with response {response_id}")
            
    except Exception as e:
        logger.error(f"Error processing response received event: {str(e)}")


# API endpoints
@app.post("/api/v1/conversations", response_model=ConversationResponse)
async def create_conversation(
    request: CreateConversationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new conversation.
    
    This endpoint creates a new conversation and publishes an event.
    """
    logger.info(f"Creating conversation for lead {request.lead_id}", props={
        "user": current_user.username,
        "channel": request.channel,
        "response_count": len(request.response_ids)
    })
    
    try:
        # Create conversation ID
        conversation_id = str(uuid.uuid4())
        
        # Create conversation data
        conversation_data = {
            "conversation_id": conversation_id,
            "lead_id": request.lead_id,
            "responses": request.response_ids,
            "started_at": datetime.utcnow(),
            "last_updated": datetime.utcnow(),
            "status": request.status,
            "channel": request.channel,
            "metadata": request.metadata,
            "tags": request.tags,
            "assigned_to": request.assigned_to
        }
        
        # Create conversation
        await conversation_service.create_conversation(conversation_data)
        
        # Publish conversation created event
        event = Event(
            event_id=str(uuid.uuid4()),
            event_type=EventType.CONVERSATION_CREATED,
            producer="conversation-management-service",
            payload=ConversationCreatedPayload(
                conversation_id=conversation_id,
                lead_id=request.lead_id,
                initial_response_id=request.response_ids[0] if request.response_ids else None,
                channel=request.channel,
                metadata=request.metadata
            ).dict()
        )
        
        await event_producer.produce_event(event)
        
        return ConversationResponse(
            conversation_id=conversation_id,
            message="Conversation created successfully"
        )
        
    except Exception as e:
        logger.error(f"Error creating conversation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating conversation: {str(e)}"
        )


@app.get("/api/v1/conversations", response_model=List[Conversation])
async def get_conversations(
    current_user: User = Depends(get_current_active_user),
    lead_id: Optional[str] = None,
    channel: Optional[ResponseChannel] = None,
    status: Optional[str] = None,
    assigned_to: Optional[str] = None,
    skip: int = 0,
    limit: int = 100
):
    """
    Get a list of conversations.
    
    This endpoint returns a list of conversations with optional filtering.
    """
    logger.info(f"Getting conversations", props={
        "user": current_user.username,
        "lead_id": lead_id,
        "channel": channel,
        "status": status,
        "assigned_to": assigned_to,
        "skip": skip,
        "limit": limit
    })
    
    try:
        # Get conversations
        conversations = await conversation_service.get_conversations(
            lead_id=lead_id,
            channel=channel,
            status=status,
            assigned_to=assigned_to,
            skip=skip,
            limit=limit
        )
        
        return conversations
        
    except Exception as e:
        logger.error(f"Error getting conversations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting conversations: {str(e)}"
        )


@app.get("/api/v1/conversations/{conversation_id}", response_model=Conversation)
async def get_conversation(
    conversation_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a conversation by ID.
    
    This endpoint returns a conversation by its ID.
    """
    logger.info(f"Getting conversation {conversation_id}", props={"user": current_user.username})
    
    try:
        # Get conversation
        conversation = await conversation_service.get_conversation(conversation_id)
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Conversation {conversation_id} not found"
            )
        
        return conversation
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting conversation: {str(e)}"
        )


@app.put("/api/v1/conversations/{conversation_id}", response_model=ConversationResponse)
async def update_conversation(
    conversation_id: str,
    request: UpdateConversationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Update a conversation.
    
    This endpoint updates a conversation by its ID and publishes an event.
    """
    logger.info(f"Updating conversation {conversation_id}", props={"user": current_user.username})
    
    try:
        # Prepare update data
        update_data = request.dict(exclude_unset=True)
        
        # Add last updated timestamp
        update_data["last_updated"] = datetime.utcnow()
        
        # Update conversation
        updated = await conversation_service.update_conversation(
            conversation_id=conversation_id,
            conversation_data=update_data
        )
        
        if not updated:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Conversation {conversation_id} not found"
            )
        
        # Get the conversation to get the lead ID
        conversation = await conversation_service.get_conversation(conversation_id)
        
        # Publish conversation updated event
        event = Event(
            event_id=str(uuid.uuid4()),
            event_type=EventType.CONVERSATION_UPDATED,
            producer="conversation-management-service",
            payload=ConversationUpdatedPayload(
                conversation_id=conversation_id,
                lead_id=conversation.lead_id,
                new_response_id=update_data.get("responses", [])[-1] if update_data.get("responses") else None,
                status=update_data.get("status"),
                metadata=update_data.get("metadata", {})
            ).dict()
        )
        
        await event_producer.produce_event(event)
        
        return ConversationResponse(
            conversation_id=conversation_id,
            message="Conversation updated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating conversation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating conversation: {str(e)}"
        )


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}


# Start the event consumer when the app starts
@app.on_event("startup")
async def startup_event():
    """Start the event consumer when the app starts."""
    await event_consumer.start(process_response_received)


# Stop the event consumer when the app stops
@app.on_event("shutdown")
async def shutdown_event():
    """Stop the event consumer when the app stops."""
    await event_consumer.stop()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)
