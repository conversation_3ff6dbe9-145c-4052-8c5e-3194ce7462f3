from abc import ABC, abstractmethod
from typing import Dict, Any, <PERSON>ple
import asyncio

# Import shared libraries
import sys
sys.path.append("../../../")
from libs.logging import get_service_logger


class BaseModel(ABC):
    """
    Base class for AI models.
    """
    
    def __init__(self, name: str):
        """
        Initialize the base model.
        
        Args:
            name: Model name
        """
        self.name = name
        self.logger = get_service_logger("ai-orchestrator", f"model.{name}")
    
    @abstractmethod
    async def predict(self, input_data: Dict[str, Any], context: Dict[str, Any]) -> Tuple[Dict[str, Any], float]:
        """
        Make a prediction.
        
        Args:
            input_data: Input data
            context: Context data
            
        Returns:
            Tuple of output data and confidence score
        """
        pass
