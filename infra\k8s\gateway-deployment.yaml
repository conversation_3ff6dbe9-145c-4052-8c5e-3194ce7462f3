apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway
  labels:
    app: gateway
spec:
  replicas: 2
  selector:
    matchLabels:
      app: gateway
  template:
    metadata:
      labels:
        app: gateway
    spec:
      containers:
      - name: gateway
        image: ${DOCKER_REGISTRY}/leadgen-gateway:latest
        ports:
        - containerPort: 8000
        env:
        - name: FORM_DISCOVERY_URL
          value: "http://form-discovery:8001"
        - name: FORM_ANALYSIS_URL
          value: "http://form-analysis:8002"
        - name: LEAD_MANAGEMENT_URL
          value: "http://lead-management:8003"
        - name: RESPONSE_INGESTION_URL
          value: "http://response-ingestion:8004"
        - name: CONVERSATION_MANAGEMENT_URL
          value: "http://conversation-management:8005"
        - name: AI_ORCHESTRATOR_URL
          value: "http://ai-orchestrator:8006"
        - name: ANALYTICS_URL
          value: "http://analytics:8007"
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: gateway-secrets
              key: secret-key
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "100m"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: gateway
spec:
  selector:
    app: gateway
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gateway-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  rules:
  - host: api.leadgen.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway
            port:
              number: 8000
  tls:
  - hosts:
    - api.leadgen.example.com
    secretName: leadgen-tls-secret
