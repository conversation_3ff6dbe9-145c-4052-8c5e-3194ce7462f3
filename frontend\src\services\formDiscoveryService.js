/**
 * Form Discovery & Submission Service API Client
 *
 * This client provides methods to interact with the Form Discovery & Submission Service.
 */

import axios from 'axios';

// Base URL for the Form Discovery & Submission Service
const API_BASE_URL = import.meta.env.VITE_FORM_DISCOVERY_API_URL || 'http://localhost:8001';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests if available
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

/**
 * Form Discovery Service
 */
const formDiscoveryService = {
  /**
   * Discover forms on a website
   *
   * @param {string} url - URL to discover forms on
   * @param {Object} options - Additional options
   * @param {string} options.companyName - Company name
   * @param {string} options.industry - Industry
   * @param {number} options.priority - Priority (1-10)
   * @param {number} options.maxPages - Maximum pages to crawl
   * @param {number} options.maxDepth - Maximum crawl depth
   * @returns {Promise<Object>} - Discovery job details
   */
  discoverForms: async (url, options = {}) => {
    try {
      const response = await apiClient.post('/api/v1/discover', {
        url,
        company_name: options.companyName,
        industry: options.industry,
        priority: options.priority || 1,
        max_pages: options.maxPages || 10,
        max_depth: options.maxDepth || 3,
      });
      return response.data;
    } catch (error) {
      console.error('Error discovering forms:', error);
      throw error;
    }
  },

  /**
   * Get the status of a form discovery job
   *
   * @param {string} jobId - Job ID
   * @returns {Promise<Object>} - Job status
   */
  getDiscoveryStatus: async (jobId) => {
    try {
      const response = await apiClient.get(`/api/v1/discover/${jobId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting discovery status:', error);
      throw error;
    }
  },

  /**
   * Submit a form
   *
   * @param {string} url - URL to submit form on
   * @param {Object} leadData - Lead data to submit
   * @param {string} formId - Form ID (optional)
   * @param {Object} options - Additional options
   * @param {number} options.timeout - Timeout in seconds
   * @param {boolean} options.useAiReasoning - Whether to use AI reasoning
   * @returns {Promise<Object>} - Submission job details
   */
  submitForm: async (url, leadData, formId = null, options = {}) => {
    try {
      const response = await apiClient.post('/api/v1/submit', {
        url,
        form_id: formId,
        lead_data: leadData,
        timeout: options.timeout || 60,
        use_ai_reasoning: options.useAiReasoning !== false,
      });
      return response.data;
    } catch (error) {
      console.error('Error submitting form:', error);
      throw error;
    }
  },

  /**
   * Get the status of a form submission job
   *
   * @param {string} jobId - Job ID
   * @returns {Promise<Object>} - Job status
   */
  getSubmissionStatus: async (jobId) => {
    try {
      const response = await apiClient.get(`/api/v1/submit/${jobId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting submission status:', error);
      throw error;
    }
  },

  /**
   * Discover and submit a form in one operation
   *
   * @param {string} url - URL to discover and submit form on
   * @param {Object} leadData - Lead data to submit
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Operation result
   */
  discoverAndSubmitForm: async (url, leadData, options = {}) => {
    try {
      // First discover forms
      const discoveryResult = await formDiscoveryService.discoverForms(url, options);
      const jobId = discoveryResult.job_id;

      // Poll for discovery completion
      let discoveryStatus;
      let attempts = 0;
      const maxAttempts = 30; // 30 attempts with 2-second delay = 60 seconds max

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2-second delay
        discoveryStatus = await formDiscoveryService.getDiscoveryStatus(jobId);

        if (discoveryStatus.completed || discoveryStatus.status === 'failed') {
          break;
        }

        attempts++;
      }

      if (!discoveryStatus.completed) {
        throw new Error('Form discovery timed out');
      }

      if (discoveryStatus.status === 'failed') {
        throw new Error(`Form discovery failed: ${discoveryStatus.error}`);
      }

      if (discoveryStatus.forms_discovered === 0) {
        throw new Error('No forms discovered');
      }

      // Submit the form
      const submissionResult = await formDiscoveryService.submitForm(url, leadData, null, options);

      // Poll for submission completion
      let submissionStatus;
      attempts = 0;

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2-second delay
        submissionStatus = await formDiscoveryService.getSubmissionStatus(submissionResult.job_id);

        if (submissionStatus.status === 'completed' || submissionStatus.status === 'failed') {
          break;
        }

        attempts++;
      }

      if (submissionStatus.status === 'failed') {
        throw new Error(`Form submission failed: ${submissionStatus.error}`);
      }

      return submissionStatus;
    } catch (error) {
      console.error('Error in discover and submit operation:', error);
      throw error;
    }
  },
};

export default formDiscoveryService;
