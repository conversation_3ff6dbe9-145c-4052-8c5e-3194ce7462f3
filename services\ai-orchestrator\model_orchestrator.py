from typing import Dict, Any, Optional, List
import uuid
import time
import asyncio
from datetime import datetime

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.interfaces import AIModelType, AIModelRequest, AIModelResponse

# Import service-specific modules
from .models.form_detection_model import FormDetectionModel
from .models.field_classification_model import FieldClassificationModel
from .models.decision_making_model import DecisionMakingModel
from .models.text_generation_model import TextGenerationModel
from .models.captcha_solver_model import CaptchaSolverModel

# Initialize logger
logger = get_service_logger("ai-orchestrator", "model-orchestrator")


class ModelOrchestrator:
    """
    Orchestrator for AI models.
    """
    
    def __init__(self):
        """Initialize the model orchestrator."""
        self.logger = logger
        
        # Initialize models
        self.models = {
            AIModelType.FORM_DETECTION: FormDetectionModel(),
            AIModelType.FIELD_CLASSIFICATION: FieldClassificationModel(),
            AIModelType.DECISION_MAKING: DecisionMakingModel(),
            AIModelType.TEXT_GENERATION: TextGenerationModel(),
            AIModelType.CAPTCHA_SOLVER: CaptchaSolverModel()
        }
        
        # Store model responses
        self.responses = {}
    
    async def process_model_request(self, model_request: AIModelRequest) -> AIModelResponse:
        """
        Process a model request.
        
        Args:
            model_request: Model request to process
            
        Returns:
            Model response
        """
        self.logger.info(f"Processing model request {model_request.request_id}", props={
            "model_type": model_request.model_type
        })
        
        # Get model
        model = self.models.get(model_request.model_type)
        
        if not model:
            self.logger.error(f"Model {model_request.model_type} not found")
            raise ValueError(f"Model {model_request.model_type} not found")
        
        # Process request
        start_time = time.time()
        
        try:
            # Run model
            output_data, confidence = await model.predict(
                input_data=model_request.input_data,
                context=model_request.context
            )
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Create response
            response = AIModelResponse(
                response_id=str(uuid.uuid4()),
                request_id=model_request.request_id,
                model_type=model_request.model_type,
                output_data=output_data,
                confidence=confidence,
                processing_time=processing_time,
                timestamp=datetime.utcnow()
            )
            
            # Store response
            self.responses[model_request.request_id] = response
            
            self.logger.info(f"Processed model request {model_request.request_id}", props={
                "model_type": model_request.model_type,
                "response_id": response.response_id,
                "confidence": confidence,
                "processing_time": processing_time
            })
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing model request: {str(e)}")
            raise
    
    async def get_model_response(self, request_id: str) -> Optional[AIModelResponse]:
        """
        Get a model response by request ID.
        
        Args:
            request_id: Request ID
            
        Returns:
            Model response or None if not found
        """
        self.logger.info(f"Getting model response for request {request_id}")
        
        # Get response from cache
        response = self.responses.get(request_id)
        
        if not response:
            self.logger.warning(f"Model response for request {request_id} not found")
            return None
        
        self.logger.info(f"Got model response for request {request_id}")
        
        return response
