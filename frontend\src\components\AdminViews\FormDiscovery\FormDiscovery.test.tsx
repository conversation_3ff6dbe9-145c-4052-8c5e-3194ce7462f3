import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import FormDiscovery from './FormDiscovery';

describe('FormDiscovery Component', () => {
  test('renders form discovery header', () => {
    render(<FormDiscovery />);
    const headerElement = screen.getByText(/Form Discovery/i);
    expect(headerElement).toBeInTheDocument();
  });

  test('renders discovery form with required fields', () => {
    render(<FormDiscovery />);
    expect(screen.getByLabelText(/Website URL/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Company Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Industry/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Priority/i)).toBeInTheDocument();
    expect(screen.getByText(/Discover Forms/i)).toBeInTheDocument();
  });

  test('renders discovered forms table', () => {
    render(<FormDiscovery />);
    expect(screen.getByText(/Discovered Forms/i)).toBeInTheDocument();
    
    // Check for table headers
    expect(screen.getByText(/URL/i)).toBeInTheDocument();
    expect(screen.getByText(/Title/i)).toBeInTheDocument();
    expect(screen.getByText(/Discovered At/i)).toBeInTheDocument();
    expect(screen.getByText(/Status/i)).toBeInTheDocument();
    expect(screen.getByText(/Fields/i)).toBeInTheDocument();
    expect(screen.getByText(/Features/i)).toBeInTheDocument();
    expect(screen.getByText(/Actions/i)).toBeInTheDocument();
  });

  test('allows entering URL and submitting form', () => {
    render(<FormDiscovery />);
    
    // Get form elements
    const urlInput = screen.getByLabelText(/Website URL/i);
    const submitButton = screen.getByText(/Discover Forms/i);
    
    // Enter URL
    fireEvent.change(urlInput, { target: { value: 'https://example.com' } });
    
    // Submit form
    fireEvent.click(submitButton);
    
    // Check if button text changes to indicate loading
    expect(screen.getByText(/Discovering.../i)).toBeInTheDocument();
  });
});
