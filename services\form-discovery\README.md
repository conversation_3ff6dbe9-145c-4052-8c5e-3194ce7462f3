# Form Discovery & Submission Service

This service is responsible for discovering and submitting lead forms on websites. It uses AI-powered reasoning to analyze forms and determine how to fill them out.

## Architecture

The service follows a microservices architecture and is built with FastAPI. It uses LangGraph for AI-powered form analysis and filling workflows.

### Key Components

- **Form Agent**: Unified agent for form discovery and submission
- **Event Producer**: Produces events to Kafka with retry logic and monitoring
- **API Endpoints**: RESTful API for form discovery and submission

## Features

- **Form Discovery**: Discovers lead forms on websites using multiple strategies
- **Form Analysis**: Analyzes form fields and determines how to fill them out
- **Form Submission**: Submits lead data to forms with AI-powered reasoning
- **Event Production**: Produces events to Kafka for downstream processing
- **Retry Logic**: Implements retry logic for form submission and event production
- **Monitoring**: Tracks metrics for form discovery and submission

## API Endpoints

### Form Discovery

- `POST /api/v1/discover`: Discover forms on a website
- `GET /api/v1/discover/{job_id}`: Get the status of a form discovery job

### Form Submission

- `POST /api/v1/submit`: Submit a form
- `GET /api/v1/submit/{job_id}`: Get the status of a form submission job

### Health Check

- `GET /health`: Health check endpoint

## Usage

### Form Discovery

```python
import requests

# Discover forms on a website
response = requests.post(
    "http://localhost:8001/api/v1/discover",
    json={
        "url": "http://www.example.com",
        "company_name": "Example Company",
        "industry": "Technology",
        "priority": 1,
        "max_pages": 10,
        "max_depth": 3
    },
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

# Get the job ID
job_id = response.json()["job_id"]

# Check the status of the job
status_response = requests.get(
    f"http://localhost:8001/api/v1/discover/{job_id}",
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

# Print the status
print(status_response.json())
```

### Form Submission

```python
import requests

# Submit a form
response = requests.post(
    "http://localhost:8001/api/v1/submit",
    json={
        "url": "http://www.example.com/contact",
        "form_id": "form_123",  # Optional
        "lead_data": {
            "name": "John Smith",
            "email": "<EMAIL>",
            "phone": "************",
            "message": "I'm interested in learning more about your services."
        },
        "timeout": 60,
        "use_ai_reasoning": True
    },
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

# Get the job ID
job_id = response.json()["job_id"]

# Check the status of the job
status_response = requests.get(
    f"http://localhost:8001/api/v1/submit/{job_id}",
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

# Print the status
print(status_response.json())
```

## Development

### Prerequisites

- Python 3.9+
- FastAPI
- LangGraph
- Playwright

### Installation

1. Clone the repository
2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. Install Playwright browsers:

```bash
playwright install
```

### Running the Service

```bash
uvicorn main:app --host 0.0.0.0 --port 8001 --reload
```

### Testing

```bash
# Run the test script
python test_form_agent.py
```

## Optimization Notes

The service has been optimized for:

1. **Code Consolidation**: Unified form discovery and submission into a single agent
2. **Error Handling**: Improved error handling and retry logic
3. **Monitoring**: Added metrics tracking for form discovery and submission
4. **Performance**: Optimized form discovery strategies for better performance
5. **Robustness**: Enhanced event production with retry logic and monitoring

## Future Improvements

1. **Database Integration**: Store results in a database instead of in-memory
2. **Kafka Integration**: Implement real Kafka integration for event production
3. **Browser Pool**: Implement a real browser pool for form discovery and submission
4. **AI Model Improvements**: Enhance AI reasoning for form analysis and filling
5. **Monitoring Dashboard**: Create a dashboard for monitoring service metrics
