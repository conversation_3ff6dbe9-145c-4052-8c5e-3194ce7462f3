import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  CircularProgress,
  Alert,
  Divider,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  FormControlLabel,
  Checkbox,
  Tab,
  Tabs
} from '@mui/material';
import {
  Business as BusinessIcon,
  Language as LanguageIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Facebook as FacebookIcon,
  LinkedIn as LinkedInIcon,
  Instagram as InstagramIcon,
  YouTube as YouTubeIcon,
  Twitter as TwitterIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  ContentCopy as ContentCopyIcon
} from '@mui/icons-material';
import leadEnrichmentService from '../../services/leadEnrichmentService';
import supabaseService from '../../services/supabaseService';

/**
 * Single Company Importer Component
 *
 * This component allows users to import a single company by entering a website URL.
 * It uses the lead enrichment service to automatically gather company and contact information.
 */
const SingleCompanyImporter = () => {
  // State for website URL input
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [companyName, setCompanyName] = useState('');

  // State for loading and errors
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // State for enriched lead data
  const [enrichedLead, setEnrichedLead] = useState(null);
  const [savedLead, setSavedLead] = useState(null);

  // State for manual editing
  const [isEditing, setIsEditing] = useState(false);
  const [editedLead, setEditedLead] = useState(null);

  // State for enrichment options
  const [useDeepScan, setUseDeepScan] = useState(false);

  // Handle website URL change
  const handleWebsiteUrlChange = (e) => {
    setWebsiteUrl(e.target.value);
    // Reset states when input changes
    setEnrichedLead(null);
    setSavedLead(null);
    setError(null);
    setSuccess(null);
  };

  // Handle company name change
  const handleCompanyNameChange = (e) => {
    setCompanyName(e.target.value);
  };

  // Handle enrichment
  const handleEnrichWebsite = async () => {
    if (!websiteUrl) {
      setError('Please enter a website URL');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);
    setEnrichedLead(null);
    setSavedLead(null);
    setIsEditing(false);
    setEditedLead(null);

    try {
      // Ensure website URL has protocol
      let normalizedUrl = websiteUrl;
      if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
        normalizedUrl = 'https://' + normalizedUrl;
      }

      // Create a basic lead object with the website URL
      const basicLead = {
        website: normalizedUrl,
        company: companyName || ''
      };

      // Enrich the lead
      const enrichmentOptions = {
        deepScan: useDeepScan,
        timeout: useDeepScan ? 30000 : 15000 // Longer timeout for deep scan
      };

      const enriched = await leadEnrichmentService.enrichLead(basicLead, enrichmentOptions);

      // Set the enriched lead
      setEnrichedLead(enriched);

      // Also set as edited lead for potential manual editing
      setEditedLead(enriched);

      // Check if we got meaningful data
      const hasMinimalData = enriched.company || enriched.email || enriched.phone ||
                            enriched.facebook || enriched.linkedin || enriched.instagram;

      if (hasMinimalData) {
        setSuccess('Successfully enriched lead data from website');
      } else {
        setSuccess('Website scanned, but limited information was found. You can manually add details below.');
        // Automatically enable editing mode if we didn't find much
        setIsEditing(true);
      }
    } catch (err) {
      console.error('Error enriching lead:', err);
      setError(`Failed to enrich lead: ${err.message}`);

      // Still create a basic lead for manual editing
      const basicLead = {
        website: normalizedUrl || websiteUrl,
        company: companyName || extractCompanyFromUrl(websiteUrl) || ''
      };
      setEnrichedLead(basicLead);
      setEditedLead(basicLead);
      setIsEditing(true);
    } finally {
      setLoading(false);
    }
  };

  // Extract company name from URL
  const extractCompanyFromUrl = (url) => {
    try {
      // Remove protocol and www
      let domain = url.replace(/^https?:\/\//, '').replace(/^www\./, '');

      // Get the domain name without extension and subdomain
      domain = domain.split('/')[0].split('.')[0];

      // Convert to title case and replace dashes/underscores with spaces
      return domain
        .replace(/[-_]/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
    } catch (err) {
      return '';
    }
  };

  // Toggle edit mode
  const toggleEditMode = () => {
    if (!isEditing) {
      // Entering edit mode
      setEditedLead(enrichedLead);
      setIsEditing(true);
    } else {
      // Exiting edit mode, apply changes
      setEnrichedLead(editedLead);
      setIsEditing(false);
    }
  };

  // Handle edit field change
  const handleEditFieldChange = (field, value) => {
    setEditedLead(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle save to database
  const handleSaveToDatabase = async () => {
    const leadToSave = isEditing ? editedLead : enrichedLead;

    if (!leadToSave) {
      setError('No lead data to save');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Save the lead to the database
      const savedLeadData = await supabaseService.createLead({
        ...leadToSave,
        source: 'single_import',
        status: 'new',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      setSavedLead(savedLeadData);
      setIsEditing(false);
      setSuccess('Lead successfully saved to database');
    } catch (err) {
      console.error('Error saving lead:', err);
      setError(`Failed to save lead: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Try again with deep scan
  const handleTryDeepScan = () => {
    setUseDeepScan(true);
    handleEnrichWebsite();
  };

  // Copy to clipboard helper
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Single Company Importer
      </Typography>

      <Typography variant="body2" paragraph>
        Enter a company website URL to automatically gather company and contact information.
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={8}>
            <TextField
              label="Website URL"
              variant="outlined"
              fullWidth
              value={websiteUrl}
              onChange={handleWebsiteUrlChange}
              placeholder="https://example.com"
              disabled={loading}
              helperText="Enter the company's website URL"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              label="Company Name (Optional)"
              variant="outlined"
              fullWidth
              value={companyName}
              onChange={handleCompanyNameChange}
              placeholder="Acme Inc."
              disabled={loading}
              helperText="If left blank, we'll try to detect it"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={useDeepScan}
                  onChange={(e) => setUseDeepScan(e.target.checked)}
                  disabled={loading}
                />
              }
              label="Use Deep Scan (more thorough but slower)"
            />
          </Grid>

          <Grid item xs={12} sm={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleEnrichWebsite}
              disabled={loading || !websiteUrl}
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
              sx={{ minWidth: 200 }}
            >
              {loading ? 'Processing...' : 'Enrich from Website'}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      {enrichedLead && (
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              {isEditing ? 'Edit Lead Data' : 'Enriched Lead Data'}
            </Typography>

            <Box sx={{ display: 'flex', gap: 1 }}>
              {!savedLead && (
                <Button
                  variant="outlined"
                  color={isEditing ? "success" : "primary"}
                  onClick={toggleEditMode}
                  disabled={loading}
                >
                  {isEditing ? 'Apply Changes' : 'Edit Manually'}
                </Button>
              )}

              {!isEditing && !savedLead && !useDeepScan && (
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={handleTryDeepScan}
                  disabled={loading}
                  startIcon={<RefreshIcon />}
                >
                  Try Deep Scan
                </Button>
              )}
            </Box>
          </Box>

          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <List dense>
                    <ListItem>
                      <ListItemIcon>
                        <BusinessIcon color="primary" />
                      </ListItemIcon>
                      {isEditing ? (
                        <TextField
                          label="Company"
                          variant="outlined"
                          fullWidth
                          size="small"
                          value={editedLead?.company || ''}
                          onChange={(e) => handleEditFieldChange('company', e.target.value)}
                          placeholder="Company name"
                          sx={{ ml: 1 }}
                        />
                      ) : (
                        <>
                          <ListItemText
                            primary="Company"
                            secondary={enrichedLead.company || 'Not detected'}
                          />
                          {enrichedLead.company && (
                            <Tooltip title="Copy to clipboard">
                              <IconButton
                                size="small"
                                onClick={() => copyToClipboard(enrichedLead.company)}
                              >
                                <ContentCopyIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </>
                      )}
                    </ListItem>

                    <ListItem>
                      <ListItemIcon>
                        <LanguageIcon color="primary" />
                      </ListItemIcon>
                      {isEditing ? (
                        <TextField
                          label="Website"
                          variant="outlined"
                          fullWidth
                          size="small"
                          value={editedLead?.website || ''}
                          onChange={(e) => handleEditFieldChange('website', e.target.value)}
                          placeholder="https://example.com"
                          sx={{ ml: 1 }}
                        />
                      ) : (
                        <>
                          <ListItemText
                            primary="Website"
                            secondary={enrichedLead.website || 'Not detected'}
                          />
                          {enrichedLead.website && (
                            <Tooltip title="Copy to clipboard">
                              <IconButton
                                size="small"
                                onClick={() => copyToClipboard(enrichedLead.website)}
                              >
                                <ContentCopyIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </>
                      )}
                    </ListItem>

                    <ListItem>
                      <ListItemIcon>
                        <EmailIcon color="primary" />
                      </ListItemIcon>
                      {isEditing ? (
                        <TextField
                          label="Email"
                          variant="outlined"
                          fullWidth
                          size="small"
                          value={editedLead?.email || ''}
                          onChange={(e) => handleEditFieldChange('email', e.target.value)}
                          placeholder="<EMAIL>"
                          sx={{ ml: 1 }}
                        />
                      ) : (
                        <>
                          <ListItemText
                            primary="Email"
                            secondary={enrichedLead.email || 'Not detected'}
                          />
                          {enrichedLead.email && (
                            <Tooltip title="Copy to clipboard">
                              <IconButton
                                size="small"
                                onClick={() => copyToClipboard(enrichedLead.email)}
                              >
                                <ContentCopyIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </>
                      )}
                    </ListItem>

                    <ListItem>
                      <ListItemIcon>
                        <PhoneIcon color="primary" />
                      </ListItemIcon>
                      {isEditing ? (
                        <TextField
                          label="Phone"
                          variant="outlined"
                          fullWidth
                          size="small"
                          value={editedLead?.phone || ''}
                          onChange={(e) => handleEditFieldChange('phone', e.target.value)}
                          placeholder="(*************"
                          sx={{ ml: 1 }}
                        />
                      ) : (
                        <>
                          <ListItemText
                            primary="Phone"
                            secondary={enrichedLead.phone || 'Not detected'}
                          />
                          {enrichedLead.phone && (
                            <Tooltip title="Copy to clipboard">
                              <IconButton
                                size="small"
                                onClick={() => copyToClipboard(enrichedLead.phone)}
                              >
                                <ContentCopyIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </>
                      )}
                    </ListItem>
                  </List>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Social Media
                  </Typography>

                  {isEditing ? (
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <TextField
                          label="Facebook"
                          variant="outlined"
                          fullWidth
                          size="small"
                          value={editedLead?.facebook || ''}
                          onChange={(e) => handleEditFieldChange('facebook', e.target.value)}
                          placeholder="https://facebook.com/company"
                          InputProps={{
                            startAdornment: <FacebookIcon fontSize="small" sx={{ mr: 1, color: '#1877F2' }} />
                          }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          label="LinkedIn"
                          variant="outlined"
                          fullWidth
                          size="small"
                          value={editedLead?.linkedin || ''}
                          onChange={(e) => handleEditFieldChange('linkedin', e.target.value)}
                          placeholder="https://linkedin.com/company/name"
                          InputProps={{
                            startAdornment: <LinkedInIcon fontSize="small" sx={{ mr: 1, color: '#0A66C2' }} />
                          }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          label="Instagram"
                          variant="outlined"
                          fullWidth
                          size="small"
                          value={editedLead?.instagram || ''}
                          onChange={(e) => handleEditFieldChange('instagram', e.target.value)}
                          placeholder="https://instagram.com/company"
                          InputProps={{
                            startAdornment: <InstagramIcon fontSize="small" sx={{ mr: 1, color: '#E4405F' }} />
                          }}
                        />
                      </Grid>
                    </Grid>
                  ) : (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {enrichedLead.facebook && (
                        <Chip
                          icon={<FacebookIcon />}
                          label="Facebook"
                          component="a"
                          href={enrichedLead.facebook}
                          target="_blank"
                          clickable
                        />
                      )}

                      {enrichedLead.linkedin && (
                        <Chip
                          icon={<LinkedInIcon />}
                          label="LinkedIn"
                          component="a"
                          href={enrichedLead.linkedin}
                          target="_blank"
                          clickable
                        />
                      )}

                      {enrichedLead.instagram && (
                        <Chip
                          icon={<InstagramIcon />}
                          label="Instagram"
                          component="a"
                          href={enrichedLead.instagram}
                          target="_blank"
                          clickable
                        />
                      )}

                      {enrichedLead.youtube && (
                        <Chip
                          icon={<YouTubeIcon />}
                          label="YouTube"
                          component="a"
                          href={enrichedLead.youtube}
                          target="_blank"
                          clickable
                        />
                      )}

                      {enrichedLead.twitter && (
                        <Chip
                          icon={<TwitterIcon />}
                          label="Twitter"
                          component="a"
                          href={enrichedLead.twitter}
                          target="_blank"
                          clickable
                        />
                      )}

                      {!enrichedLead.facebook && !enrichedLead.linkedin &&
                       !enrichedLead.instagram && !enrichedLead.youtube &&
                       !enrichedLead.twitter && (
                        <Typography variant="body2" color="text.secondary">
                          No social media profiles detected
                        </Typography>
                      )}
                    </Box>
                  )}

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="subtitle2" sx={{ mr: 1 }}>
                      Facebook Pixel:
                    </Typography>
                    {isEditing ? (
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={editedLead?.facebook_pixel || false}
                            onChange={(e) => handleEditFieldChange('facebook_pixel', e.target.checked)}
                          />
                        }
                        label="Detected"
                      />
                    ) : (
                      enrichedLead.facebook_pixel ? (
                        <Chip
                          icon={<CheckCircleIcon />}
                          label="Detected"
                          color="success"
                          size="small"
                        />
                      ) : (
                        <Chip
                          icon={<ErrorIcon />}
                          label="Not Detected"
                          color="default"
                          size="small"
                        />
                      )
                    )}
                  </Box>
                </Grid>
              </Grid>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                {!isEditing && !savedLead && (
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={() => {
                      setWebsiteUrl('');
                      setCompanyName('');
                      setEnrichedLead(null);
                      setEditedLead(null);
                      setSavedLead(null);
                      setError(null);
                      setSuccess(null);
                      setIsEditing(false);
                      setUseDeepScan(false);
                    }}
                  >
                    Start Over
                  </Button>
                )}

                <Box sx={{ ml: 'auto' }}>
                  {isEditing && (
                    <Button
                      variant="outlined"
                      color="error"
                      onClick={() => {
                        setEditedLead(enrichedLead);
                        setIsEditing(false);
                      }}
                      sx={{ mr: 1 }}
                    >
                      Cancel
                    </Button>
                  )}

                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSaveToDatabase}
                    disabled={loading || savedLead}
                    startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
                  >
                    {savedLead ? 'Lead Saved' : 'Save to Database'}
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>

          {!isEditing && !savedLead && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Tip:</strong> If the automatic enrichment didn't find all the information you need,
                click "Edit Manually" to add or modify the lead data before saving.
              </Typography>
            </Alert>
          )}
        </Box>
      )}

      {savedLead && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Lead successfully saved with ID: {savedLead.id}
        </Alert>
      )}
    </Box>
  );
};

export default SingleCompanyImporter;
