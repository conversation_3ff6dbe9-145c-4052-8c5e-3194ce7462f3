/**
 * Outreach Service
 * 
 * Service for interacting with the outreach agent API
 */

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8007';

class OutreachService {
  /**
   * Generate outreach messages for a lead
   */
  async generateOutreach(leadId, auditReportId, options = {}) {
    const requestData = {
      lead_id: leadId,
      audit_report_id: auditReportId,
      message_types: options.messageTypes || ['email', 'linkedin'],
      tone: options.tone || 'professional',
      include_audit_link: options.includeAuditLink !== false
    };

    try {
      const response = await fetch(`${API_BASE_URL}/generate-outreach`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error generating outreach:', error);
      throw error;
    }
  }

  /**
   * Get outreach messages for a lead
   */
  async getOutreachMessages(leadId) {
    try {
      const response = await fetch(`${API_BASE_URL}/outreach-messages/${leadId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching outreach messages:', error);
      throw error;
    }
  }

  /**
   * Send an outreach message
   */
  async sendMessage(messageId) {
    try {
      const response = await fetch(`${API_BASE_URL}/send-message/${messageId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Update message status
   */
  async updateMessageStatus(messageId, status) {
    try {
      const response = await fetch(`${API_BASE_URL}/outreach-messages/${messageId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error updating message status:', error);
      throw error;
    }
  }

  /**
   * Get audit report for a lead
   */
  async getAuditReport(leadId) {
    try {
      const response = await fetch(`${API_BASE_URL}/audit-reports/${leadId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching audit report:', error);
      throw error;
    }
  }

  /**
   * Get lead state/progress
   */
  async getLeadState(leadId) {
    try {
      const response = await fetch(`${API_BASE_URL}/lead-states/${leadId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching lead state:', error);
      throw error;
    }
  }

  /**
   * Health check for the outreach service
   */
  async healthCheck() {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error checking service health:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const outreachService = new OutreachService();
export default outreachService;
