/**
 * Supabase Database Reset Script
 *
 * This script will delete all records from all tables in the Supabase database.
 * Use with extreme caution as this action cannot be undone.
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY || process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL and key must be provided in .env file');
  console.error('Make sure you have SUPABASE_URL and SUPABASE_KEY or SUPABASE_SERVICE_KEY defined');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Get all tables from the database
 */
async function getAllTables() {
  try {
    // Query the information_schema to get all tables in the public schema
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');

    if (error) throw error;

    return data.map(table => table.table_name);
  } catch (error) {
    console.error('Error fetching tables:', error.message);
    return [];
  }
}

/**
 * Delete all records from a table
 */
async function clearTable(tableName) {
  try {
    console.log(`Deleting all records from table: ${tableName}`);
    const { error } = await supabase
      .from(tableName)
      .delete()
      .neq('id', 0); // This will delete all records (since all ids are not equal to 0)

    if (error) {
      // If the delete operation fails, try with a different approach
      console.log(`Error with standard delete for ${tableName}, trying alternative approach...`);
      const { error: truncateError } = await supabase.rpc('truncate_table', { table_name: tableName });

      if (truncateError) {
        throw truncateError;
      }
    }

    console.log(`✓ Successfully cleared table: ${tableName}`);
    return true;
  } catch (error) {
    console.error(`Error clearing table ${tableName}:`, error.message);
    return false;
  }
}

/**
 * Drop a table from the database
 */
async function dropTable(tableName) {
  try {
    console.log(`Dropping table: ${tableName}`);

    // Use RPC to execute SQL (requires appropriate permissions)
    const { error } = await supabase.rpc('drop_table', { table_name: tableName });

    if (error) throw error;

    console.log(`✓ Successfully dropped table: ${tableName}`);
    return true;
  } catch (error) {
    console.error(`Error dropping table ${tableName}:`, error.message);
    return false;
  }
}

/**
 * Reset the entire database
 */
async function resetDatabase(shouldDropTables = false) {
  console.log('Starting database reset...');

  // Get all tables
  const tables = await getAllTables();

  if (tables.length === 0) {
    console.log('No tables found in the database.');
    return;
  }

  console.log(`Found ${tables.length} tables: ${tables.join(', ')}`);

  // In non-interactive mode, we'll proceed without confirmation
  console.log(`WARNING: About to delete all records from ${tables.length} tables. This cannot be undone.`);
  console.log('Tables to be processed:', tables.join(', '));

  // Proceed with deletion
  if (shouldDropTables) {
    console.log('WARNING: Will also DROP all tables after clearing them.');
  }

  // Give a brief pause before proceeding
  console.log('Starting in 3 seconds...');
  await new Promise(resolve => setTimeout(resolve, 3000));

  // Process the reset
  await processReset(tables, shouldDropTables);
}

/**
 * Process the actual reset
 */
async function processReset(tables, shouldDropTables) {
  // First, clear all tables
  for (const table of tables) {
    await clearTable(table);
  }

  // If requested, drop the tables
  if (shouldDropTables) {
    for (const table of tables) {
      await dropTable(table);
    }
  }

  console.log('Database reset complete!');
}

// Run the script
resetDatabase(true);
