from typing import List, Dict, Any, Optional
import asyncio
import os
from urllib.parse import urlparse

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger

# Import service-specific modules
from .strategies import FormDiscoveryStrategy
from .browser_pool import BrowserPool

# Initialize logger
logger = get_service_logger("form-discovery", "engine")


class FormDiscoveryEngine:
    """
    Engine for discovering forms on websites using multiple strategies.
    """
    
    def __init__(self, strategies: List[FormDiscoveryStrategy], browser_pool_size: int = 5):
        """
        Initialize the form discovery engine.
        
        Args:
            strategies: List of form discovery strategies to use
            browser_pool_size: Size of the browser pool
        """
        self.strategies = strategies
        self.browser_pool = BrowserPool(size=browser_pool_size)
        self.logger = logger
    
    async def discover_forms(
        self, 
        url: str, 
        max_pages: int = 10, 
        max_depth: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Discover forms on a website using multiple strategies.
        
        Args:
            url: The URL of the website to analyze
            max_pages: Maximum number of pages to analyze
            max_depth: Maximum depth of pages to analyze
            
        Returns:
            A list of discovered forms with their metadata
        """
        self.logger.info(f"Starting form discovery for {url}", props={
            "max_pages": max_pages,
            "max_depth": max_depth
        })
        
        # Initialize the browser pool
        await self.browser_pool.initialize()
        
        try:
            all_forms = []
            
            # Execute each strategy in sequence
            for strategy in self.strategies:
                self.logger.info(f"Executing strategy: {strategy.name}")
                
                # Execute the strategy
                forms = await strategy.execute(url, self.browser_pool)
                
                # Add discovered forms to the result
                if forms:
                    all_forms.extend(forms)
                    self.logger.info(f"Strategy {strategy.name} found {len(forms)} forms")
                    
                    # If the strategy has high confidence, we can stop
                    if strategy.is_high_confidence():
                        self.logger.info(f"Strategy {strategy.name} has high confidence, stopping")
                        break
            
            # Deduplicate forms based on URL and submission endpoint
            deduplicated_forms = self._deduplicate_forms(all_forms)
            
            self.logger.info(f"Form discovery completed for {url}", props={
                "total_forms": len(all_forms),
                "unique_forms": len(deduplicated_forms)
            })
            
            return deduplicated_forms
            
        except Exception as e:
            self.logger.error(f"Error during form discovery: {str(e)}")
            return []
            
        finally:
            # Close the browser pool
            await self.browser_pool.close()
    
    def _deduplicate_forms(self, forms: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Deduplicate forms based on URL and submission endpoint.
        
        Args:
            forms: List of forms to deduplicate
            
        Returns:
            Deduplicated list of forms
        """
        unique_forms = {}
        
        for form in forms:
            # Create a key based on URL and submission endpoint
            url = form.get("url", "")
            endpoint = form.get("submission_endpoint", "")
            key = f"{url}|{endpoint}"
            
            # If we haven't seen this form before, or the new form has more fields, keep it
            if key not in unique_forms or len(form.get("fields", [])) > len(unique_forms[key].get("fields", [])):
                unique_forms[key] = form
        
        return list(unique_forms.values())
