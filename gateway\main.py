import os
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional

from fastapi import Fast<PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.security import OAuth2<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from fastapi.middleware.cors import CORSMiddleware
from jose import JWTError, jwt
from passlib.context import CryptContext
from pydantic import BaseModel

import httpx

# Initialize FastAPI app
app = FastAPI(
    title="Lead Generation System API Gateway",
    description="API Gateway for the Lead Generation System",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Authentication configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key")  # In production, use a secure key
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/token")

# Service URLs
FORM_DISCOVERY_URL = os.getenv("FORM_DISCOVERY_URL", "http://form-discovery:8001")
FORM_ANALYSIS_URL = os.getenv("FORM_ANALYSIS_URL", "http://form-analysis:8002")
LEAD_MANAGEMENT_URL = os.getenv("LEAD_MANAGEMENT_URL", "http://lead-management:8003")
RESPONSE_INGESTION_URL = os.getenv("RESPONSE_INGESTION_URL", "http://response-ingestion:8004")
CONVERSATION_MANAGEMENT_URL = os.getenv("CONVERSATION_MANAGEMENT_URL", "http://conversation-management:8005")
AI_ORCHESTRATOR_URL = os.getenv("AI_ORCHESTRATOR_URL", "http://ai-orchestrator:8006")
ANALYTICS_URL = os.getenv("ANALYTICS_URL", "http://analytics:8007")


# Models
class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    username: Optional[str] = None
    scopes: List[str] = []


class User(BaseModel):
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: Optional[bool] = None
    role: str = "user"


class UserInDB(User):
    hashed_password: str


# Mock users database - in production, use a real database
users_db = {
    "admin": {
        "username": "admin",
        "full_name": "Admin User",
        "email": "<EMAIL>",
        "hashed_password": pwd_context.hash("password"),
        "disabled": False,
        "role": "admin"
    },
    "user": {
        "username": "user",
        "full_name": "Regular User",
        "email": "<EMAIL>",
        "hashed_password": pwd_context.hash("password"),
        "disabled": False,
        "role": "user"
    }
}


# Authentication functions
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_user(db, username: str):
    if username in db:
        user_dict = db[username]
        return UserInDB(**user_dict)


def authenticate_user(db, username: str, password: str):
    user = get_user(db, username)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    user = get_user(users_db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user


async def get_current_active_user(current_user: User = Depends(get_current_user)):
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


# Authentication endpoints
@app.post("/api/v1/auth/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(users_db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}


@app.get("/api/v1/auth/profile", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    return current_user


# Proxy function
async def proxy_request(request: Request, service_url: str, path: str):
    # Get request method
    method = request.method
    
    # Get request headers
    headers = dict(request.headers)
    headers.pop("host", None)  # Remove host header
    
    # Get request body
    body = await request.body()
    
    # Get query parameters
    params = dict(request.query_params)
    
    # Create URL for the target service
    target_url = f"{service_url}{path}"
    
    # Create HTTP client
    async with httpx.AsyncClient() as client:
        # Forward the request to the target service
        response = await client.request(
            method=method,
            url=target_url,
            headers=headers,
            params=params,
            content=body
        )
        
        # Return the response from the target service
        return response.json()


# Form Discovery Service routes
@app.post("/api/v1/discover")
async def discover_forms(request: Request, current_user: User = Depends(get_current_active_user)):
    return await proxy_request(request, FORM_DISCOVERY_URL, "/api/v1/discover")


@app.get("/api/v1/discover/{job_id}")
async def get_discovery_status(job_id: str, request: Request, current_user: User = Depends(get_current_active_user)):
    return await proxy_request(request, FORM_DISCOVERY_URL, f"/api/v1/discover/{job_id}")


# Form Analysis Service routes
@app.post("/api/v1/analyze")
async def analyze_form(request: Request, current_user: User = Depends(get_current_active_user)):
    return await proxy_request(request, FORM_ANALYSIS_URL, "/api/v1/analyze")


@app.get("/api/v1/analyze/{form_id}")
async def get_analysis_result(form_id: str, request: Request, current_user: User = Depends(get_current_active_user)):
    return await proxy_request(request, FORM_ANALYSIS_URL, f"/api/v1/analyze/{form_id}")


# Lead Management Service routes
@app.get("/api/v1/leads")
async def get_leads(request: Request, current_user: User = Depends(get_current_active_user)):
    return await proxy_request(request, LEAD_MANAGEMENT_URL, "/api/v1/leads")


@app.get("/api/v1/leads/{lead_id}")
async def get_lead(lead_id: str, request: Request, current_user: User = Depends(get_current_active_user)):
    return await proxy_request(request, LEAD_MANAGEMENT_URL, f"/api/v1/leads/{lead_id}")


# Response Ingestion Service routes
@app.get("/api/v1/responses")
async def get_responses(request: Request, current_user: User = Depends(get_current_active_user)):
    return await proxy_request(request, RESPONSE_INGESTION_URL, "/api/v1/responses")


# Conversation Management Service routes
@app.get("/api/v1/conversations")
async def get_conversations(request: Request, current_user: User = Depends(get_current_active_user)):
    return await proxy_request(request, CONVERSATION_MANAGEMENT_URL, "/api/v1/conversations")


@app.get("/api/v1/conversations/{conversation_id}")
async def get_conversation(conversation_id: str, request: Request, current_user: User = Depends(get_current_active_user)):
    return await proxy_request(request, CONVERSATION_MANAGEMENT_URL, f"/api/v1/conversations/{conversation_id}")


# Analytics Service routes
@app.get("/api/v1/analytics/metrics")
async def get_metrics(request: Request, current_user: User = Depends(get_current_active_user)):
    return await proxy_request(request, ANALYTICS_URL, "/api/v1/metrics")


# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
