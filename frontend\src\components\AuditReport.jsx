import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Typography, Grid, Box, CircularProgress, Divider, <PERSON>, Button, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  AccessTime as AccessTimeIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Grade as GradeIcon,
  Warning as WarningIcon,
  TrendingDown as TrendingDownIcon,
  MonetizationOn as MonetizationOnIcon
} from '@mui/icons-material';

// Mock data for the audit report - single form submission
const mockAuditData = {
  summary: {
    company_name: "Acme Roofing Solutions",
    website: "acmeroofing.com",
    form_url: "https://acmeroofing.com/contact",
    submission_time: "2023-05-03T14:32:15Z",
    audit_completed: "2023-05-05T14:32:15Z",
    responded: true,
    response_time_seconds: 2180,
    response_time_human: "0:36:20",
    response_channel: "sms",
    time_score: 60,
    channel_bonus: 15,
    total_score: 75,
    grade: "C",
    industry_avg_score: 45,
    industry_avg_grade: "D",
    industry_avg_response_time: "1:42:15",
    industry_best_practice: "Under 5 minutes via phone"
  },
  response_channels: {
    "phone": {
      used: false,
      time_score_potential: 100,
      channel_bonus: 25,
      potential_score: 125,
      potential_grade: "A+",
      industry_usage: "42% of top performers"
    },
    "sms": {
      used: true,
      time_score: 60,
      channel_bonus: 15,
      total_score: 75,
      grade: "C",
      industry_usage: "28% of businesses"
    },
    "email": {
      used: false,
      time_score_potential: 60,
      channel_bonus: 5,
      potential_score: 65,
      potential_grade: "C",
      industry_usage: "65% of businesses"
    },
    "NO_RESPONSE": {
      used: false,
      industry_percentage: "22% of businesses"
    }
  },
  industry_benchmarks: {
    "A+": {
      percentage: 8,
      description: "Elite performers - respond in under 5 minutes via phone"
    },
    "A": {
      percentage: 12,
      description: "Strong performers - respond in under 10 minutes via phone or SMS"
    },
    "B": {
      percentage: 18,
      description: "Above average - respond in under 30 minutes via any channel"
    },
    "C": {
      percentage: 25,
      description: "Average - respond within 1 hour via any channel"
    },
    "D": {
      percentage: 15,
      description: "Below average - respond within 3 hours via any channel"
    },
    "F": {
      percentage: 22,
      description: "Poor performers - respond after 3+ hours or not at all"
    }
  },
  response_details: {
    submission: {
      timestamp: "2023-05-03T14:32:15Z",
      form_url: "https://acmeroofing.com/contact",
      form_type: "Contact Form",
      fields_submitted: {
        name: "John Smith",
        email: "<EMAIL>",
        phone: "(*************",
        message: "I need a quote for a new roof installation."
      }
    },
    responses: [
      {
        channel: "sms",
        timestamp: "2023-05-03T15:08:35Z",
        delay_seconds: 2180,
        delay_human: "0:36:20",
        content: "Hi John, thanks for your inquiry about a new roof. This is Mike from Acme Roofing. When would be a good time to discuss your project?",
        sender_info: {
          name: "Mike Johnson",
          role: "Sales Representative",
          phone: "(*************"
        }
      }
    ]
  }
};

// Helper function to get color based on grade
const getGradeColor = (grade) => {
  switch (grade) {
    case 'A+': return '#4caf50'; // Green
    case 'A': return '#8bc34a'; // Light Green
    case 'B': return '#ffeb3b'; // Yellow
    case 'C': return '#ff9800'; // Orange
    case 'D': return '#f44336'; // Red
    case 'F': return '#9e9e9e'; // Grey
    default: return '#9e9e9e'; // Grey
  }
};

// Helper function to get icon for channel
const getChannelIcon = (channel) => {
  switch (channel) {
    case 'phone': return <PhoneIcon />;
    case 'sms': return <SmsIcon />;
    case 'email': return <EmailIcon />;
    case 'NO_RESPONSE': return <CancelIcon />;
    default: return null;
  }
};

const AuditReport = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [auditData, setAuditData] = useState(mockAuditData);

  // Fetch audit data from API
  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we're using mock data
    setLoading(true);
    setTimeout(() => {
      setAuditData(mockAuditData);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  const { summary } = auditData;

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
          LEAD RESPONSE AUDIT REPORT
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          CONFIDENTIAL: For {mockAuditData.summary.company_name} Management
        </Typography>
        <Divider sx={{ my: 2 }} />
        <Typography variant="body1" sx={{ maxWidth: 800, mx: 'auto', mb: 2 }}>
          This audit analyzes your team's lead response performance based on a real form submission to your website.
          Our scoring system evaluates both response time and communication channels used.
        </Typography>
      </Box>

      {/* Executive Summary Card */}
      <Card sx={{ mb: 4, boxShadow: 3, borderTop: `5px solid ${getGradeColor(mockAuditData.summary.grade)}` }}>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
            EXECUTIVE SUMMARY
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Typography variant="h3" sx={{ fontWeight: 'bold', color: getGradeColor(mockAuditData.summary.grade), mr: 2 }}>
              {mockAuditData.summary.grade}
            </Typography>
            <Typography variant="h6" sx={{ fontWeight: 'medium' }}>
              {mockAuditData.summary.grade === 'A+' && "Elite Operator — You dominate your market"}
              {mockAuditData.summary.grade === 'A' && "Strong but vulnerable — Your competitors are close behind"}
              {mockAuditData.summary.grade === 'B' && "Above average — But still leaving money on the table"}
              {mockAuditData.summary.grade === 'C' && "Average — You're losing to faster competitors"}
              {mockAuditData.summary.grade === 'D' && "Below average — You're wasting ad spend"}
              {mockAuditData.summary.grade === 'F' && "Critical Failure — Your sales process needs immediate attention"}
            </Typography>
          </Box>

          <Paper elevation={0} sx={{ p: 2, bgcolor: '#f5f5f5', mb: 3, borderRadius: 2 }}>
            <Typography variant="body1" paragraph sx={{ fontWeight: 'medium', fontSize: '1.1rem' }}>
              {mockAuditData.summary.grade === 'A+' && "Your team is responding at an elite level. Stay sharp and maintain this advantage."}
              {mockAuditData.summary.grade === 'A' && "Your response system is strong, but vulnerable. One slip and competitors will eat your lunch."}
              {mockAuditData.summary.grade === 'B' && "Your above-average response performance still leaves money on the table. Faster competitors are winning deals you should be closing."}
              {mockAuditData.summary.grade === 'C' && "Your average response times are costing you deals. You consistently lose to the first responder."}
              {mockAuditData.summary.grade === 'D' && "Your slow response speed is costing you 6 out of every 10 deals. This is thousands in lost revenue monthly."}
              {mockAuditData.summary.grade === 'F' && "This is the equivalent of paying for advertising and then ignoring the leads. You're burning money."}
            </Typography>
          </Paper>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', p: 2, border: '1px solid #eee', borderRadius: 2 }}>
                <Typography variant="h6">Response Status</Typography>
                <Typography variant="h3" color={mockAuditData.summary.responded ? "success.main" : "error.main"} sx={{ fontWeight: 'bold' }}>
                  {mockAuditData.summary.responded ? "Responded" : "No Response"}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {mockAuditData.summary.responded
                    ? `Via ${mockAuditData.summary.response_channel} after ${mockAuditData.summary.response_time_human}`
                    : "No response received within 48 hours"}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', p: 2, border: '1px solid #eee', borderRadius: 2 }}>
                <Typography variant="h6">Your Score</Typography>
                <Typography variant="h3" sx={{ fontWeight: 'bold', color: getGradeColor(mockAuditData.summary.grade) }}>
                  {mockAuditData.summary.total_score}/125
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Industry Average: {mockAuditData.summary.industry_avg_score} ({mockAuditData.summary.industry_avg_grade})
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', p: 2, border: '1px solid #eee', borderRadius: 2 }}>
                <Typography variant="h6">Estimated Revenue Impact</Typography>
                <Typography variant="h3" color="error.main" sx={{ fontWeight: 'bold' }}>
                  {mockAuditData.summary.grade === 'A+' && "$0"}
                  {mockAuditData.summary.grade === 'A' && "-$2,500"}
                  {mockAuditData.summary.grade === 'B' && "-$8,000"}
                  {mockAuditData.summary.grade === 'C' && "-$15,000"}
                  {mockAuditData.summary.grade === 'D' && "-$25,000"}
                  {mockAuditData.summary.grade === 'F' && "-$40,000"}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Estimated monthly revenue loss
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Channel Performance Cards */}
      <Card sx={{ mb: 4, boxShadow: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
            RESPONSE CHANNEL ANALYSIS
          </Typography>

          <Typography variant="body1" paragraph>
            How you respond to leads matters as much as when. Our analysis shows your team's performance with the test lead we submitted:
          </Typography>

          <Grid container spacing={3} sx={{ mb: 2 }}>
            {Object.entries(mockAuditData.response_channels).map(([channel, stats]) => (
              <Grid item xs={12} md={4} key={channel}>
                <Card sx={{
                  height: '100%',
                  boxShadow: 1,
                  border: stats.used ? '2px solid #4caf50' : '1px solid #eee',
                  bgcolor: stats.used ? '#f1f8e9' : 'white'
                }}>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={2}>
                      {getChannelIcon(channel)}
                      <Typography variant="h6" sx={{ ml: 1, textTransform: 'capitalize', fontWeight: 'medium' }}>
                        {channel === 'NO_RESPONSE' ? 'No Response' : channel}
                      </Typography>
                      <Box sx={{ ml: 'auto' }}>
                        {channel !== 'NO_RESPONSE' && (
                          <Chip
                            label={`+${channel === 'phone' ? '25' : channel === 'sms' ? '15' : '5'} pts`}
                            size="small"
                            color={channel === 'phone' ? 'success' : channel === 'sms' ? 'primary' : 'default'}
                          />
                        )}
                      </Box>
                    </Box>
                    <Divider sx={{ mb: 2 }} />

                    {channel === 'NO_RESPONSE' ? (
                      <Box>
                        <Typography variant="body2" color="text.secondary">Industry Data</Typography>
                        <Typography variant="body1" sx={{ mt: 1 }}>
                          {stats.industry_percentage} of businesses fail to respond to new leads within 48 hours.
                        </Typography>
                        <Typography variant="body2" color="error.main" sx={{ mt: 2 }}>
                          Each non-response costs an average of $300 in wasted marketing spend.
                        </Typography>
                      </Box>
                    ) : (
                      <>
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="body2" color="text.secondary">Status</Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            {stats.used ? (
                              <>
                                <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 1 }} />
                                <Typography variant="body1" sx={{ fontWeight: 'medium' }}>Used</Typography>
                              </>
                            ) : (
                              <>
                                <CancelIcon color="action" fontSize="small" sx={{ mr: 1 }} />
                                <Typography variant="body1" color="text.secondary">Not Used</Typography>
                              </>
                            )}
                          </Box>
                        </Box>

                        <Box sx={{ mb: 2 }}>
                          <Typography variant="body2" color="text.secondary">
                            {stats.used ? 'Your Response Time' : 'Industry Best Practice'}
                          </Typography>
                          <Typography variant="body1" sx={{
                            fontWeight: 'medium',
                            color: stats.used ? (
                              channel === 'phone' && stats.time_score > 80 ? 'success.main' :
                              channel === 'sms' && stats.time_score > 60 ? 'success.main' :
                              channel === 'email' && stats.time_score > 40 ? 'success.main' : 'error.main'
                            ) : 'text.secondary'
                          }}>
                            {stats.used ? mockAuditData.summary.response_time_human : mockAuditData.summary.industry_best_practice}
                          </Typography>
                        </Box>

                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {stats.used ? 'Your Score' : 'Potential Score'}
                          </Typography>
                          <Typography variant="body1" sx={{
                            fontWeight: 'medium',
                            color: getGradeColor(stats.used ? stats.grade : stats.potential_grade)
                          }}>
                            {stats.used ? stats.total_score : stats.potential_score}/125
                            {!stats.used && ` (Grade: ${stats.potential_grade})`}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {stats.industry_usage}
                          </Typography>
                        </Box>
                      </>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Response Time Impact */}
          <Paper elevation={0} sx={{ p: 2, bgcolor: '#e3f2fd', borderRadius: 2, mt: 3 }}>
            <Box display="flex" alignItems="flex-start">
              <AccessTimeIcon color="primary" sx={{ mr: 2, mt: 0.5 }} />
              <Box>
                <Typography variant="h6" color="primary.main">Response Time Impact</Typography>
                <Typography variant="body2">
                  <strong>5-minute response time:</strong> 85% conversion rate<br />
                  <strong>10-minute response time:</strong> 70% conversion rate<br />
                  <strong>30-minute response time:</strong> 52% conversion rate<br />
                  <strong>1-hour response time:</strong> 40% conversion rate<br />
                  <strong>Your response time ({mockAuditData.summary.response_time_human}):</strong> Estimated {
                    mockAuditData.summary.response_time_seconds < 300 ? "85%" :
                    mockAuditData.summary.response_time_seconds < 600 ? "70%" :
                    mockAuditData.summary.response_time_seconds < 1800 ? "52%" :
                    mockAuditData.summary.response_time_seconds < 3600 ? "40%" :
                    mockAuditData.summary.response_time_seconds < 10800 ? "28%" :
                    mockAuditData.summary.response_time_seconds < 86400 ? "15%" : "5%"
                  } conversion rate
                </Typography>
              </Box>
            </Box>
          </Paper>
        </CardContent>
      </Card>

      {/* Industry Benchmarks */}
      <Card sx={{ mb: 4, boxShadow: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
            INDUSTRY BENCHMARKS
          </Typography>

          <Typography variant="body1" paragraph>
            Our scoring system grades lead responses from A+ (elite) to F (critical failure). Here's how your grade compares to industry benchmarks:
          </Typography>

          <Grid container spacing={2} sx={{ mb: 3 }}>
            {['A+', 'A', 'B', 'C', 'D', 'F'].map((grade) => {
              const gradeStats = mockAuditData.industry_benchmarks[grade] || { percentage: 0, description: "" };
              const isYourGrade = grade === mockAuditData.summary.grade;
              return (
                <Grid item xs={6} sm={4} md={2} key={grade}>
                  <Box
                    sx={{
                      p: 2,
                      textAlign: 'center',
                      borderRadius: 2,
                      bgcolor: isYourGrade ? `${getGradeColor(grade)}22` : 'white', // Light background for your grade
                      border: isYourGrade ? `2px solid ${getGradeColor(grade)}` : `1px solid ${getGradeColor(grade)}`,
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      position: 'relative'
                    }}
                  >
                    {isYourGrade && (
                      <Box sx={{
                        position: 'absolute',
                        top: -10,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        bgcolor: 'white',
                        px: 1,
                        borderRadius: 1
                      }}>
                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>YOUR GRADE</Typography>
                      </Box>
                    )}
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: getGradeColor(grade), mb: 1 }}>{grade}</Typography>
                    <Typography variant="body2" sx={{ fontWeight: isYourGrade ? 'medium' : 'normal' }}>{gradeStats.percentage}% of businesses</Typography>
                  </Box>
                </Grid>
              );
            })}
          </Grid>

          <Box sx={{ mt: 4, mb: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'medium' }}>
              Grade Descriptions
            </Typography>

            <Grid container spacing={2}>
              {Object.entries(mockAuditData.industry_benchmarks).map(([grade, stats]) => (
                <Grid item xs={12} sm={6} md={4} key={grade}>
                  <Box sx={{
                    p: 2,
                    borderLeft: `4px solid ${getGradeColor(grade)}`,
                    bgcolor: grade === mockAuditData.summary.grade ? `${getGradeColor(grade)}10` : 'transparent',
                    mb: 1
                  }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 'medium', color: getGradeColor(grade) }}>
                      Grade {grade} ({stats.percentage}%)
                    </Typography>
                    <Typography variant="body2">
                      {stats.description}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Box>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom sx={{ fontWeight: 'medium' }}>
            What Your Grade Means For Your Business
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                <TrendingDownIcon color="error" sx={{ mr: 1, mt: 0.5 }} />
                <Box>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>Conversion Impact</Typography>
                  <Typography variant="body2">
                    {mockAuditData.summary.grade === 'A+' && "You're converting at near-maximum efficiency."}
                    {mockAuditData.summary.grade === 'A' && "You're losing ~15% of potential conversions due to response issues."}
                    {mockAuditData.summary.grade === 'B' && "You're losing ~30% of potential conversions due to response issues."}
                    {mockAuditData.summary.grade === 'C' && "You're losing ~50% of potential conversions due to response issues."}
                    {mockAuditData.summary.grade === 'D' && "You're losing ~70% of potential conversions due to response issues."}
                    {mockAuditData.summary.grade === 'F' && "You're losing ~90% of potential conversions due to response issues."}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                <MonetizationOnIcon color="primary" sx={{ mr: 1, mt: 0.5 }} />
                <Box>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>ROI on Marketing</Typography>
                  <Typography variant="body2">
                    {mockAuditData.summary.grade === 'A+' && "Your marketing spend is generating maximum ROI."}
                    {mockAuditData.summary.grade === 'A' && "Your marketing ROI is reduced by ~10% due to response issues."}
                    {mockAuditData.summary.grade === 'B' && "Your marketing ROI is reduced by ~25% due to response issues."}
                    {mockAuditData.summary.grade === 'C' && "Your marketing ROI is reduced by ~40% due to response issues."}
                    {mockAuditData.summary.grade === 'D' && "Your marketing ROI is reduced by ~60% due to response issues."}
                    {mockAuditData.summary.grade === 'F' && "Your marketing ROI is reduced by ~80% due to response issues."}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                <WarningIcon color="warning" sx={{ mr: 1, mt: 0.5 }} />
                <Box>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>Competitive Position</Typography>
                  <Typography variant="body2">
                    {mockAuditData.summary.grade === 'A+' && "You're outperforming 99% of competitors in lead response."}
                    {mockAuditData.summary.grade === 'A' && "You're outperforming 85% of competitors in lead response."}
                    {mockAuditData.summary.grade === 'B' && "You're outperforming 60% of competitors in lead response."}
                    {mockAuditData.summary.grade === 'C' && "You're underperforming against 60% of competitors."}
                    {mockAuditData.summary.grade === 'D' && "You're underperforming against 85% of competitors."}
                    {mockAuditData.summary.grade === 'F' && "You're underperforming against 99% of competitors."}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Form Submission Details */}
      <Card sx={{ mb: 4, boxShadow: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
            FORM SUBMISSION DETAILS
          </Typography>

          <Typography variant="body1" paragraph>
            Below are the details of the test form submission we made to your website and the response we received.
          </Typography>

          <Grid container spacing={3}>
            {/* Form Submission */}
            <Grid item xs={12} md={6}>
              <Card sx={{ boxShadow: 1, height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 'medium', color: 'primary.main' }}>
                    Our Test Submission
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Form URL</Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                      {mockAuditData.response_details.submission.form_url}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Submission Time</Typography>
                    <Typography variant="body1">
                      {new Date(mockAuditData.response_details.submission.timestamp).toLocaleString()}
                    </Typography>
                  </Box>

                  <Box>
                    <Typography variant="body2" color="text.secondary">Information Submitted</Typography>
                    <Box sx={{ mt: 1, p: 1.5, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                      {Object.entries(mockAuditData.response_details.submission.fields_submitted).map(([field, value]) => (
                        <Box key={field} sx={{ mb: 1 }}>
                          <Typography variant="caption" sx={{ fontWeight: 'medium', textTransform: 'capitalize' }}>
                            {field}:
                          </Typography>
                          <Typography variant="body2">{value}</Typography>
                        </Box>
                      ))}
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Response Details */}
            <Grid item xs={12} md={6}>
              <Card sx={{
                boxShadow: 1,
                height: '100%',
                borderLeft: mockAuditData.summary.responded ? `5px solid ${getGradeColor(mockAuditData.summary.grade)}` : '5px solid #9e9e9e',
              }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 'medium', color: mockAuditData.summary.responded ? 'success.main' : 'error.main' }}>
                    {mockAuditData.summary.responded ? 'Your Response' : 'No Response Received'}
                  </Typography>

                  {mockAuditData.summary.responded ? (
                    <>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Response Channel</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                          {getChannelIcon(mockAuditData.summary.response_channel)}
                          <Typography variant="body1" sx={{ ml: 1, textTransform: 'capitalize', fontWeight: 'medium' }}>
                            {mockAuditData.summary.response_channel}
                          </Typography>
                        </Box>
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Response Time</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                          <AccessTimeIcon fontSize="small" color={
                            mockAuditData.summary.time_score > 80 ? "success" :
                            mockAuditData.summary.time_score > 40 ? "warning" : "error"
                          } />
                          <Typography variant="body1" sx={{ ml: 1 }}>
                            {mockAuditData.summary.response_time_human} after submission
                          </Typography>
                        </Box>
                      </Box>

                      <Box>
                        <Typography variant="body2" color="text.secondary">Response Content</Typography>
                        <Box sx={{ mt: 1, p: 1.5, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                          <Typography variant="body2" sx={{ fontStyle: 'italic', mb: 1 }}>
                            "{mockAuditData.response_details.responses[0].content}"
                          </Typography>
                          <Typography variant="caption">
                            - {mockAuditData.response_details.responses[0].sender_info.name}, {mockAuditData.response_details.responses[0].sender_info.role}
                          </Typography>
                        </Box>
                      </Box>

                      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box>
                          <Typography variant="body2" color="text.secondary">Score Breakdown</Typography>
                          <Typography variant="body1">
                            Time: {mockAuditData.summary.time_score} + Channel: +{mockAuditData.summary.channel_bonus} = {mockAuditData.summary.total_score}
                          </Typography>
                        </Box>
                        <Chip
                          label={`Grade: ${mockAuditData.summary.grade}`}
                          sx={{
                            bgcolor: getGradeColor(mockAuditData.summary.grade),
                            color: 'white',
                            fontWeight: 'bold'
                          }}
                        />
                      </Box>
                    </>
                  ) : (
                    <Box sx={{ p: 3, textAlign: 'center' }}>
                      <CancelIcon color="error" sx={{ fontSize: 60, mb: 2, opacity: 0.7 }} />
                      <Typography variant="body1" paragraph>
                        We did not receive any response to our form submission within the 48-hour monitoring period.
                      </Typography>
                      <Typography variant="body2" color="error.main">
                        This results in a grade of F (0/125 points) and represents a 100% loss of this potential lead.
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Call to Action */}
      <Card sx={{ mb: 4, bgcolor: '#e8f5e9', boxShadow: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
            HOW TO IMPROVE YOUR LEAD RESPONSE
          </Typography>

          <Typography variant="body1" paragraph>
            Based on our test submission to your website, we've identified specific opportunities to improve your lead response system:
          </Typography>

          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'white', borderRadius: 2, height: '100%' }}>
                <Typography variant="h6" gutterBottom sx={{ color: '#2e7d32' }}>1. Response Time Optimization</Typography>
                <Typography variant="body2">
                  {mockAuditData.summary.responded ? (
                    <>
                      Your current response time of {mockAuditData.summary.response_time_human} can be improved to under 5 minutes.
                      This alone would increase your conversion rate from approximately {
                        mockAuditData.summary.response_time_seconds < 300 ? "85%" :
                        mockAuditData.summary.response_time_seconds < 600 ? "70%" :
                        mockAuditData.summary.response_time_seconds < 1800 ? "52%" :
                        mockAuditData.summary.response_time_seconds < 3600 ? "40%" :
                        mockAuditData.summary.response_time_seconds < 10800 ? "28%" :
                        mockAuditData.summary.response_time_seconds < 86400 ? "15%" : "5%"
                      } to 85%.
                    </>
                  ) : (
                    <>
                      Implement a system that ensures all leads receive a response within 5 minutes.
                      This would increase your conversion rate from 0% to approximately 85%.
                    </>
                  )}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'white', borderRadius: 2, height: '100%' }}>
                <Typography variant="h6" gutterBottom sx={{ color: '#2e7d32' }}>2. Channel Prioritization</Typography>
                <Typography variant="body2">
                  {mockAuditData.summary.response_channel === 'phone' ? (
                    <>
                      You're already using the optimal channel (phone). Continue this practice
                      while ensuring faster response times for maximum effectiveness.
                    </>
                  ) : mockAuditData.summary.response_channel === 'sms' ? (
                    <>
                      While SMS is good, our data shows phone calls convert 3x better.
                      Consider using phone as your primary response channel.
                    </>
                  ) : mockAuditData.summary.response_channel === 'email' ? (
                    <>
                      Email is your least effective option. Our data shows phone calls convert 3x better
                      than email responses, and SMS converts 2x better.
                    </>
                  ) : (
                    <>
                      Prioritize phone calls for high-value leads, followed by SMS.
                      Our data shows phone calls convert 3x better than email responses.
                    </>
                  )}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'white', borderRadius: 2, height: '100%' }}>
                <Typography variant="h6" gutterBottom sx={{ color: '#2e7d32' }}>3. Automated Lead Routing</Typography>
                <Typography variant="body2">
                  Implement intelligent lead routing to ensure leads are sent to available team members
                  who can respond immediately. This eliminates the {mockAuditData.summary.responded ?
                    `${mockAuditData.summary.response_time_human} delay` :
                    "non-response issue"} we observed in our test.
                </Typography>
              </Box>
            </Grid>
          </Grid>

          <Box sx={{ p: 3, bgcolor: 'white', borderRadius: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ color: '#2e7d32' }}>
              Our Solution: LeadResponsePro
            </Typography>
            <Typography variant="body1" paragraph>
              We've developed a comprehensive lead response system that addresses all these issues:
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <CheckCircleIcon color="success" sx={{ mr: 1, mt: 0.5 }} />
                  <Typography variant="body2">
                    <strong>5-Minute Response Guarantee</strong> - Our system ensures every lead receives a response within 5 minutes, 24/7.
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <CheckCircleIcon color="success" sx={{ mr: 1, mt: 0.5 }} />
                  <Typography variant="body2">
                    <strong>Multi-Channel Approach</strong> - Automated phone, SMS, and email responses based on lead value and preferences.
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <CheckCircleIcon color="success" sx={{ mr: 1, mt: 0.5 }} />
                  <Typography variant="body2">
                    <strong>Smart Lead Routing</strong> - AI-powered distribution to the right team member at the right time.
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>

          <Box display="flex" justifyContent="center" mt={3}>
            <Button
              variant="contained"
              color="success"
              size="large"
              sx={{ px: 4, py: 1.5, fontWeight: 'bold' }}
            >
              Schedule a Consultation
            </Button>
          </Box>
        </CardContent>
      </Card>

      <Box mt={4} display="flex" justifyContent="center" flexDirection="column" alignItems="center">
        <Button variant="outlined" color="primary" sx={{ mb: 2 }}>
          Download Full Report
        </Button>
        <Typography variant="caption" color="text.secondary" align="center">
          This report is confidential and intended only for {mockAuditData.summary.company_name} management.
          <br />
          © {new Date().getFullYear()} Lead Response Audit System
        </Typography>
      </Box>
    </Box>
  );
};

export default AuditReport;
