import React, { useState, useEffect } from 'react';
import {
  Card, Card<PERSON>ontent, Typography, Grid, Box, CircularProgress, Divider,
  Chip, Button, Paper, Avatar, LinearProgress, useMediaQuery, Alert,
  Container, Stack, Fade, Grow
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  AccessTime as AccessTimeIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  TrendingDown as TrendingDownIcon,
  MonetizationOn as MonetizationOnIcon,
  Warning as WarningIcon,
  Download as DownloadIcon,
  Speed as SpeedIcon,
  ArrowForward as ArrowForwardIcon,
  Schedule as ScheduleIcon,
  CallMade as CallMadeIcon,
  TrendingUp as TrendingUpIcon,
  LocalFireDepartment as FireIcon,
  Psychology as PsychologyIcon,
  CompareArrows as CompareArrowsIcon
} from '@mui/icons-material';

// Helper function to generate dynamic feedback based on audit results
const generateDynamicFeedback = (summary) => {
  const responseTime = summary.response_time_seconds;
  const responded = summary.responded;
  const grade = summary.grade;

  let feedback = {
    headline: "",
    punchline: "",
    urgency_level: "MEDIUM",
    qualification_status: responded ? "QUALIFIED" : "DISQUALIFIED"
  };

  if (!responded) {
    feedback.headline = "🔴 No Response = No Business";
    feedback.punchline = "You missed a potential customer completely. They've already moved on to your competitors.";
    feedback.urgency_level = "CRITICAL";
  } else if (responseTime < 300) { // Under 5 minutes
    feedback.headline = "🟢 Fast Response - Good Job!";
    feedback.punchline = "You're responding quickly, but there's still room for optimization.";
    feedback.urgency_level = "LOW";
  } else if (responseTime < 1800) { // Under 30 minutes
    feedback.headline = "🟡 Decent Speed, But Competitors Are Faster";
    feedback.punchline = "You're responding, but faster competitors are likely winning more deals.";
    feedback.urgency_level = "MEDIUM";
  } else { // Over 30 minutes
    feedback.headline = "🔴 Too Slow - Losing Deals Daily";
    feedback.punchline = "By the time you respond, prospects have already contacted 3-4 other companies.";
    feedback.urgency_level = "HIGH";
  }

  return feedback;
};

// Mock data scenarios for testing different audit results
const mockAuditScenarios = {
  // Scenario 1: Poor performance (current default)
  poor: {
    company_name: "Acme Roofing Solutions",
    website: "acmeroofing.com",
    form_url: "https://acmeroofing.com/contact",
    submission_time: "2023-05-03T14:32:15Z",
    audit_completed: "2023-05-05T14:32:15Z",
    responded: true,
    response_time_seconds: 2180, // 36 minutes
    response_time_human: "36 minutes",
    response_channel: "sms",
    time_score: 60,
    channel_bonus: 15,
    multi_channel_bonus: 0,
    channels_used: ["sms"],
    total_score: 75,
    grade: "C",
    industry_avg_score: 45,
    industry_avg_grade: "D",
    industry_avg_response_time: "1:42:15",
    industry_best_practice: "Under 5 minutes via phone followed by multi-channel follow-up"
  },
  // Scenario 2: No response
  noResponse: {
    company_name: "Slow Response Corp",
    website: "slowresponse.com",
    form_url: "https://slowresponse.com/contact",
    submission_time: "2023-05-03T14:32:15Z",
    audit_completed: "2023-05-05T14:32:15Z",
    responded: false,
    response_time_seconds: 0,
    response_time_human: "No response",
    response_channel: null,
    time_score: 0,
    channel_bonus: 0,
    multi_channel_bonus: 0,
    channels_used: [],
    total_score: 0,
    grade: "F",
    industry_avg_score: 45,
    industry_avg_grade: "D",
    industry_avg_response_time: "1:42:15",
    industry_best_practice: "Under 5 minutes via phone followed by multi-channel follow-up"
  },
  // Scenario 3: Good performance
  good: {
    company_name: "Fast Response Inc",
    website: "fastresponse.com",
    form_url: "https://fastresponse.com/contact",
    submission_time: "2023-05-03T14:32:15Z",
    audit_completed: "2023-05-05T14:32:15Z",
    responded: true,
    response_time_seconds: 240, // 4 minutes
    response_time_human: "4 minutes",
    response_channel: "phone",
    time_score: 95,
    channel_bonus: 25,
    multi_channel_bonus: 0,
    channels_used: ["phone"],
    total_score: 120,
    grade: "A",
    industry_avg_score: 45,
    industry_avg_grade: "D",
    industry_avg_response_time: "1:42:15",
    industry_best_practice: "Under 5 minutes via phone followed by multi-channel follow-up"
  }
};

// Use the poor scenario as default for demonstration
const mockAuditData = {
  summary: mockAuditScenarios.poor,
  response_details: {
    submission: {
      timestamp: "2023-05-03T14:32:15Z",
      form_url: "https://acmeroofing.com/contact",
      form_type: "Contact Form",
      fields_submitted: {
        name: "John Smith",
        email: "<EMAIL>",
        phone: "(*************",
        message: "I need a quote for a new roof installation."
      }
    },
    responses: [
      {
        channel: "sms",
        timestamp: "2023-05-03T15:08:35Z",
        delay_seconds: 2180,
        delay_human: "0:36:20",
        content: "Hi John, thanks for your inquiry about a new roof. This is Mike from Acme Roofing. When would be a good time to discuss your project?",
        sender_info: {
          name: "Mike Johnson",
          role: "Sales Representative",
          phone: "(*************"
        }
      },
      // Additional responses would be listed here for multi-channel examples
      // Example of a follow-up email response
      /*
      {
        channel: "email",
        timestamp: "2023-05-03T15:15:22Z",
        delay_seconds: 2587,
        delay_human: "0:43:07",
        content: "Hello John, I just sent you a text message about your roof installation inquiry. I've also attached our brochure with some examples of our recent work. Looking forward to discussing your project!",
        sender_info: {
          name: "Mike Johnson",
          role: "Sales Representative",
          email: "<EMAIL>"
        }
      },
      // Example of a follow-up phone call
      {
        channel: "phone",
        timestamp: "2023-05-03T15:30:45Z",
        delay_seconds: 3510,
        delay_human: "0:58:30",
        content: "Called to follow up on text and email. Discussed project details and scheduled an in-person consultation.",
        sender_info: {
          name: "Mike Johnson",
          role: "Sales Representative",
          phone: "(*************"
        }
      }
      */
    ]
  },
  response_channels: {
    phone: {
      used: false,
      time_score: 0,
      total_score: 0,
      grade: "F",
      potential_score: 125,
      potential_grade: "A+",
      industry_usage: "Used by 45% of top performers"
    },
    sms: {
      used: true,
      time_score: 60,
      total_score: 75,
      grade: "C",
      potential_score: 115,
      potential_grade: "A",
      industry_usage: "Used by 30% of top performers"
    },
    email: {
      used: false,
      time_score: 0,
      total_score: 0,
      grade: "F",
      potential_score: 105,
      potential_grade: "B",
      industry_usage: "Used by 25% of top performers"
    }
  },
  multi_channel: {
    used: false,
    bonus_points: 0,
    potential_bonus: 10,
    description: "Using multiple channels increases response effectiveness by 35%",
    industry_usage: "Used by only 15% of businesses but 80% of top performers",
    best_practice: "Initial response via phone within 5 minutes, followed by email with details, and SMS for appointment confirmation"
  }
};

// Helper function to get color based on grade
const getGradeColor = (grade) => {
  switch (grade) {
    case 'A+': return '#4caf50'; // Green
    case 'A': return '#8bc34a'; // Light Green
    case 'B': return '#ffeb3b'; // Yellow
    case 'C': return '#ff9800'; // Orange
    case 'D': return '#f44336'; // Red
    case 'F': return '#9e9e9e'; // Grey
    default: return '#9e9e9e'; // Grey
  }
};

// Helper function to get icon for channel
const getChannelIcon = (channel) => {
  switch (channel) {
    case 'phone': return <PhoneIcon />;
    case 'sms': return <SmsIcon />;
    case 'email': return <EmailIcon />;
    case 'NO_RESPONSE': return <CancelIcon />;
    default: return null;
  }
};

const AuditReportBalanced = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [loading, setLoading] = useState(false);
  const [auditData, setAuditData] = useState(mockAuditData);

  // Fetch audit data from API
  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    setLoading(true);
    setTimeout(() => {
      setAuditData(mockAuditData);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  const { summary } = auditData;
  const gradeColor = getGradeColor(summary.grade);
  const dynamicFeedback = generateDynamicFeedback(summary);

  // Calculate conversion rate based on response time (realistic estimates)
  const getConversionRate = (seconds) => {
    if (!summary.responded) return 0;
    if (seconds < 300) return 85;    // Under 5 minutes
    if (seconds < 600) return 70;    // 5-10 minutes
    if (seconds < 1800) return 52;   // 10-30 minutes
    if (seconds < 3600) return 35;   // 30-60 minutes
    if (seconds < 7200) return 25;   // 1-2 hours
    return 15;                       // Over 2 hours
  };

  const currentConversionRate = getConversionRate(summary.response_time_seconds);
  const potentialConversionRate = 85;

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Clean Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h3" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2' }}>
          Lead Response Audit Report
        </Typography>
        <Typography variant="h5" color="text.secondary" gutterBottom>
          {summary.company_name}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Audit completed on {new Date(summary.audit_completed).toLocaleDateString()}
        </Typography>
      </Box>

      {/* Dynamic Feedback Card */}
      <Card sx={{
        mb: 4,
        borderRadius: 3,
        boxShadow: 3,
        border: `3px solid ${
          dynamicFeedback.urgency_level === 'CRITICAL' ? '#f44336' :
          dynamicFeedback.urgency_level === 'HIGH' ? '#ff9800' :
          dynamicFeedback.urgency_level === 'MEDIUM' ? '#2196f3' : '#4caf50'
        }`
      }}>
        <CardContent sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h4" sx={{
            fontWeight: 'bold',
            mb: 2,
            color: dynamicFeedback.urgency_level === 'CRITICAL' ? '#f44336' :
                   dynamicFeedback.urgency_level === 'HIGH' ? '#ff9800' :
                   dynamicFeedback.urgency_level === 'MEDIUM' ? '#2196f3' : '#4caf50'
          }}>
            {dynamicFeedback.headline}
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
            {dynamicFeedback.punchline}
          </Typography>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <Card sx={{ mb: 4, borderRadius: 3, boxShadow: 3 }}>
        <CardContent sx={{ p: 4 }}>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', textAlign: 'center', mb: 3 }}>
            Your Performance Summary
          </Typography>

          <Grid container spacing={4}>
            {/* Response Status */}
            <Grid item xs={12} md={4}>
              <Paper sx={{
                p: 3,
                textAlign: 'center',
                bgcolor: summary.responded ? '#e8f5e8' : '#ffebee',
                borderRadius: 2,
                border: `2px solid ${summary.responded ? '#4caf50' : '#f44336'}`
              }}>
                {summary.responded ? (
                  <CheckCircleIcon sx={{ fontSize: 48, color: '#4caf50', mb: 1 }} />
                ) : (
                  <CancelIcon sx={{ fontSize: 48, color: '#f44336', mb: 1 }} />
                )}
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {summary.responded ? 'Responded' : 'No Response'}
                </Typography>
                <Typography variant="body1">
                  {summary.responded
                    ? `${summary.response_time_human} via ${summary.response_channel}`
                    : 'No response within 48 hours'
                  }
                </Typography>
              </Paper>
            </Grid>

            {/* Grade */}
            <Grid item xs={12} md={4}>
              <Paper sx={{
                p: 3,
                textAlign: 'center',
                bgcolor: `${gradeColor}15`,
                borderRadius: 2,
                border: `2px solid ${gradeColor}`
              }}>
                <Typography variant="h2" sx={{ fontWeight: 'bold', color: gradeColor, mb: 1 }}>
                  {summary.grade}
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Response Grade
                </Typography>
                <Typography variant="body1">
                  {summary.total_score} out of 125 points
                </Typography>
              </Paper>
            </Grid>

            {/* Conversion Impact */}
            <Grid item xs={12} md={4}>
              <Paper sx={{
                p: 3,
                textAlign: 'center',
                bgcolor: currentConversionRate >= 70 ? '#e8f5e8' : '#fff3e0',
                borderRadius: 2,
                border: `2px solid ${currentConversionRate >= 70 ? '#4caf50' : '#ff9800'}`
              }}>
                <Typography variant="h2" sx={{
                  fontWeight: 'bold',
                  color: currentConversionRate >= 70 ? '#4caf50' : '#ff9800',
                  mb: 1
                }}>
                  {currentConversionRate}%
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Conversion Rate
                </Typography>
                <Typography variant="body1">
                  Industry best: 85%
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Improvement Opportunity */}
      {currentConversionRate < potentialConversionRate && (
        <Card sx={{ mb: 4, borderRadius: 3, boxShadow: 3 }}>
          <CardContent sx={{ p: 4 }}>
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', textAlign: 'center', mb: 3 }}>
              Improvement Opportunity
            </Typography>

            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={6}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                    Current Performance
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h3" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                      {currentConversionRate}%
                    </Typography>
                  </Box>
                  <Typography variant="body1" color="text.secondary">
                    Response time: {summary.response_time_human}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={currentConversionRate}
                    sx={{ mt: 2, height: 8, borderRadius: 4 }}
                    color="warning"
                  />
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                    Industry Best Practice
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h3" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                      {potentialConversionRate}%
                    </Typography>
                  </Box>
                  <Typography variant="body1" color="text.secondary">
                    Response time: Under 5 minutes
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={potentialConversionRate}
                    sx={{ mt: 2, height: 8, borderRadius: 4 }}
                    color="success"
                  />
                </Box>
              </Grid>
            </Grid>

            <Box sx={{ textAlign: 'center', mt: 4, p: 3, bgcolor: '#e3f2fd', borderRadius: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#1976d2', mb: 1 }}>
                Potential Improvement
              </Typography>
              <Typography variant="body1">
                By improving your response time to under 5 minutes, you could increase your conversion rate by{' '}
                <strong>{potentialConversionRate - currentConversionRate} percentage points</strong>
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Audit Details */}
      <Card sx={{ mb: 4, borderRadius: 3, boxShadow: 3 }}>
        <CardContent sx={{ p: 4 }}>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', textAlign: 'center', mb: 3 }}>
            Audit Details
          </Typography>

          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                Test Submission
              </Typography>
              <Box sx={{ p: 3, bgcolor: '#f5f5f5', borderRadius: 2 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Form URL
                </Typography>
                <Typography variant="body1" gutterBottom sx={{ wordBreak: 'break-all' }}>
                  {summary.form_url}
                </Typography>

                <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                  Submitted
                </Typography>
                <Typography variant="body1">
                  {new Date(summary.submission_time).toLocaleString()}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                Response Analysis
              </Typography>
              <Box sx={{ p: 3, bgcolor: '#f5f5f5', borderRadius: 2 }}>
                {summary.responded ? (
                  <>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Response Channel
                    </Typography>
                    <Typography variant="body1" gutterBottom sx={{ textTransform: 'capitalize' }}>
                      {summary.response_channel}
                    </Typography>

                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                      Response Time
                    </Typography>
                    <Typography variant="body1">
                      {summary.response_time_human}
                    </Typography>
                  </>
                ) : (
                  <Typography variant="body1" color="error.main">
                    No response received within 48-hour monitoring period
                  </Typography>
                )}
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Professional CTA - Only show if there's room for improvement */}
      {(dynamicFeedback.urgency_level === 'HIGH' || dynamicFeedback.urgency_level === 'MEDIUM' || dynamicFeedback.urgency_level === 'CRITICAL') && (
        <Card sx={{
          mb: 4,
          borderRadius: 3,
          boxShadow: 3,
          border: '2px solid #1976d2'
        }}>
          <CardContent sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2' }}>
              Ready to Improve Your Response Time?
            </Typography>
            <Typography variant="body1" sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}>
              Based on your audit results, there's clear opportunity to improve your lead response performance.
              We specialize in helping businesses optimize their lead response systems.
            </Typography>

            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center" alignItems="center">
              <Button
                variant="contained"
                size="large"
                sx={{
                  bgcolor: '#1976d2',
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  '&:hover': { bgcolor: '#1565c0' }
                }}
              >
                Schedule Consultation
              </Button>
              <Button
                variant="outlined"
                size="large"
                sx={{
                  borderColor: '#1976d2',
                  color: '#1976d2',
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  '&:hover': {
                    bgcolor: 'rgba(25, 118, 210, 0.04)',
                    borderColor: '#1976d2'
                  }
                }}
              >
                Learn More
              </Button>
            </Stack>
          </CardContent>
        </Card>
      )}

      {/* Final CTA Footer */}
      <Paper sx={{
        p: 4,
        textAlign: 'center',
        bgcolor: '#f5f5f5',
        borderRadius: 3,
        border: '2px solid #1976d2'
      }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: '#1976d2' }}>
          📊 CONFIDENTIAL AUDIT REPORT
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          This comprehensive analysis was prepared exclusively for {summary.company_name} management.
          <br />
          Based on real lead submission and 48-hour response monitoring.
        </Typography>

        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center" alignItems="center">
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            sx={{
              borderColor: '#1976d2',
              color: '#1976d2',
              '&:hover': {
                bgcolor: 'rgba(25, 118, 210, 0.04)',
                borderColor: '#1976d2'
              }
            }}
          >
            Download PDF Report
          </Button>
          <Button
            variant="contained"
            sx={{
              bgcolor: '#1976d2',
              '&:hover': { bgcolor: '#1565c0' }
            }}
          >
            Share with Team
          </Button>
        </Stack>

        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 3 }}>
          © {new Date().getFullYear()} Lead Response Audit System | Confidential Business Intelligence
        </Typography>
      </Paper>
    </Container>
  );
};

// Arrow icon component
const ArrowIcon = () => (
  <Box sx={{ mx: 1, color: 'text.secondary' }}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z" fill="currentColor" />
    </svg>
  </Box>
);

export default AuditReportBalanced;
