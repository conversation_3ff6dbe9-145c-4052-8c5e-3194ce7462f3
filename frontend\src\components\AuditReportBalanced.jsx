import React, { useState, useEffect } from 'react';
import {
  <PERSON>, CardContent, Typography, Grid, Box, CircularProgress, Divider,
  Chip, Button, Paper, Avatar, LinearProgress, useMediaQuery
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  AccessTime as AccessTimeIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  TrendingDown as TrendingDownIcon,
  MonetizationOn as MonetizationOnIcon,
  Warning as WarningIcon,
  Download as DownloadIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';

// Mock data for the audit report
const mockAuditData = {
  summary: {
    company_name: "Acme Roofing Solutions",
    website: "acmeroofing.com",
    form_url: "https://acmeroofing.com/contact",
    submission_time: "2023-05-03T14:32:15Z",
    audit_completed: "2023-05-05T14:32:15Z",
    responded: true,
    response_time_seconds: 2180,
    response_time_human: "0:36:20",
    response_channel: "sms",
    time_score: 60,
    channel_bonus: 15,
    multi_channel_bonus: 0, // No multi-channel bonus in this example
    channels_used: ["sms"], // Only SMS used in this example
    total_score: 75,
    grade: "C",
    industry_avg_score: 45,
    industry_avg_grade: "D",
    industry_avg_response_time: "1:42:15",
    industry_best_practice: "Under 5 minutes via phone followed by multi-channel follow-up"
  },
  response_details: {
    submission: {
      timestamp: "2023-05-03T14:32:15Z",
      form_url: "https://acmeroofing.com/contact",
      form_type: "Contact Form",
      fields_submitted: {
        name: "John Smith",
        email: "<EMAIL>",
        phone: "(*************",
        message: "I need a quote for a new roof installation."
      }
    },
    responses: [
      {
        channel: "sms",
        timestamp: "2023-05-03T15:08:35Z",
        delay_seconds: 2180,
        delay_human: "0:36:20",
        content: "Hi John, thanks for your inquiry about a new roof. This is Mike from Acme Roofing. When would be a good time to discuss your project?",
        sender_info: {
          name: "Mike Johnson",
          role: "Sales Representative",
          phone: "(*************"
        }
      },
      // Additional responses would be listed here for multi-channel examples
      // Example of a follow-up email response
      /*
      {
        channel: "email",
        timestamp: "2023-05-03T15:15:22Z",
        delay_seconds: 2587,
        delay_human: "0:43:07",
        content: "Hello John, I just sent you a text message about your roof installation inquiry. I've also attached our brochure with some examples of our recent work. Looking forward to discussing your project!",
        sender_info: {
          name: "Mike Johnson",
          role: "Sales Representative",
          email: "<EMAIL>"
        }
      },
      // Example of a follow-up phone call
      {
        channel: "phone",
        timestamp: "2023-05-03T15:30:45Z",
        delay_seconds: 3510,
        delay_human: "0:58:30",
        content: "Called to follow up on text and email. Discussed project details and scheduled an in-person consultation.",
        sender_info: {
          name: "Mike Johnson",
          role: "Sales Representative",
          phone: "(*************"
        }
      }
      */
    ]
  },
  response_channels: {
    phone: {
      used: false,
      time_score: 0,
      total_score: 0,
      grade: "F",
      potential_score: 125,
      potential_grade: "A+",
      industry_usage: "Used by 45% of top performers"
    },
    sms: {
      used: true,
      time_score: 60,
      total_score: 75,
      grade: "C",
      potential_score: 115,
      potential_grade: "A",
      industry_usage: "Used by 30% of top performers"
    },
    email: {
      used: false,
      time_score: 0,
      total_score: 0,
      grade: "F",
      potential_score: 105,
      potential_grade: "B",
      industry_usage: "Used by 25% of top performers"
    }
  },
  multi_channel: {
    used: false,
    bonus_points: 0,
    potential_bonus: 10,
    description: "Using multiple channels increases response effectiveness by 35%",
    industry_usage: "Used by only 15% of businesses but 80% of top performers",
    best_practice: "Initial response via phone within 5 minutes, followed by email with details, and SMS for appointment confirmation"
  }
};

// Helper function to get color based on grade
const getGradeColor = (grade) => {
  switch (grade) {
    case 'A+': return '#4caf50'; // Green
    case 'A': return '#8bc34a'; // Light Green
    case 'B': return '#ffeb3b'; // Yellow
    case 'C': return '#ff9800'; // Orange
    case 'D': return '#f44336'; // Red
    case 'F': return '#9e9e9e'; // Grey
    default: return '#9e9e9e'; // Grey
  }
};

// Helper function to get icon for channel
const getChannelIcon = (channel) => {
  switch (channel) {
    case 'phone': return <PhoneIcon />;
    case 'sms': return <SmsIcon />;
    case 'email': return <EmailIcon />;
    case 'NO_RESPONSE': return <CancelIcon />;
    default: return null;
  }
};

const AuditReportBalanced = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [loading, setLoading] = useState(false);
  const [auditData, setAuditData] = useState(mockAuditData);

  // Fetch audit data from API
  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    setLoading(true);
    setTimeout(() => {
      setAuditData(mockAuditData);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  const { summary } = auditData;
  const gradeColor = getGradeColor(summary.grade);

  // Calculate conversion rate based on response time
  const getConversionRate = (seconds) => {
    if (seconds < 300) return "85%";
    if (seconds < 600) return "70%";
    if (seconds < 1800) return "52%";
    if (seconds < 3600) return "40%";
    if (seconds < 10800) return "28%";
    if (seconds < 86400) return "15%";
    return "5%";
  };

  const conversionRate = getConversionRate(summary.response_time_seconds);
  const potentialConversionRate = "85%";

  return (
    <Box sx={{ p: { xs: 2, md: 4 }, maxWidth: 1200, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
          LEAD RESPONSE AUDIT REPORT
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          {summary.company_name}
        </Typography>
        <Divider sx={{ my: 2 }} />
      </Box>

      {/* Grade Card */}
      <Card sx={{
        mb: 4,
        boxShadow: 3,
        borderRadius: 2,
        overflow: 'hidden',
        background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.paper} 50%, ${gradeColor}10 100%)`
      }}>
        <Box sx={{ bgcolor: gradeColor, height: 8 }} />
        <CardContent sx={{ p: 3 }}>
          <Grid container spacing={3} alignItems="center">
            {/* Grade */}
            <Grid item xs={12} md={3}>
              <Box sx={{
                textAlign: 'center',
                p: 2,
                borderRadius: 2,
                bgcolor: `${gradeColor}15`,
                border: `2px solid ${gradeColor}`
              }}>
                <Typography variant="overline" sx={{ fontWeight: 'medium' }}>YOUR GRADE</Typography>
                <Typography variant="h1" sx={{ fontWeight: 'bold', color: gradeColor, lineHeight: 1.2 }}>
                  {summary.grade}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 1 }}>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {summary.total_score}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
                    /125 points
                  </Typography>
                </Box>
              </Box>
            </Grid>

            {/* Response Details */}
            <Grid item xs={12} md={5}>
              <Box>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'medium' }}>Response Details</Typography>

                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={6}>
                    <Paper elevation={0} sx={{ p: 1.5, bgcolor: 'background.default', borderRadius: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ bgcolor: summary.responded ? 'success.main' : 'error.main', width: 32, height: 32 }}>
                          {summary.responded ? <CheckCircleIcon fontSize="small" /> : <CancelIcon fontSize="small" />}
                        </Avatar>
                        <Box sx={{ ml: 1 }}>
                          <Typography variant="body2" color="text.secondary">Status</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                            {summary.responded ? "Responded" : "No Response"}
                          </Typography>
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>

                  <Grid item xs={6}>
                    <Paper elevation={0} sx={{ p: 1.5, bgcolor: 'background.default', borderRadius: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{
                          bgcolor: summary.response_channel === 'phone' ? '#4caf50' :
                                   summary.response_channel === 'sms' ? '#2196f3' : '#ff9800',
                          width: 32,
                          height: 32
                        }}>
                          {getChannelIcon(summary.response_channel)}
                        </Avatar>
                        <Box sx={{ ml: 1 }}>
                          <Typography variant="body2" color="text.secondary">Channel</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 'medium', textTransform: 'capitalize' }}>
                            {summary.response_channel}
                          </Typography>
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>

                <Paper elevation={0} sx={{ p: 1.5, bgcolor: 'background.default', borderRadius: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <AccessTimeIcon sx={{ color: summary.time_score > 80 ? 'success.main' : 'warning.main', mr: 1 }} />
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" color="text.secondary">Response Time</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                        {summary.response_time_human}
                      </Typography>
                    </Box>
                    <Chip
                      label={`${summary.time_score} pts`}
                      size="small"
                      sx={{
                        bgcolor: summary.time_score > 80 ? 'success.main' :
                                summary.time_score > 40 ? 'warning.main' : 'error.main',
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </Box>
                </Paper>
              </Box>
            </Grid>

            {/* Impact */}
            <Grid item xs={12} md={4}>
              <Box>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'medium' }}>Conversion Impact</Typography>

                <Paper elevation={0} sx={{ p: 2, bgcolor: 'background.default', borderRadius: 2, mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>Current vs. Potential</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="h5" color="error.main" sx={{ fontWeight: 'bold' }}>
                      {conversionRate}
                    </Typography>
                    <ArrowIcon />
                    <Typography variant="h5" color="success.main" sx={{ fontWeight: 'bold' }}>
                      {potentialConversionRate}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={parseInt(conversionRate) / 0.85}
                    sx={{ height: 8, borderRadius: 4, mb: 1 }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    Optimizing your response could increase conversions by {Math.round((85 - parseInt(conversionRate)) / parseInt(conversionRate) * 100)}%
                  </Typography>
                </Paper>

                <Paper elevation={0} sx={{ p: 2, bgcolor: '#e3f2fd', borderRadius: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <MonetizationOnIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      Estimated Revenue Impact:
                      <Box component="span" sx={{ color: 'error.main', fontWeight: 'bold', ml: 0.5 }}>
                        {summary.grade === 'A+' ? "$0" :
                         summary.grade === 'A' ? "-$2,500" :
                         summary.grade === 'B' ? "-$8,000" :
                         summary.grade === 'C' ? "-$15,000" :
                         summary.grade === 'D' ? "-$25,000" : "-$40,000"}
                      </Box>
                      <Typography variant="caption" display="block" color="text.secondary">
                        Monthly revenue loss
                      </Typography>
                    </Typography>
                  </Box>
                </Paper>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Response Analysis */}
      <Card sx={{ mb: 4, boxShadow: 3, borderRadius: 2 }}>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
            RESPONSE ANALYSIS
          </Typography>

          {/* Multi-Channel Strategy */}
          <Paper elevation={0} sx={{ p: 2, bgcolor: '#f0f7ff', borderRadius: 2, mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
              <Avatar sx={{ bgcolor: '#1565c0', mr: 2 }}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 18H4V6H20V18Z" fill="white"/>
                  <path d="M6 10H8V12H6V10Z" fill="white"/>
                  <path d="M6 14H8V16H6V14Z" fill="white"/>
                  <path d="M10 10H18V12H10V10Z" fill="white"/>
                  <path d="M10 14H15V16H10V14Z" fill="white"/>
                </svg>
              </Avatar>
              <Box>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'medium', color: '#1565c0' }}>
                  Multi-Channel Strategy
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Top performers use multiple channels</strong> to follow up with leads. This approach increases response effectiveness by 35% compared to single-channel responses.
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1 }}>
                  <Chip
                    icon={<PhoneIcon fontSize="small" />}
                    label="Initial Phone Call"
                    size="small"
                    sx={{ bgcolor: '#4caf50', color: 'white' }}
                  />
                  <Typography variant="body2" sx={{ mx: 0.5 }}>+</Typography>
                  <Chip
                    icon={<EmailIcon fontSize="small" />}
                    label="Follow-up Email"
                    size="small"
                    sx={{ bgcolor: '#ff9800', color: 'white' }}
                  />
                  <Typography variant="body2" sx={{ mx: 0.5 }}>+</Typography>
                  <Chip
                    icon={<SmsIcon fontSize="small" />}
                    label="SMS Confirmation"
                    size="small"
                    sx={{ bgcolor: '#2196f3', color: 'white' }}
                  />
                  <Chip
                    label={`+${auditData.multi_channel.potential_bonus} bonus pts`}
                    size="small"
                    sx={{ ml: 1, bgcolor: '#1565c0', color: 'white', fontWeight: 'bold' }}
                  />
                </Box>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                  {auditData.multi_channel.industry_usage}
                </Typography>
              </Box>
            </Box>
          </Paper>

          <Grid container spacing={3}>
            {/* Response Time Impact */}
            <Grid item xs={12} md={6}>
              <Paper elevation={0} sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2, height: '100%' }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <SpeedIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'medium' }}>
                      Response Time Impact
                    </Typography>

                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="body2">5-minute response:</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'success.main' }}>85% conversion</Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={85}
                        sx={{ height: 6, borderRadius: 3, bgcolor: 'background.paper', mb: 1 }}
                        color="success"
                      />
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="body2">30-minute response:</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'warning.main' }}>52% conversion</Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={52}
                        sx={{ height: 6, borderRadius: 3, bgcolor: 'background.paper', mb: 1 }}
                        color="warning"
                      />
                    </Box>

                    <Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="body2">Your response ({summary.response_time_human}):</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'error.main' }}>{conversionRate} conversion</Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={parseInt(conversionRate)}
                        sx={{ height: 6, borderRadius: 3, bgcolor: 'background.paper' }}
                        color="error"
                      />
                    </Box>
                  </Box>
                </Box>
              </Paper>
            </Grid>

            {/* Channel Performance */}
            <Grid item xs={12} md={6}>
              <Paper elevation={0} sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2, height: '100%' }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <PhoneIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'medium' }}>
                      Channel Performance
                    </Typography>

                    <Grid container spacing={2}>
                      {Object.entries(auditData.response_channels).map(([channel, data]) => (
                        <Grid item xs={12} key={channel}>
                          <Box sx={{
                            p: 1.5,
                            borderRadius: 1,
                            bgcolor: data.used ? `${getGradeColor(data.grade)}15` : 'background.paper',
                            border: data.used ? `1px solid ${getGradeColor(data.grade)}` : '1px solid #e0e0e0',
                            display: 'flex',
                            alignItems: 'center'
                          }}>
                            <Avatar
                              sx={{
                                bgcolor: channel === 'phone' ? '#4caf50' :
                                         channel === 'sms' ? '#2196f3' : '#ff9800',
                                width: 32,
                                height: 32,
                                mr: 1.5,
                                opacity: data.used ? 1 : 0.5
                              }}
                            >
                              {getChannelIcon(channel)}
                            </Avatar>

                            <Box sx={{ flexGrow: 1 }}>
                              <Typography variant="body2" sx={{
                                fontWeight: 'medium',
                                textTransform: 'capitalize',
                                color: data.used ? 'text.primary' : 'text.secondary'
                              }}>
                                {channel}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {data.industry_usage}
                              </Typography>
                            </Box>

                            <Chip
                              label={data.used ? `${data.total_score} pts` : `+${channel === 'phone' ? '25' : channel === 'sms' ? '15' : '5'} pts`}
                              size="small"
                              sx={{
                                bgcolor: data.used ? getGradeColor(data.grade) : 'background.paper',
                                color: data.used ? 'white' : 'text.secondary',
                                border: data.used ? 'none' : '1px solid #e0e0e0',
                                fontWeight: 'medium'
                              }}
                            />
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Submission Details */}
      <Card sx={{ mb: 4, boxShadow: 3, borderRadius: 2 }}>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
            SUBMISSION DETAILS
          </Typography>

          <Grid container spacing={3}>
            {/* Form Submission */}
            <Grid item xs={12} md={6}>
              <Paper elevation={0} sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2 }}>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'medium', color: 'primary.main' }}>
                  Test Submission
                </Typography>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">Form URL</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                    {auditData.response_details.submission.form_url}
                  </Typography>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">Submission Time</Typography>
                  <Typography variant="body1">
                    {new Date(auditData.response_details.submission.timestamp).toLocaleString()}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">Information Submitted</Typography>
                  <Box sx={{ mt: 1, p: 1.5, bgcolor: 'background.paper', borderRadius: 1 }}>
                    {Object.entries(auditData.response_details.submission.fields_submitted).map(([field, value]) => (
                      <Box key={field} sx={{ mb: 1 }}>
                        <Typography variant="caption" sx={{ fontWeight: 'medium', textTransform: 'capitalize' }}>
                          {field}:
                        </Typography>
                        <Typography variant="body2">{value}</Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              </Paper>
            </Grid>

            {/* Response Details */}
            <Grid item xs={12} md={6}>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  bgcolor: '#f5f5f5',
                  borderRadius: 2,
                  borderLeft: `4px solid ${getGradeColor(summary.grade)}`
                }}
              >
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'medium', color: summary.responded ? 'success.main' : 'error.main' }}>
                  {summary.responded ? 'Your Response' : 'No Response Received'}
                </Typography>

                {summary.responded ? (
                  <>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">Response Time</Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                        <AccessTimeIcon fontSize="small" color={
                          summary.time_score > 80 ? "success" :
                          summary.time_score > 40 ? "warning" : "error"
                        } sx={{ mr: 1 }} />
                        <Typography variant="body1">
                          {summary.response_time_human} after submission
                        </Typography>
                      </Box>
                    </Box>

                    <Box>
                      <Typography variant="body2" color="text.secondary">Response Content</Typography>
                      <Box sx={{ mt: 1, p: 1.5, bgcolor: 'background.paper', borderRadius: 1 }}>
                        <Typography variant="body2" sx={{ fontStyle: 'italic', mb: 1 }}>
                          "{auditData.response_details.responses[0].content}"
                        </Typography>
                        <Typography variant="caption">
                          - {auditData.response_details.responses[0].sender_info.name}, {auditData.response_details.responses[0].sender_info.role}
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" color="text.secondary">Score Breakdown</Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 0.5 }}>
                        <Chip
                          label={`Time: ${summary.time_score} pts`}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                        <Chip
                          label={`Channel: +${summary.channel_bonus} pts`}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                        {summary.multi_channel_bonus > 0 && (
                          <Chip
                            label={`Multi-Channel: +${summary.multi_channel_bonus} pts`}
                            size="small"
                            color="success"
                            variant="outlined"
                          />
                        )}
                        <Chip
                          label={`Total: ${summary.total_score} pts`}
                          size="small"
                          sx={{
                            bgcolor: getGradeColor(summary.grade),
                            color: 'white',
                            fontWeight: 'bold'
                          }}
                        />
                      </Box>
                    </Box>
                  </>
                ) : (
                  <Box sx={{ p: 3, textAlign: 'center' }}>
                    <CancelIcon color="error" sx={{ fontSize: 60, mb: 2, opacity: 0.7 }} />
                    <Typography variant="body1" paragraph>
                      No response received within the 48-hour monitoring period.
                    </Typography>
                    <Typography variant="body2" color="error.main">
                      This results in a grade of F (0/125 points).
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card sx={{ mb: 4, bgcolor: '#e8f5e9', boxShadow: 3, borderRadius: 2 }}>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
            RECOMMENDATIONS
          </Typography>

          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <Box sx={{ p: 2, bgcolor: 'white', borderRadius: 2, height: '100%' }}>
                <Typography variant="h6" gutterBottom sx={{ color: '#2e7d32' }}>1. Response Time Optimization</Typography>
                <Typography variant="body2">
                  {summary.responded ? (
                    <>
                      Reduce your response time from {summary.response_time_human} to under 5 minutes.
                      This would increase your conversion rate from {conversionRate} to 85%.
                    </>
                  ) : (
                    <>
                      Implement a system that ensures all leads receive a response within 5 minutes.
                      This would increase your conversion rate from 0% to approximately 85%.
                    </>
                  )}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box sx={{ p: 2, bgcolor: 'white', borderRadius: 2, height: '100%' }}>
                <Typography variant="h6" gutterBottom sx={{ color: '#2e7d32' }}>2. Channel Prioritization</Typography>
                <Typography variant="body2">
                  {summary.response_channel === 'phone' ? (
                    <>
                      You're already using the optimal channel (phone). Continue this practice
                      while ensuring faster response times for maximum effectiveness.
                    </>
                  ) : summary.response_channel === 'sms' ? (
                    <>
                      While SMS is good, phone calls convert 3x better.
                      Consider using phone as your primary response channel.
                    </>
                  ) : summary.response_channel === 'email' ? (
                    <>
                      Email is your least effective option. Phone calls convert 3x better
                      than email responses, and SMS converts 2x better.
                    </>
                  ) : (
                    <>
                      Prioritize phone calls for high-value leads, followed by SMS.
                      Our data shows phone calls convert 3x better than email responses.
                    </>
                  )}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box sx={{ p: 2, bgcolor: 'white', borderRadius: 2, height: '100%' }}>
                <Typography variant="h6" gutterBottom sx={{ color: '#2e7d32' }}>3. Multi-Channel Follow-up</Typography>
                <Box>
                  <Typography variant="body2">
                    Implement a multi-channel follow-up strategy that includes:
                  </Typography>
                  <Box component="ul" sx={{ paddingLeft: '1.2rem', marginTop: '0.5rem', marginBottom: 0 }}>
                    <li>Initial phone call within 5 minutes</li>
                    <li>Follow-up email with detailed information</li>
                    <li>SMS confirmation for appointments</li>
                  </Box>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    This approach earns a {auditData.multi_channel.potential_bonus}-point bonus and increases conversion by 35%.
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box sx={{ p: 2, bgcolor: 'white', borderRadius: 2, height: '100%' }}>
                <Typography variant="h6" gutterBottom sx={{ color: '#2e7d32' }}>4. Automated Lead Routing</Typography>
                <Typography variant="body2">
                  Implement intelligent lead routing to ensure leads are sent to available team members
                  who can respond immediately. This eliminates the {summary.responded ?
                    `${summary.response_time_human} delay` :
                    "non-response issue"} we observed in our test.
                </Typography>
              </Box>
            </Grid>
          </Grid>

          <Box display="flex" justifyContent="center" mt={3}>
            <Button
              variant="contained"
              color="success"
              size="large"
              sx={{ px: 4, py: 1.5, fontWeight: 'bold' }}
            >
              Schedule a Consultation
            </Button>
          </Box>
        </CardContent>
      </Card>

      <Box mt={4} display="flex" justifyContent="center" flexDirection="column" alignItems="center">
        <Button variant="outlined" startIcon={<DownloadIcon />} sx={{ mb: 2 }}>
          Download Full Report
        </Button>
        <Typography variant="caption" color="text.secondary" align="center">
          This report is confidential and intended only for {summary.company_name} management.
          <br />
          © {new Date().getFullYear()} Lead Response Audit System
        </Typography>
      </Box>
    </Box>
  );
};

// Arrow icon component
const ArrowIcon = () => (
  <Box sx={{ mx: 1, color: 'text.secondary' }}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z" fill="currentColor" />
    </svg>
  </Box>
);

export default AuditReportBalanced;
