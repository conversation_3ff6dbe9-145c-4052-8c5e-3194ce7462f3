import React, { useState, useEffect } from 'react';
import {
  <PERSON>, CardContent, Typography, Grid, Box, CircularProgress, Divider,
  Chip, Button, Paper, Avatar, LinearProgress, useMediaQuery, Alert,
  Container, Stack, Fade, Grow
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  AccessTime as AccessTimeIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  TrendingDown as TrendingDownIcon,
  MonetizationOn as MonetizationOnIcon,
  Warning as WarningIcon,
  Download as DownloadIcon,
  Speed as SpeedIcon,
  ArrowForward as ArrowForwardIcon,
  Schedule as ScheduleIcon,
  CallMade as CallMadeIcon,
  TrendingUp as TrendingUpIcon,
  LocalFireDepartment as FireIcon,
  Psychology as PsychologyIcon,
  CompareArrows as CompareArrowsIcon
} from '@mui/icons-material';

// Mock data for the audit report - optimized for conversion impact
const mockAuditData = {
  summary: {
    company_name: "Acme Roofing Solutions",
    website: "acmeroofing.com",
    form_url: "https://acmeroofing.com/contact",
    submission_time: "2023-05-03T14:32:15Z",
    audit_completed: "2023-05-05T14:32:15Z",
    responded: true,
    response_time_seconds: 2180,
    response_time_human: "36 minutes",
    response_channel: "sms",
    time_score: 60,
    channel_bonus: 15,
    multi_channel_bonus: 0,
    channels_used: ["sms"],
    total_score: 75,
    grade: "C",
    industry_avg_score: 45,
    industry_avg_grade: "D",
    industry_avg_response_time: "1:42:15",
    industry_best_practice: "Under 5 minutes via phone followed by multi-channel follow-up",
    // Conversion-focused data
    brutal_feedback: "🔴 You're slow. You lose to the first responder — always.",
    punchline: "If you were in a race with your competitors, you'd be in the parking lot while they're cashing checks.",
    monthly_revenue_loss: 15000,
    leads_lost_monthly: 47,
    competitor_advantage: "Your competitors who respond in 5 minutes are stealing 6 out of every 10 deals from you.",
    urgency_factor: "HIGH", // HIGH, MEDIUM, LOW
    qualification_status: "QUALIFIED" // QUALIFIED, DISQUALIFIED
  },
  response_details: {
    submission: {
      timestamp: "2023-05-03T14:32:15Z",
      form_url: "https://acmeroofing.com/contact",
      form_type: "Contact Form",
      fields_submitted: {
        name: "John Smith",
        email: "<EMAIL>",
        phone: "(*************",
        message: "I need a quote for a new roof installation."
      }
    },
    responses: [
      {
        channel: "sms",
        timestamp: "2023-05-03T15:08:35Z",
        delay_seconds: 2180,
        delay_human: "0:36:20",
        content: "Hi John, thanks for your inquiry about a new roof. This is Mike from Acme Roofing. When would be a good time to discuss your project?",
        sender_info: {
          name: "Mike Johnson",
          role: "Sales Representative",
          phone: "(*************"
        }
      },
      // Additional responses would be listed here for multi-channel examples
      // Example of a follow-up email response
      /*
      {
        channel: "email",
        timestamp: "2023-05-03T15:15:22Z",
        delay_seconds: 2587,
        delay_human: "0:43:07",
        content: "Hello John, I just sent you a text message about your roof installation inquiry. I've also attached our brochure with some examples of our recent work. Looking forward to discussing your project!",
        sender_info: {
          name: "Mike Johnson",
          role: "Sales Representative",
          email: "<EMAIL>"
        }
      },
      // Example of a follow-up phone call
      {
        channel: "phone",
        timestamp: "2023-05-03T15:30:45Z",
        delay_seconds: 3510,
        delay_human: "0:58:30",
        content: "Called to follow up on text and email. Discussed project details and scheduled an in-person consultation.",
        sender_info: {
          name: "Mike Johnson",
          role: "Sales Representative",
          phone: "(*************"
        }
      }
      */
    ]
  },
  response_channels: {
    phone: {
      used: false,
      time_score: 0,
      total_score: 0,
      grade: "F",
      potential_score: 125,
      potential_grade: "A+",
      industry_usage: "Used by 45% of top performers"
    },
    sms: {
      used: true,
      time_score: 60,
      total_score: 75,
      grade: "C",
      potential_score: 115,
      potential_grade: "A",
      industry_usage: "Used by 30% of top performers"
    },
    email: {
      used: false,
      time_score: 0,
      total_score: 0,
      grade: "F",
      potential_score: 105,
      potential_grade: "B",
      industry_usage: "Used by 25% of top performers"
    }
  },
  multi_channel: {
    used: false,
    bonus_points: 0,
    potential_bonus: 10,
    description: "Using multiple channels increases response effectiveness by 35%",
    industry_usage: "Used by only 15% of businesses but 80% of top performers",
    best_practice: "Initial response via phone within 5 minutes, followed by email with details, and SMS for appointment confirmation"
  }
};

// Helper function to get color based on grade
const getGradeColor = (grade) => {
  switch (grade) {
    case 'A+': return '#4caf50'; // Green
    case 'A': return '#8bc34a'; // Light Green
    case 'B': return '#ffeb3b'; // Yellow
    case 'C': return '#ff9800'; // Orange
    case 'D': return '#f44336'; // Red
    case 'F': return '#9e9e9e'; // Grey
    default: return '#9e9e9e'; // Grey
  }
};

// Helper function to get icon for channel
const getChannelIcon = (channel) => {
  switch (channel) {
    case 'phone': return <PhoneIcon />;
    case 'sms': return <SmsIcon />;
    case 'email': return <EmailIcon />;
    case 'NO_RESPONSE': return <CancelIcon />;
    default: return null;
  }
};

const AuditReportBalanced = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [loading, setLoading] = useState(false);
  const [auditData, setAuditData] = useState(mockAuditData);

  // Fetch audit data from API
  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    setLoading(true);
    setTimeout(() => {
      setAuditData(mockAuditData);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  const { summary } = auditData;
  const gradeColor = getGradeColor(summary.grade);

  // Calculate conversion rate based on response time
  const getConversionRate = (seconds) => {
    if (seconds < 300) return "85%";
    if (seconds < 600) return "70%";
    if (seconds < 1800) return "52%";
    if (seconds < 3600) return "40%";
    if (seconds < 10800) return "28%";
    if (seconds < 86400) return "15%";
    return "5%";
  };

  const conversionRate = getConversionRate(summary.response_time_seconds);
  const potentialConversionRate = "85%";

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Urgent Header with Brutal Feedback */}
      <Fade in timeout={800}>
        <Card sx={{
          mb: 4,
          background: 'linear-gradient(135deg, #ff1744 0%, #d32f2f 100%)',
          color: 'white',
          boxShadow: '0 8px 32px rgba(255, 23, 68, 0.3)',
          borderRadius: 3,
          overflow: 'hidden'
        }}>
          <CardContent sx={{ p: 4, textAlign: 'center' }}>
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
              <FireIcon sx={{ fontSize: 40, mr: 1 }} />
              <Typography variant="h3" sx={{ fontWeight: 900, textShadow: '2px 2px 4px rgba(0,0,0,0.3)' }}>
                URGENT
              </Typography>
            </Box>
            <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
              LEAD RESPONSE AUDIT REPORT
            </Typography>
            <Typography variant="h5" sx={{ fontWeight: 'medium', opacity: 0.95, mb: 2 }}>
              {summary.company_name}
            </Typography>

            {/* Brutal Feedback */}
            <Paper sx={{
              p: 3,
              bgcolor: 'rgba(255,255,255,0.95)',
              color: 'text.primary',
              borderRadius: 2,
              mt: 3,
              border: '3px solid #ff5722'
            }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#d32f2f', mb: 1 }}>
                {summary.brutal_feedback}
              </Typography>
              <Typography variant="body1" sx={{ fontStyle: 'italic', color: '#666' }}>
                {summary.punchline}
              </Typography>
            </Paper>
          </CardContent>
        </Card>
      </Fade>

      {/* Revenue Loss Dashboard */}
      <Grow in timeout={1200}>
        <Card sx={{
          mb: 4,
          boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
          borderRadius: 3,
          overflow: 'hidden',
          border: '2px solid #ff5722'
        }}>
          <Box sx={{
            background: 'linear-gradient(135deg, #ff5722 0%, #d32f2f 100%)',
            color: 'white',
            p: 2,
            textAlign: 'center'
          }}>
            <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
              💸 REVENUE HEMORRHAGING ALERT 💸
            </Typography>
          </Box>
          <CardContent sx={{ p: 4 }}>
            <Grid container spacing={4} alignItems="center">
              {/* Monthly Loss */}
              <Grid item xs={12} md={4}>
                <Paper sx={{
                  p: 3,
                  textAlign: 'center',
                  bgcolor: '#ffebee',
                  border: '2px solid #f44336',
                  borderRadius: 2
                }}>
                  <MonetizationOnIcon sx={{ fontSize: 48, color: '#f44336', mb: 1 }} />
                  <Typography variant="overline" sx={{ fontWeight: 'bold', color: '#666' }}>
                    MONTHLY REVENUE LOSS
                  </Typography>
                  <Typography variant="h3" sx={{ fontWeight: 900, color: '#f44336', lineHeight: 1 }}>
                    ${summary.monthly_revenue_loss.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Due to slow response times
                  </Typography>
                </Paper>
              </Grid>

              {/* Leads Lost */}
              <Grid item xs={12} md={4}>
                <Paper sx={{
                  p: 3,
                  textAlign: 'center',
                  bgcolor: '#fff3e0',
                  border: '2px solid #ff9800',
                  borderRadius: 2
                }}>
                  <TrendingDownIcon sx={{ fontSize: 48, color: '#ff9800', mb: 1 }} />
                  <Typography variant="overline" sx={{ fontWeight: 'bold', color: '#666' }}>
                    LEADS LOST MONTHLY
                  </Typography>
                  <Typography variant="h3" sx={{ fontWeight: 900, color: '#ff9800', lineHeight: 1 }}>
                    {summary.leads_lost_monthly}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    To faster competitors
                  </Typography>
                </Paper>
              </Grid>

              {/* Your Grade */}
              <Grid item xs={12} md={4}>
                <Paper sx={{
                  p: 3,
                  textAlign: 'center',
                  bgcolor: `${gradeColor}15`,
                  border: `2px solid ${gradeColor}`,
                  borderRadius: 2
                }}>
                  <WarningIcon sx={{ fontSize: 48, color: gradeColor, mb: 1 }} />
                  <Typography variant="overline" sx={{ fontWeight: 'bold', color: '#666' }}>
                    RESPONSE GRADE
                  </Typography>
                  <Typography variant="h3" sx={{ fontWeight: 900, color: gradeColor, lineHeight: 1 }}>
                    {summary.grade}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {summary.total_score}/125 points
                  </Typography>
                </Paper>
              </Grid>
            </Grid>

            {/* Competitive Analysis Alert */}
            <Alert severity="error" sx={{ mt: 3, p: 3, borderRadius: 2, border: '2px solid #f44336' }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                🚨 COMPETITIVE THREAT ANALYSIS
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {summary.competitor_advantage}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                <Chip
                  icon={<AccessTimeIcon />}
                  label={`Your Response: ${summary.response_time_human}`}
                  sx={{ bgcolor: '#f44336', color: 'white', fontWeight: 'bold' }}
                />
                <CompareArrowsIcon sx={{ color: '#666' }} />
                <Chip
                  icon={<TrendingUpIcon />}
                  label="Competitors: 5 minutes"
                  sx={{ bgcolor: '#4caf50', color: 'white', fontWeight: 'bold' }}
                />
              </Box>
            </Alert>
          </CardContent>
        </Card>
      </Grow>

      {/* Before vs After Transformation */}
      <Fade in timeout={1600}>
        <Card sx={{
          mb: 4,
          boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
          borderRadius: 3,
          overflow: 'hidden'
        }}>
          <Box sx={{
            background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
            color: 'white',
            p: 2,
            textAlign: 'center'
          }}>
            <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
              🎯 YOUR TRANSFORMATION POTENTIAL
            </Typography>
          </Box>
          <CardContent sx={{ p: 4 }}>
            <Grid container spacing={4}>
              {/* BEFORE */}
              <Grid item xs={12} md={6}>
                <Paper sx={{
                  p: 3,
                  bgcolor: '#ffebee',
                  border: '2px solid #f44336',
                  borderRadius: 2,
                  height: '100%'
                }}>
                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#f44336', mb: 1 }}>
                      😰 BEFORE (Current State)
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      What's happening right now
                    </Typography>
                  </Box>

                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CancelIcon sx={{ color: '#f44336', mr: 2 }} />
                      <Typography variant="body1">
                        <strong>{summary.response_time_human}</strong> response time
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <TrendingDownIcon sx={{ color: '#f44336', mr: 2 }} />
                      <Typography variant="body1">
                        <strong>{conversionRate}</strong> conversion rate
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <MonetizationOnIcon sx={{ color: '#f44336', mr: 2 }} />
                      <Typography variant="body1">
                        <strong>-${summary.monthly_revenue_loss.toLocaleString()}/month</strong> revenue loss
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <WarningIcon sx={{ color: '#f44336', mr: 2 }} />
                      <Typography variant="body1">
                        <strong>{summary.leads_lost_monthly} leads lost</strong> to competitors
                      </Typography>
                    </Box>
                  </Stack>
                </Paper>
              </Grid>

              {/* AFTER */}
              <Grid item xs={12} md={6}>
                <Paper sx={{
                  p: 3,
                  bgcolor: '#e8f5e8',
                  border: '2px solid #4caf50',
                  borderRadius: 2,
                  height: '100%',
                  position: 'relative'
                }}>
                  <Box sx={{
                    position: 'absolute',
                    top: -10,
                    right: -10,
                    bgcolor: '#ff9800',
                    color: 'white',
                    px: 2,
                    py: 0.5,
                    borderRadius: 2,
                    transform: 'rotate(15deg)',
                    fontWeight: 'bold',
                    fontSize: '0.8rem'
                  }}>
                    ACHIEVABLE!
                  </Box>

                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#4caf50', mb: 1 }}>
                      🚀 AFTER (With Our Solution)
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      What you could achieve
                    </Typography>
                  </Box>

                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CheckCircleIcon sx={{ color: '#4caf50', mr: 2 }} />
                      <Typography variant="body1">
                        <strong>Under 5 minutes</strong> response time
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <TrendingUpIcon sx={{ color: '#4caf50', mr: 2 }} />
                      <Typography variant="body1">
                        <strong>85%</strong> conversion rate
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <MonetizationOnIcon sx={{ color: '#4caf50', mr: 2 }} />
                      <Typography variant="body1">
                        <strong>+${(summary.monthly_revenue_loss * 1.5).toLocaleString()}/month</strong> revenue gain
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CallMadeIcon sx={{ color: '#4caf50', mr: 2 }} />
                      <Typography variant="body1">
                        <strong>Win against</strong> 90% of competitors
                      </Typography>
                    </Box>
                  </Stack>
                </Paper>
              </Grid>
            </Grid>

            {/* Transformation Arrow */}
            <Box sx={{ textAlign: 'center', my: 3 }}>
              <ArrowForwardIcon sx={{ fontSize: 48, color: '#1976d2' }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                TRANSFORMATION AVAILABLE
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Fade>

      {/* Quick Stats Summary */}
      <Fade in timeout={1400}>
        <Card sx={{ mb: 4, boxShadow: 3, borderRadius: 2 }}>
          <CardContent sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', textAlign: 'center', mb: 3 }}>
              📈 THE BRUTAL TRUTH ABOUT YOUR PERFORMANCE
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, bgcolor: '#ffebee', borderRadius: 2, textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#f44336', mb: 2 }}>
                    ⏰ RESPONSE TIME ANALYSIS
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    You responded in <strong>{summary.response_time_human}</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Industry leaders respond in under 5 minutes
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={Math.min((300 / summary.response_time_seconds) * 100, 100)}
                    sx={{ mt: 2, height: 8, borderRadius: 4 }}
                    color="error"
                  />
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, bgcolor: '#e3f2fd', borderRadius: 2, textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#1976d2', mb: 2 }}>
                    💰 FINANCIAL IMPACT
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    Current conversion rate: <strong>{conversionRate}</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Potential with 5-min response: <strong>85%</strong>
                  </Typography>
                  <Box sx={{ mt: 2, p: 2, bgcolor: '#ffcdd2', borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#d32f2f' }}>
                      Monthly Loss: ${summary.monthly_revenue_loss.toLocaleString()}
                    </Typography>
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Fade>

      {/* Urgent Action Required */}
      <Fade in timeout={2000}>
        <Card sx={{
          mb: 4,
          background: 'linear-gradient(135deg, #ff5722 0%, #d32f2f 100%)',
          color: 'white',
          boxShadow: '0 12px 40px rgba(255, 87, 34, 0.4)',
          borderRadius: 3,
          overflow: 'hidden',
          border: '3px solid #ff1744'
        }}>
          <CardContent sx={{ p: 4, textAlign: 'center' }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="h4" sx={{ fontWeight: 900, mb: 2, textShadow: '2px 2px 4px rgba(0,0,0,0.3)' }}>
                ⚠️ URGENT ACTION REQUIRED ⚠️
              </Typography>
              <Typography variant="h6" sx={{ opacity: 0.95, mb: 3 }}>
                Every minute you wait, competitors are stealing your leads
              </Typography>
            </Box>

            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} md={4}>
                <Paper sx={{ p: 3, bgcolor: 'rgba(255,255,255,0.95)', color: 'text.primary', borderRadius: 2 }}>
                  <ScheduleIcon sx={{ fontSize: 48, color: '#f44336', mb: 1 }} />
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#f44336' }}>
                    TIME IS MONEY
                  </Typography>
                  <Typography variant="body2">
                    You're losing <strong>${(summary.monthly_revenue_loss / 30).toFixed(0)}</strong> every single day
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} md={4}>
                <Paper sx={{ p: 3, bgcolor: 'rgba(255,255,255,0.95)', color: 'text.primary', borderRadius: 2 }}>
                  <PsychologyIcon sx={{ fontSize: 48, color: '#ff9800', mb: 1 }} />
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                    PSYCHOLOGY FACT
                  </Typography>
                  <Typography variant="body2">
                    78% of customers buy from the <strong>first responder</strong>
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} md={4}>
                <Paper sx={{ p: 3, bgcolor: 'rgba(255,255,255,0.95)', color: 'text.primary', borderRadius: 2 }}>
                  <FireIcon sx={{ fontSize: 48, color: '#d32f2f', mb: 1 }} />
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#d32f2f' }}>
                    COMPETITIVE THREAT
                  </Typography>
                  <Typography variant="body2">
                    Your competitors are <strong>already faster</strong> than you
                  </Typography>
                </Paper>
              </Grid>
            </Grid>

            <Box sx={{ mb: 4 }}>
              <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 2 }}>
                🎯 WE CAN FIX THIS IN 30 DAYS
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.95, maxWidth: 600, mx: 'auto' }}>
                Our proven lead response system has helped 500+ businesses transform their response times
                from hours to minutes, resulting in an average revenue increase of 340%.
              </Typography>
            </Box>

            <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} justifyContent="center" alignItems="center">
              <Button
                variant="contained"
                size="large"
                sx={{
                  bgcolor: '#4caf50',
                  color: 'white',
                  px: 6,
                  py: 2,
                  fontSize: '1.2rem',
                  fontWeight: 'bold',
                  borderRadius: 3,
                  boxShadow: '0 8px 24px rgba(76, 175, 80, 0.4)',
                  '&:hover': {
                    bgcolor: '#388e3c',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 12px 32px rgba(76, 175, 80, 0.6)'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                🚀 GET INSTANT SOLUTION
              </Button>
              <Button
                variant="outlined"
                size="large"
                sx={{
                  borderColor: 'white',
                  color: 'white',
                  px: 4,
                  py: 2,
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  borderRadius: 3,
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.1)',
                    borderColor: 'white'
                  }
                }}
              >
                📞 EMERGENCY CONSULTATION
              </Button>
            </Stack>

            <Typography variant="body2" sx={{ mt: 3, opacity: 0.8 }}>
              ⏰ Limited Time: Free implementation for the first 10 companies this month
            </Typography>
          </CardContent>
        </Card>
      </Fade>

      {/* Final CTA Footer */}
      <Paper sx={{
        p: 4,
        textAlign: 'center',
        bgcolor: '#f5f5f5',
        borderRadius: 3,
        border: '2px solid #1976d2'
      }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: '#1976d2' }}>
          📊 CONFIDENTIAL AUDIT REPORT
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          This comprehensive analysis was prepared exclusively for {summary.company_name} management.
          <br />
          Based on real lead submission and 48-hour response monitoring.
        </Typography>

        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center" alignItems="center">
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            sx={{
              borderColor: '#1976d2',
              color: '#1976d2',
              '&:hover': {
                bgcolor: 'rgba(25, 118, 210, 0.04)',
                borderColor: '#1976d2'
              }
            }}
          >
            Download PDF Report
          </Button>
          <Button
            variant="contained"
            sx={{
              bgcolor: '#1976d2',
              '&:hover': { bgcolor: '#1565c0' }
            }}
          >
            Share with Team
          </Button>
        </Stack>

        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 3 }}>
          © {new Date().getFullYear()} Lead Response Audit System | Confidential Business Intelligence
        </Typography>
      </Paper>
    </Container>
  );
};

// Arrow icon component
const ArrowIcon = () => (
  <Box sx={{ mx: 1, color: 'text.secondary' }}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z" fill="currentColor" />
    </svg>
  </Box>
);

export default AuditReportBalanced;
