from enum import Enum
from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class EventType(str, Enum):
    FORM_DISCOVERED = "form.discovered"
    FORM_ANALYZED = "form.analyzed"
    FORM_SUBMISSION_PENDING = "form.submission_pending"
    FORM_SUBMITTED = "form.submitted"
    FORM_SUBMISSION_FAILED = "form.submission_failed"
    LEAD_CREATED = "lead.created"
    LEAD_UPDATED = "lead.updated"
    RESPONSE_RECEIVED = "response.received"
    RESPONSE_ANALYZED = "response.analyzed"
    CONVERSATION_CREATED = "conversation.created"
    CONVERSATION_UPDATED = "conversation.updated"
    INSIGHT_GENERATED = "insight.generated"
    AI_MODEL_REQUESTED = "ai.model.requested"
    AI_MODEL_RESPONDED = "ai.model.responded"


class Event(BaseModel):
    event_id: str
    event_type: EventType
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    producer: str
    payload: Dict[str, Any]
    metadata: Dict[str, Any] = {}
    correlation_id: Optional[str] = None
    partition_key: Optional[str] = None


# Event Payload Models

class FormDiscoveredPayload(BaseModel):
    form_id: str
    url: str
    discovered_by: str
    screenshot_path: Optional[str] = None
    html_content: Optional[str] = None
    metadata: Dict[str, Any] = {}


class FormAnalyzedPayload(BaseModel):
    form_id: str
    url: str
    fields: Dict[str, Any]
    has_captcha: bool = False
    multi_step: bool = False
    submission_endpoint: Optional[str] = None
    method: str = "POST"
    success_indicators: Optional[Dict[str, Any]] = None
    error_indicators: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = {}


class FormSubmissionPendingPayload(BaseModel):
    form_id: str
    submission_id: str
    lead_id: str
    url: str
    data_to_submit: Dict[str, Any]
    metadata: Dict[str, Any] = {}


class FormSubmittedPayload(BaseModel):
    form_id: str
    submission_id: str
    lead_id: str
    url: str
    submitted_data: Dict[str, Any]
    success: bool
    response_html: Optional[str] = None
    screenshot_path: Optional[str] = None
    metadata: Dict[str, Any] = {}


class FormSubmissionFailedPayload(BaseModel):
    form_id: str
    submission_id: str
    lead_id: str
    url: str
    error_message: str
    error_type: str
    screenshot_path: Optional[str] = None
    metadata: Dict[str, Any] = {}


class LeadCreatedPayload(BaseModel):
    lead_id: str
    source: str
    lead_data: Dict[str, Any]
    metadata: Dict[str, Any] = {}


class LeadUpdatedPayload(BaseModel):
    lead_id: str
    updated_fields: Dict[str, Any]
    metadata: Dict[str, Any] = {}


class ResponseReceivedPayload(BaseModel):
    response_id: str
    lead_id: str
    submission_id: Optional[str] = None
    channel: str
    content: str
    sender_info: Dict[str, Any] = {}
    metadata: Dict[str, Any] = {}


class ResponseAnalyzedPayload(BaseModel):
    response_id: str
    lead_id: str
    sentiment_score: float
    quality_score: float
    analysis_results: Dict[str, Any]
    metadata: Dict[str, Any] = {}


class ConversationCreatedPayload(BaseModel):
    conversation_id: str
    lead_id: str
    initial_response_id: str
    channel: str
    metadata: Dict[str, Any] = {}


class ConversationUpdatedPayload(BaseModel):
    conversation_id: str
    lead_id: str
    new_response_id: str
    status: Optional[str] = None
    metadata: Dict[str, Any] = {}


class InsightGeneratedPayload(BaseModel):
    insight_id: str
    insight_type: str
    entity_id: Optional[str] = None
    entity_type: Optional[str] = None
    insight_data: Dict[str, Any]
    recommendations: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = {}


class AIModelRequestedPayload(BaseModel):
    request_id: str
    model_type: str
    input_data: Dict[str, Any]
    context: Dict[str, Any] = {}
    metadata: Dict[str, Any] = {}


class AIModelRespondedPayload(BaseModel):
    response_id: str
    request_id: str
    model_type: str
    output_data: Dict[str, Any]
    confidence: float
    processing_time: float
    metadata: Dict[str, Any] = {}
