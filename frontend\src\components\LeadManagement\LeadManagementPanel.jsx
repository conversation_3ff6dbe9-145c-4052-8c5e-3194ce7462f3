import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Button,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ThumbDown as ThumbDownIcon,
  ThumbUp as ThumbUpIcon,
  FilterList as FilterListIcon,
  Email as EmailIcon,
  Phone as PhoneIcon
} from '@mui/icons-material';
import supabaseService from '../../services/supabaseService';
import LeadDetailsDialog from './LeadDetailsDialog';

// Lead status options and colors
const LEAD_STATUS_OPTIONS = [
  { value: 'new', label: 'New', color: 'primary' },
  { value: 'contacted', label: 'Contacted', color: 'info' },
  { value: 'qualified', label: 'Qualified', color: 'success' },
  { value: 'not_interested', label: 'Not Interested', color: 'error' },
  { value: 'converted', label: 'Converted', color: 'secondary' },
  { value: 'nurturing', label: 'Nurturing', color: 'warning' }
];

/**
 * Lead Management Panel Component
 *
 * This component displays a list of leads with management capabilities.
 */
const LeadManagementPanel = () => {
  // State for leads data
  const [leads, setLeads] = useState([]);
  const [filteredLeads, setFilteredLeads] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // State for search
  const [searchTerm, setSearchTerm] = useState('');

  // State for lead details dialog
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedLeadId, setSelectedLeadId] = useState(null);

  // State for action menu
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null);
  const [actionMenuLeadId, setActionMenuLeadId] = useState(null);

  // Load leads on component mount
  useEffect(() => {
    fetchLeads();
  }, []);

  // Filter leads when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredLeads(leads);
    } else {
      const lowercasedSearch = searchTerm.toLowerCase();
      const filtered = leads.filter(lead =>
        (lead.first_name && lead.first_name.toLowerCase().includes(lowercasedSearch)) ||
        (lead.last_name && lead.last_name.toLowerCase().includes(lowercasedSearch)) ||
        (lead.name && lead.name.toLowerCase().includes(lowercasedSearch)) ||
        (lead.email && lead.email.toLowerCase().includes(lowercasedSearch)) ||
        (lead.phone && lead.phone.toLowerCase().includes(lowercasedSearch)) ||
        (lead.company && lead.company.toLowerCase().includes(lowercasedSearch))
      );
      setFilteredLeads(filtered);
    }
  }, [searchTerm, leads]);

  // Fetch leads from Supabase
  const fetchLeads = async () => {
    setLoading(true);
    setError(null);

    try {
      const { leads } = await supabaseService.getLeads();
      setLeads(leads);
      setFilteredLeads(leads);
    } catch (err) {
      console.error('Error fetching leads:', err);
      setError('Failed to load leads. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Open lead details dialog
  const handleOpenDetails = (leadId) => {
    setSelectedLeadId(leadId);
    setDetailsDialogOpen(true);
    setActionMenuAnchor(null);
  };

  // Close lead details dialog
  const handleCloseDetails = () => {
    setDetailsDialogOpen(false);
    setSelectedLeadId(null);
  };

  // Open action menu
  const handleOpenActionMenu = (event, leadId) => {
    setActionMenuAnchor(event.currentTarget);
    setActionMenuLeadId(leadId);
  };

  // Close action menu
  const handleCloseActionMenu = () => {
    setActionMenuAnchor(null);
    setActionMenuLeadId(null);
  };

  // Update lead status
  const handleUpdateStatus = async (leadId, status) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Get the status label for the success message
      const statusOption = LEAD_STATUS_OPTIONS.find(option => option.value === status);
      const statusLabel = statusOption ? statusOption.label : status;

      // Update the lead status
      const updatedLead = await supabaseService.updateLead(leadId, { status });

      if (!updatedLead) {
        throw new Error('Failed to update lead status. No response from server.');
      }

      // Update local state
      setLeads(prevLeads =>
        prevLeads.map(lead =>
          lead.id === leadId ? { ...lead, status } : lead
        )
      );

      setSuccess(`Lead marked as "${statusLabel}"`);
      handleCloseActionMenu();
    } catch (err) {
      console.error('Error updating lead status:', err);
      setError(`Failed to update lead status: ${err.message || 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle lead updated from details dialog
  const handleLeadUpdated = (updatedLead) => {
    setLeads(prevLeads =>
      prevLeads.map(lead =>
        lead.id === updatedLead.id ? { ...lead, ...updatedLead } : lead
      )
    );
  };

  // Get status chip color
  const getStatusColor = (status) => {
    const statusOption = LEAD_STATUS_OPTIONS.find(option => option.value === status);
    return statusOption ? statusOption.color : 'default';
  };

  // Get status label
  const getStatusLabel = (status) => {
    const statusOption = LEAD_STATUS_OPTIONS.find(option => option.value === status);
    return statusOption ? statusOption.label : status;
  };

  // Format name
  const formatName = (lead) => {
    if (lead.name) return lead.name;
    if (lead.first_name || lead.last_name) {
      return `${lead.first_name || ''} ${lead.last_name || ''}`.trim();
    }
    return 'Unknown';
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Lead Management</Typography>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchLeads}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            disabled={loading}
          >
            Filter
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      <Box sx={{ mb: 2 }}>
        <TextField
          fullWidth
          placeholder="Search leads by name, email, phone, or company..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          variant="outlined"
          size="small"
        />
      </Box>

      <Paper sx={{ width: '100%', mb: 2 }}>
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow sx={{ bgcolor: 'background.default' }}>
                <TableCell><Typography variant="subtitle2">Name</Typography></TableCell>
                <TableCell><Typography variant="subtitle2">Contact</Typography></TableCell>
                <TableCell><Typography variant="subtitle2">Company</Typography></TableCell>
                <TableCell><Typography variant="subtitle2">Status</Typography></TableCell>
                <TableCell><Typography variant="subtitle2">Created</Typography></TableCell>
                <TableCell align="right"><Typography variant="subtitle2">Actions</Typography></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading && leads.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    <CircularProgress size={30} />
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      Loading leads...
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : filteredLeads.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    <Typography variant="body2">
                      {searchTerm ? 'No leads match your search criteria.' : 'No leads found.'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredLeads
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((lead) => (
                    <TableRow
                      key={lead.id}
                      hover
                      onClick={() => handleOpenDetails(lead.id)}
                      sx={{ cursor: 'pointer' }}
                    >
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          {formatName(lead)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          {lead.email && (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <EmailIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary', fontSize: 14 }} />
                              <Typography variant="body2">{lead.email}</Typography>
                            </Box>
                          )}
                          {lead.phone && (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <PhoneIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary', fontSize: 14 }} />
                              <Typography variant="body2">{lead.phone}</Typography>
                            </Box>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{lead.company || '-'}</Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getStatusLabel(lead.status || 'new')}
                          color={getStatusColor(lead.status || 'new')}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(lead.created_at).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenActionMenu(e, lead.id);
                          }}
                        >
                          <MoreVertIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={filteredLeads.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      {/* Lead Details Dialog */}
      <LeadDetailsDialog
        open={detailsDialogOpen}
        leadId={selectedLeadId}
        onClose={handleCloseDetails}
        onLeadUpdated={handleLeadUpdated}
      />

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleCloseActionMenu}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={() => handleOpenDetails(actionMenuLeadId)}>
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>

        <MenuItem onClick={() => {
          handleOpenDetails(actionMenuLeadId);
          setTimeout(() => document.querySelector('[data-edit-button="true"]')?.click(), 100);
        }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Lead</ListItemText>
        </MenuItem>

        <Divider />

        <MenuItem onClick={() => handleUpdateStatus(actionMenuLeadId, 'not_interested')}>
          <ListItemIcon>
            <ThumbDownIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Mark as Not Interested</ListItemText>
        </MenuItem>

        <MenuItem onClick={() => handleUpdateStatus(actionMenuLeadId, 'qualified')}>
          <ListItemIcon>
            <ThumbUpIcon fontSize="small" color="success" />
          </ListItemIcon>
          <ListItemText>Mark as Qualified</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default LeadManagementPanel;
