import os
import uuid
from typing import List, Dict, Any, Optional
from fastapi import <PERSON><PERSON><PERSON>, BackgroundTasks, HTTPException, Depends, status, File, UploadFile
from pydantic import BaseModel, EmailStr
from datetime import datetime

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.auth import get_current_active_user, User
from libs.interfaces import (
    Response, 
    ResponseChannel,
    ResponseStatus,
    Event, 
    EventType, 
    ResponseReceivedPayload
)

# Import service-specific modules
from .response_service import ResponseService
from .event_producer import EventProducer

# Initialize FastAPI app
app = FastAPI(
    title="Response Ingestion Service",
    description="Service for ingesting responses from various channels",
    version="0.1.0"
)

# Initialize logger
logger = get_service_logger("response-ingestion")

# Initialize event producer
event_producer = EventProducer(
    bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092"),
    topic=os.getenv("RESPONSE_RECEIVED_TOPIC", "response.received")
)

# Initialize response service
response_service = ResponseService()


# Request/Response models
class EmailResponseRequest(BaseModel):
    lead_id: str
    from_email: EmailStr
    to_email: EmailStr
    subject: str
    body: str
    received_at: datetime
    headers: Dict[str, str] = {}
    attachments: List[str] = []


class SMSResponseRequest(BaseModel):
    lead_id: str
    from_number: str
    to_number: str
    body: str
    received_at: datetime
    metadata: Dict[str, Any] = {}


class VoiceResponseRequest(BaseModel):
    lead_id: str
    from_number: str
    to_number: str
    duration: int
    recording_url: Optional[str] = None
    transcript: Optional[str] = None
    received_at: datetime
    metadata: Dict[str, Any] = {}


class ResponseCreatedResponse(BaseModel):
    response_id: str
    message: str


# API endpoints
@app.post("/api/v1/responses/email", response_model=ResponseCreatedResponse)
async def ingest_email_response(
    request: EmailResponseRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """
    Ingest an email response.
    
    This endpoint ingests an email response and publishes an event.
    """
    logger.info(f"Ingesting email response for lead {request.lead_id}", props={
        "user": current_user.username,
        "from_email": request.from_email,
        "to_email": request.to_email
    })
    
    try:
        # Create response ID
        response_id = str(uuid.uuid4())
        
        # Create response data
        response_data = {
            "response_id": response_id,
            "lead_id": request.lead_id,
            "channel": ResponseChannel.EMAIL,
            "content": request.body,
            "received_at": request.received_at,
            "sender_info": {
                "email": request.from_email,
                "to_email": request.to_email,
                "subject": request.subject
            },
            "status": ResponseStatus.RECEIVED,
            "metadata": {
                "headers": request.headers,
                "attachments": request.attachments
            }
        }
        
        # Save response
        await response_service.create_response(response_data)
        
        # Publish event
        background_tasks.add_task(
            publish_response_received_event,
            response_id=response_id,
            lead_id=request.lead_id,
            channel=ResponseChannel.EMAIL,
            content=request.body,
            sender_info={
                "email": request.from_email,
                "to_email": request.to_email,
                "subject": request.subject
            },
            metadata={
                "headers": request.headers,
                "attachments": request.attachments
            }
        )
        
        return ResponseCreatedResponse(
            response_id=response_id,
            message="Email response ingested successfully"
        )
        
    except Exception as e:
        logger.error(f"Error ingesting email response: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error ingesting email response: {str(e)}"
        )


@app.post("/api/v1/responses/sms", response_model=ResponseCreatedResponse)
async def ingest_sms_response(
    request: SMSResponseRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """
    Ingest an SMS response.
    
    This endpoint ingests an SMS response and publishes an event.
    """
    logger.info(f"Ingesting SMS response for lead {request.lead_id}", props={
        "user": current_user.username,
        "from_number": request.from_number,
        "to_number": request.to_number
    })
    
    try:
        # Create response ID
        response_id = str(uuid.uuid4())
        
        # Create response data
        response_data = {
            "response_id": response_id,
            "lead_id": request.lead_id,
            "channel": ResponseChannel.SMS,
            "content": request.body,
            "received_at": request.received_at,
            "sender_info": {
                "phone": request.from_number,
                "to_phone": request.to_number
            },
            "status": ResponseStatus.RECEIVED,
            "metadata": request.metadata
        }
        
        # Save response
        await response_service.create_response(response_data)
        
        # Publish event
        background_tasks.add_task(
            publish_response_received_event,
            response_id=response_id,
            lead_id=request.lead_id,
            channel=ResponseChannel.SMS,
            content=request.body,
            sender_info={
                "phone": request.from_number,
                "to_phone": request.to_number
            },
            metadata=request.metadata
        )
        
        return ResponseCreatedResponse(
            response_id=response_id,
            message="SMS response ingested successfully"
        )
        
    except Exception as e:
        logger.error(f"Error ingesting SMS response: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error ingesting SMS response: {str(e)}"
        )


@app.post("/api/v1/responses/voice", response_model=ResponseCreatedResponse)
async def ingest_voice_response(
    request: VoiceResponseRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """
    Ingest a voice response.
    
    This endpoint ingests a voice response and publishes an event.
    """
    logger.info(f"Ingesting voice response for lead {request.lead_id}", props={
        "user": current_user.username,
        "from_number": request.from_number,
        "to_number": request.to_number,
        "duration": request.duration
    })
    
    try:
        # Create response ID
        response_id = str(uuid.uuid4())
        
        # Create response data
        response_data = {
            "response_id": response_id,
            "lead_id": request.lead_id,
            "channel": ResponseChannel.VOICE,
            "content": request.transcript or f"Voice call from {request.from_number} to {request.to_number}, duration: {request.duration} seconds",
            "received_at": request.received_at,
            "sender_info": {
                "phone": request.from_number,
                "to_phone": request.to_number,
                "duration": request.duration
            },
            "status": ResponseStatus.RECEIVED,
            "metadata": {
                "recording_url": request.recording_url,
                "transcript": request.transcript,
                **request.metadata
            }
        }
        
        # Save response
        await response_service.create_response(response_data)
        
        # Publish event
        background_tasks.add_task(
            publish_response_received_event,
            response_id=response_id,
            lead_id=request.lead_id,
            channel=ResponseChannel.VOICE,
            content=request.transcript or f"Voice call from {request.from_number} to {request.to_number}, duration: {request.duration} seconds",
            sender_info={
                "phone": request.from_number,
                "to_phone": request.to_number,
                "duration": request.duration
            },
            metadata={
                "recording_url": request.recording_url,
                "transcript": request.transcript,
                **request.metadata
            }
        )
        
        return ResponseCreatedResponse(
            response_id=response_id,
            message="Voice response ingested successfully"
        )
        
    except Exception as e:
        logger.error(f"Error ingesting voice response: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error ingesting voice response: {str(e)}"
        )


@app.get("/api/v1/responses", response_model=List[Response])
async def get_responses(
    current_user: User = Depends(get_current_active_user),
    lead_id: Optional[str] = None,
    channel: Optional[ResponseChannel] = None,
    skip: int = 0,
    limit: int = 100
):
    """
    Get a list of responses.
    
    This endpoint returns a list of responses with optional filtering.
    """
    logger.info(f"Getting responses", props={
        "user": current_user.username,
        "lead_id": lead_id,
        "channel": channel,
        "skip": skip,
        "limit": limit
    })
    
    try:
        # Get responses
        responses = await response_service.get_responses(
            lead_id=lead_id,
            channel=channel,
            skip=skip,
            limit=limit
        )
        
        return responses
        
    except Exception as e:
        logger.error(f"Error getting responses: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting responses: {str(e)}"
        )


@app.get("/api/v1/responses/{response_id}", response_model=Response)
async def get_response(
    response_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a response by ID.
    
    This endpoint returns a response by its ID.
    """
    logger.info(f"Getting response {response_id}", props={"user": current_user.username})
    
    try:
        # Get response
        response = await response_service.get_response(response_id)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Response {response_id} not found"
            )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting response: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting response: {str(e)}"
        )


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}


# Helper functions
async def publish_response_received_event(
    response_id: str,
    lead_id: str,
    channel: ResponseChannel,
    content: str,
    sender_info: Dict[str, Any],
    metadata: Dict[str, Any]
):
    """
    Publish a response received event.
    
    Args:
        response_id: Response ID
        lead_id: Lead ID
        channel: Response channel
        content: Response content
        sender_info: Sender information
        metadata: Additional metadata
    """
    logger.info(f"Publishing response received event for response {response_id}")
    
    try:
        # Create event
        event = Event(
            event_id=str(uuid.uuid4()),
            event_type=EventType.RESPONSE_RECEIVED,
            producer="response-ingestion-service",
            payload=ResponseReceivedPayload(
                response_id=response_id,
                lead_id=lead_id,
                channel=channel,
                content=content,
                sender_info=sender_info,
                metadata=metadata
            ).dict()
        )
        
        # Publish event
        await event_producer.produce_event(event)
        
        logger.info(f"Published response received event for response {response_id}")
        
    except Exception as e:
        logger.error(f"Error publishing response received event: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)
