variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "leadgen"
}

variable "aws_region" {
  description = "AWS region to deploy to"
  type        = string
  default     = "us-east-1"
}

variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "availability_zones" {
  description = "List of availability zones to use"
  type        = list(string)
  default     = ["us-east-1a", "us-east-1b", "us-east-1c"]
}

variable "db_username" {
  description = "Username for the PostgreSQL database"
  type        = string
  sensitive   = true
}

variable "db_password" {
  description = "Password for the PostgreSQL database"
  type        = string
  sensitive   = true
}

variable "docdb_username" {
  description = "Username for the DocumentDB database"
  type        = string
  sensitive   = true
}

variable "docdb_password" {
  description = "Password for the DocumentDB database"
  type        = string
  sensitive   = true
}
