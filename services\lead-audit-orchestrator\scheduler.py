"""
Lead Audit Scheduler

Manages automated scheduling and queuing for lead audit workflows:
- Automatic lead processing from Supabase
- Queue management with priorities
- 48-hour timer management
- Retry logic for failed operations
- Rate limiting to avoid overwhelming target websites
"""

import asyncio
import datetime
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import heapq
import logging

# Import service integration
from service_integrations import service_integration

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    LOW = 1
    NORMAL = 5
    HIGH = 8
    URGENT = 10

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"

@dataclass
class ScheduledTask:
    """Represents a scheduled task in the audit workflow"""
    task_id: str
    task_type: str  # enrichment, form_submission, monitoring, report_generation
    lead_id: str
    batch_id: str
    priority: TaskPriority
    scheduled_time: datetime.datetime
    max_retries: int = 3
    retry_count: int = 0
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime.datetime = None
    started_at: Optional[datetime.datetime] = None
    completed_at: Optional[datetime.datetime] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.datetime.utcnow()
        if self.metadata is None:
            self.metadata = {}

    def __lt__(self, other):
        # For priority queue - higher priority and earlier time come first
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value
        return self.scheduled_time < other.scheduled_time

class LeadAuditScheduler:
    """
    Scheduler for managing lead audit workflows with timing and queuing
    """

    def __init__(self, max_concurrent_tasks: int = 10, rate_limit_per_minute: int = 30):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.rate_limit_per_minute = rate_limit_per_minute

        # Task queues
        self.task_queue: List[ScheduledTask] = []  # Priority queue
        self.running_tasks: Dict[str, ScheduledTask] = {}
        self.completed_tasks: Dict[str, ScheduledTask] = {}

        # Rate limiting
        self.last_minute_tasks = []

        # Monitoring timers
        self.monitoring_timers: Dict[str, datetime.datetime] = {}

        # Scheduler state
        self.is_running = False
        self.scheduler_task = None

        logger.info(f"Scheduler initialized with max_concurrent={max_concurrent_tasks}, rate_limit={rate_limit_per_minute}/min")

    async def start(self):
        """Start the scheduler"""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return

        self.is_running = True
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
        logger.info("Lead audit scheduler started")

    async def stop(self):
        """Stop the scheduler"""
        self.is_running = False
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        logger.info("Lead audit scheduler stopped")

    def schedule_task(self, task: ScheduledTask):
        """Schedule a new task"""
        heapq.heappush(self.task_queue, task)
        logger.info(f"Scheduled task {task.task_id} ({task.task_type}) for {task.scheduled_time}")

    def schedule_lead_audit_workflow(self, lead_id: str, batch_id: str,
                                   priority: TaskPriority = TaskPriority.NORMAL,
                                   skip_enrichment: bool = False,
                                   monitoring_hours: int = 48) -> List[str]:
        """
        Schedule a complete lead audit workflow

        Returns:
            List of task IDs created
        """
        now = datetime.datetime.utcnow()
        task_ids = []

        # Task 1: Lead Enrichment (if not skipped)
        if not skip_enrichment:
            enrichment_task = ScheduledTask(
                task_id=f"{lead_id}_enrichment",
                task_type="enrichment",
                lead_id=lead_id,
                batch_id=batch_id,
                priority=priority,
                scheduled_time=now,
                metadata={"step": 1}
            )
            self.schedule_task(enrichment_task)
            task_ids.append(enrichment_task.task_id)

            # Schedule form submission after enrichment (5 minute buffer)
            form_submission_time = now + datetime.timedelta(minutes=5)
        else:
            form_submission_time = now

        # Task 2: Form Submission
        form_task = ScheduledTask(
            task_id=f"{lead_id}_form_submission",
            task_type="form_submission",
            lead_id=lead_id,
            batch_id=batch_id,
            priority=priority,
            scheduled_time=form_submission_time,
            metadata={"step": 2, "depends_on": task_ids[-1] if task_ids else None}
        )
        self.schedule_task(form_task)
        task_ids.append(form_task.task_id)

        # Task 3: Start Monitoring (immediately after form submission)
        monitoring_start_task = ScheduledTask(
            task_id=f"{lead_id}_monitoring_start",
            task_type="monitoring_start",
            lead_id=lead_id,
            batch_id=batch_id,
            priority=priority,
            scheduled_time=form_submission_time + datetime.timedelta(minutes=1),
            metadata={"step": 3, "depends_on": form_task.task_id, "monitoring_hours": monitoring_hours}
        )
        self.schedule_task(monitoring_start_task)
        task_ids.append(monitoring_start_task.task_id)

        # Task 4: End Monitoring and Generate Report (after monitoring period)
        monitoring_end_time = form_submission_time + datetime.timedelta(hours=monitoring_hours)
        report_task = ScheduledTask(
            task_id=f"{lead_id}_report_generation",
            task_type="report_generation",
            lead_id=lead_id,
            batch_id=batch_id,
            priority=priority,
            scheduled_time=monitoring_end_time,
            metadata={"step": 4, "depends_on": monitoring_start_task.task_id}
        )
        self.schedule_task(report_task)
        task_ids.append(report_task.task_id)

        # Set monitoring timer
        self.monitoring_timers[lead_id] = monitoring_end_time

        logger.info(f"Scheduled complete workflow for lead {lead_id} with {len(task_ids)} tasks")
        return task_ids

    def schedule_batch_audit(self, lead_ids: List[str], batch_id: str,
                           priority: TaskPriority = TaskPriority.NORMAL,
                           skip_enrichment: bool = False,
                           monitoring_hours: int = 48,
                           stagger_minutes: int = 2) -> Dict[str, List[str]]:
        """
        Schedule audit workflows for multiple leads with staggered timing

        Args:
            lead_ids: List of lead IDs to process
            batch_id: Batch identifier
            priority: Task priority
            skip_enrichment: Skip enrichment step
            monitoring_hours: Hours to monitor responses
            stagger_minutes: Minutes between each lead's workflow start

        Returns:
            Dict mapping lead_id to list of task_ids
        """
        batch_task_ids = {}

        for i, lead_id in enumerate(lead_ids):
            # Stagger the start times to avoid overwhelming target websites
            delay_minutes = i * stagger_minutes

            # Temporarily adjust scheduling for staggered start
            original_time = datetime.datetime.utcnow()

            task_ids = self.schedule_lead_audit_workflow(
                lead_id=lead_id,
                batch_id=batch_id,
                priority=priority,
                skip_enrichment=skip_enrichment,
                monitoring_hours=monitoring_hours
            )

            # Adjust all task times for this lead
            for task_id in task_ids:
                for task in self.task_queue:
                    if task.task_id == task_id:
                        task.scheduled_time += datetime.timedelta(minutes=delay_minutes)
                        break

            batch_task_ids[lead_id] = task_ids

        # Re-heapify the queue after time adjustments
        heapq.heapify(self.task_queue)

        logger.info(f"Scheduled batch audit for {len(lead_ids)} leads with {stagger_minutes}min stagger")
        return batch_task_ids

    async def _scheduler_loop(self):
        """Main scheduler loop"""
        while self.is_running:
            try:
                await self._process_pending_tasks()
                await self._check_monitoring_timers()
                await self._cleanup_completed_tasks()

                # Sleep for a short interval
                await asyncio.sleep(10)  # Check every 10 seconds

            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                await asyncio.sleep(30)  # Wait longer on error

    async def _process_pending_tasks(self):
        """Process pending tasks that are ready to run"""
        now = datetime.datetime.utcnow()

        # Check rate limiting
        if not self._can_start_new_task():
            return

        # Process tasks that are ready
        while (self.task_queue and
               len(self.running_tasks) < self.max_concurrent_tasks and
               self.task_queue[0].scheduled_time <= now):

            task = heapq.heappop(self.task_queue)

            # Check dependencies
            if not self._check_task_dependencies(task):
                # Re-schedule for later
                task.scheduled_time = now + datetime.timedelta(minutes=1)
                heapq.heappush(self.task_queue, task)
                continue

            # Start the task
            await self._start_task(task)

    def _can_start_new_task(self) -> bool:
        """Check if we can start a new task based on rate limiting"""
        now = datetime.datetime.utcnow()

        # Clean up old entries
        self.last_minute_tasks = [
            task_time for task_time in self.last_minute_tasks
            if now - task_time < datetime.timedelta(minutes=1)
        ]

        return len(self.last_minute_tasks) < self.rate_limit_per_minute

    def _check_task_dependencies(self, task: ScheduledTask) -> bool:
        """Check if task dependencies are satisfied"""
        depends_on = task.metadata.get("depends_on")
        if not depends_on:
            return True

        # Check if dependency is completed
        dependency_task = self.completed_tasks.get(depends_on)
        return dependency_task is not None and dependency_task.status == TaskStatus.COMPLETED

    async def _start_task(self, task: ScheduledTask):
        """Start executing a task"""
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.datetime.utcnow()
        self.running_tasks[task.task_id] = task
        self.last_minute_tasks.append(task.started_at)

        logger.info(f"Starting task {task.task_id} ({task.task_type}) for lead {task.lead_id}")

        # Execute the task in background
        asyncio.create_task(self._execute_task(task))

    async def _execute_task(self, task: ScheduledTask):
        """Execute a specific task"""
        try:
            # Route to appropriate handler based on task type
            if task.task_type == "enrichment":
                await self._execute_enrichment_task(task)
            elif task.task_type == "form_submission":
                await self._execute_form_submission_task(task)
            elif task.task_type == "monitoring_start":
                await self._execute_monitoring_start_task(task)
            elif task.task_type == "report_generation":
                await self._execute_report_generation_task(task)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")

            # Mark as completed
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.datetime.utcnow()

        except Exception as e:
            logger.error(f"Task {task.task_id} failed: {e}")
            task.error_message = str(e)

            # Handle retries
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.RETRYING

                # Re-schedule with exponential backoff
                retry_delay = 2 ** task.retry_count  # 2, 4, 8 minutes
                task.scheduled_time = datetime.datetime.utcnow() + datetime.timedelta(minutes=retry_delay)
                heapq.heappush(self.task_queue, task)

                logger.info(f"Retrying task {task.task_id} in {retry_delay} minutes (attempt {task.retry_count})")
            else:
                task.status = TaskStatus.FAILED
                logger.error(f"Task {task.task_id} failed permanently after {task.max_retries} retries")

        finally:
            # Move from running to completed
            if task.task_id in self.running_tasks:
                del self.running_tasks[task.task_id]
            self.completed_tasks[task.task_id] = task

    async def _execute_enrichment_task(self, task: ScheduledTask):
        """Execute lead enrichment task"""
        logger.info(f"Executing enrichment for lead {task.lead_id}")

        try:
            # Get lead data (in production, fetch from database)
            lead_data = task.metadata.get("lead_data", {})
            if not lead_data:
                # Mock lead data for now
                lead_data = {
                    "id": task.lead_id,
                    "first_name": "John",
                    "last_name": "Doe",
                    "email": f"{task.lead_id}@example.com",
                    "company": f"Company {task.lead_id}",
                    "enriched": False
                }

            # Call enrichment service
            enriched_data = await service_integration.enrich_lead(task.lead_id, lead_data)

            # Store enriched data in task metadata
            task.metadata["enriched_data"] = enriched_data

            logger.info(f"Enrichment completed for lead {task.lead_id}")

        except Exception as e:
            logger.error(f"Enrichment failed for lead {task.lead_id}: {e}")
            raise

    async def _execute_form_submission_task(self, task: ScheduledTask):
        """Execute form submission task"""
        logger.info(f"Executing form submission for lead {task.lead_id}")

        try:
            # Get lead data (enriched if available)
            lead_data = task.metadata.get("enriched_data") or task.metadata.get("lead_data", {})
            if not lead_data:
                # Mock lead data for now
                lead_data = {
                    "id": task.lead_id,
                    "first_name": "John",
                    "last_name": "Doe",
                    "email": f"{task.lead_id}@example.com",
                    "company": f"Company {task.lead_id}",
                    "website": f"https://company{task.lead_id}.com"
                }

            # Call form submission service
            submission_result = await service_integration.submit_form(task.lead_id, lead_data)

            # Store submission result in task metadata
            task.metadata["submission_result"] = submission_result
            task.metadata["form_submitted_at"] = datetime.datetime.utcnow().isoformat()

            if not submission_result.get("success"):
                raise Exception(f"Form submission failed: {submission_result.get('error')}")

            logger.info(f"Form submission completed for lead {task.lead_id}")

        except Exception as e:
            logger.error(f"Form submission failed for lead {task.lead_id}: {e}")
            raise

    async def _execute_monitoring_start_task(self, task: ScheduledTask):
        """Execute monitoring start task"""
        logger.info(f"Starting response monitoring for lead {task.lead_id}")

        try:
            # Get lead data
            lead_data = task.metadata.get("enriched_data") or task.metadata.get("lead_data", {})
            monitoring_hours = task.metadata.get("monitoring_hours", 48)

            # Start response monitoring
            monitoring_config = await service_integration.start_response_monitoring(
                task.lead_id, lead_data, monitoring_hours
            )

            # Store monitoring config in task metadata
            task.metadata["monitoring_config"] = monitoring_config

            logger.info(f"Response monitoring started for lead {task.lead_id} for {monitoring_hours} hours")

        except Exception as e:
            logger.error(f"Failed to start monitoring for lead {task.lead_id}: {e}")
            raise

    async def _execute_report_generation_task(self, task: ScheduledTask):
        """Execute report generation task"""
        logger.info(f"Generating audit report for lead {task.lead_id}")

        try:
            # Get lead data and check for responses
            lead_data = task.metadata.get("enriched_data") or task.metadata.get("lead_data", {})

            # Check for responses during monitoring period
            responses = await service_integration.check_responses(task.lead_id)

            # Check if lead should be disqualified (responded within 10 minutes)
            form_submitted_at_str = task.metadata.get("form_submitted_at")
            if form_submitted_at_str:
                form_submitted_at = datetime.datetime.fromisoformat(form_submitted_at_str)
                is_disqualified = await service_integration.check_early_disqualification(
                    task.lead_id, form_submitted_at
                )

                if is_disqualified:
                    task.metadata["disqualified"] = True
                    task.metadata["disqualification_reason"] = "Responded within 10 minutes via phone/SMS"
                    logger.info(f"Lead {task.lead_id} disqualified - quick response")
                    return

            # Generate audit report
            report = await service_integration.generate_audit_report(
                task.lead_id, lead_data, responses
            )

            # Store report in task metadata
            task.metadata["audit_report"] = report

            logger.info(f"Audit report generated for lead {task.lead_id} - Grade: {report.get('grade', 'N/A')}")

        except Exception as e:
            logger.error(f"Report generation failed for lead {task.lead_id}: {e}")
            raise

    async def _check_monitoring_timers(self):
        """Check for monitoring periods that have ended"""
        now = datetime.datetime.utcnow()

        expired_timers = []
        for lead_id, end_time in self.monitoring_timers.items():
            if now >= end_time:
                expired_timers.append(lead_id)

        for lead_id in expired_timers:
            logger.info(f"Monitoring period ended for lead {lead_id}")
            del self.monitoring_timers[lead_id]

            # TODO: Trigger final report generation if not already scheduled

    async def _cleanup_completed_tasks(self):
        """Clean up old completed tasks to prevent memory leaks"""
        now = datetime.datetime.utcnow()
        cutoff_time = now - datetime.timedelta(hours=24)  # Keep tasks for 24 hours

        expired_tasks = []
        for task_id, task in self.completed_tasks.items():
            if task.completed_at and task.completed_at < cutoff_time:
                expired_tasks.append(task_id)

        for task_id in expired_tasks:
            del self.completed_tasks[task_id]

        if expired_tasks:
            logger.info(f"Cleaned up {len(expired_tasks)} old completed tasks")

    def get_status(self) -> Dict[str, Any]:
        """Get scheduler status"""
        return {
            "is_running": self.is_running,
            "pending_tasks": len(self.task_queue),
            "running_tasks": len(self.running_tasks),
            "completed_tasks": len(self.completed_tasks),
            "monitoring_timers": len(self.monitoring_timers),
            "rate_limit_usage": len(self.last_minute_tasks),
            "rate_limit_max": self.rate_limit_per_minute
        }

    def get_lead_status(self, lead_id: str) -> Dict[str, Any]:
        """Get status for a specific lead"""
        lead_tasks = []

        # Check pending tasks
        for task in self.task_queue:
            if task.lead_id == lead_id:
                lead_tasks.append({
                    "task_id": task.task_id,
                    "task_type": task.task_type,
                    "status": task.status.value,
                    "scheduled_time": task.scheduled_time.isoformat(),
                    "retry_count": task.retry_count
                })

        # Check running tasks
        for task in self.running_tasks.values():
            if task.lead_id == lead_id:
                lead_tasks.append({
                    "task_id": task.task_id,
                    "task_type": task.task_type,
                    "status": task.status.value,
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "retry_count": task.retry_count
                })

        # Check completed tasks
        for task in self.completed_tasks.values():
            if task.lead_id == lead_id:
                lead_tasks.append({
                    "task_id": task.task_id,
                    "task_type": task.task_type,
                    "status": task.status.value,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "error_message": task.error_message,
                    "retry_count": task.retry_count
                })

        monitoring_end_time = self.monitoring_timers.get(lead_id)

        return {
            "lead_id": lead_id,
            "tasks": lead_tasks,
            "monitoring_end_time": monitoring_end_time.isoformat() if monitoring_end_time else None,
            "total_tasks": len(lead_tasks)
        }
