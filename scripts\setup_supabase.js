/**
 * Supabase Setup Script
 * 
 * This script executes the SQL to create the necessary tables in Supabase.
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL and service role key must be provided in .env file');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Read the SQL file
const sqlFilePath = path.join(__dirname, 'create_lead_tables.sql');
const sql = fs.readFileSync(sqlFilePath, 'utf8');

// Split the SQL into individual statements
const statements = sql
  .replace(/--.*$/gm, '') // Remove comments
  .split(';')
  .filter(statement => statement.trim() !== '');

// Execute each SQL statement
async function executeSQL() {
  console.log(`Found ${statements.length} SQL statements to execute`);
  
  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i].trim();
    if (!statement) continue;
    
    try {
      console.log(`Executing statement ${i + 1}/${statements.length}...`);
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        console.error(`Error executing statement ${i + 1}:`, error);
      } else {
        console.log(`Statement ${i + 1} executed successfully`);
      }
    } catch (error) {
      console.error(`Error executing statement ${i + 1}:`, error);
    }
  }
  
  console.log('SQL execution complete');
}

// First, create the exec_sql function in Supabase
async function createExecSQLFunction() {
  console.log('Creating exec_sql function in Supabase...');
  
  const createFunctionSQL = `
    CREATE OR REPLACE FUNCTION exec_sql(sql text)
    RETURNS void AS $$
    BEGIN
      EXECUTE sql;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
  `;
  
  try {
    // Execute raw SQL to create the function
    const { error } = await supabase.rpc('exec_sql', { sql: createFunctionSQL }).catch(() => {
      // If the function doesn't exist yet, we need to create it directly
      return supabase.from('_exec_sql').select('*').limit(1).then(() => {
        throw new Error('Need to create exec_sql function first');
      });
    });
    
    if (error) {
      console.error('Error creating exec_sql function:', error);
      console.log('You may need to create this function manually in the Supabase SQL editor:');
      console.log(createFunctionSQL);
      process.exit(1);
    }
    
    console.log('exec_sql function created successfully');
    return true;
  } catch (error) {
    console.error('Error creating exec_sql function:', error);
    console.log('Please run this SQL in the Supabase SQL editor first:');
    console.log(createFunctionSQL);
    process.exit(1);
  }
}

// Main function
async function main() {
  try {
    await createExecSQLFunction();
    await executeSQL();
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the script
main();
