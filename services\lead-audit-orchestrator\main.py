"""
Lead Audit Orchestrator Service

This service orchestrates the complete lead response audit workflow:
1. Lead enrichment (if needed)
2. Form discovery and submission
3. 48-hour response monitoring
4. Automatic report generation
5. Lead qualification filtering (disqualify <10min responders)
"""

import os
import uuid
import asyncio
import datetime
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, BackgroundTasks, HTTPException, status
from pydantic import BaseModel, Field
from enum import Enum

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.interfaces import Event, EventType

# Import scheduler
from scheduler import LeadAuditScheduler, TaskPriority

# Initialize FastAPI app
app = FastAPI(
    title="Lead Audit Orchestrator Service",
    description="Orchestrates the complete lead response audit workflow",
    version="1.0.0"
)

# Initialize logger
logger = get_service_logger("lead-audit-orchestrator")

# Pydantic models
class AuditStatus(str, Enum):
    PENDING = "pending"
    ENRICHING = "enriching"
    SUBMITTING = "submitting"
    MONITORING = "monitoring"
    COMPLETED = "completed"
    FAILED = "failed"
    DISQUALIFIED = "disqualified"  # Responded within 10 minutes

class LeadAuditRequest(BaseModel):
    lead_ids: List[str] = Field(..., description="List of lead IDs to audit")
    batch_name: Optional[str] = Field(None, description="Name for this audit batch")
    priority: int = Field(5, description="Priority level (1-10)")
    skip_enrichment: bool = Field(False, description="Skip lead enrichment step")
    monitoring_hours: int = Field(48, description="Hours to monitor for responses")

class LeadAuditResponse(BaseModel):
    batch_id: str
    status: str
    message: str
    leads_count: int
    estimated_completion: str

class AuditBatchStatus(BaseModel):
    batch_id: str
    status: AuditStatus
    leads_total: int
    leads_completed: int
    leads_failed: int
    leads_disqualified: int
    created_at: datetime.datetime
    estimated_completion: Optional[datetime.datetime]
    current_phase: str

# Global state management (in production, use Redis/database)
audit_batches: Dict[str, Dict[str, Any]] = {}
lead_audit_states: Dict[str, Dict[str, Any]] = {}

class LeadAuditOrchestrator:
    """Main orchestrator class for lead audit workflows"""

    def __init__(self):
        self.logger = get_service_logger("lead-audit-orchestrator")

    async def start_audit_batch(self, request: LeadAuditRequest) -> str:
        """
        Start a new audit batch for multiple leads

        Args:
            request: Audit request parameters

        Returns:
            Batch ID
        """
        batch_id = str(uuid.uuid4())

        # Initialize batch state
        batch_state = {
            "batch_id": batch_id,
            "status": AuditStatus.PENDING,
            "leads_total": len(request.lead_ids),
            "leads_completed": 0,
            "leads_failed": 0,
            "leads_disqualified": 0,
            "created_at": datetime.datetime.utcnow(),
            "request": request.dict(),
            "current_phase": "initializing"
        }

        audit_batches[batch_id] = batch_state

        self.logger.info(f"Started audit batch {batch_id} with {len(request.lead_ids)} leads")

        # Initialize individual lead states
        for lead_id in request.lead_ids:
            lead_audit_states[f"{batch_id}_{lead_id}"] = {
                "batch_id": batch_id,
                "lead_id": lead_id,
                "status": AuditStatus.PENDING,
                "current_step": "pending",
                "started_at": datetime.datetime.utcnow(),
                "steps_completed": [],
                "errors": [],
                "form_submitted_at": None,
                "monitoring_end_time": None,
                "responses": [],
                "final_score": None,
                "qualification_status": "pending"  # pending, qualified, disqualified
            }

        return batch_id

    async def process_lead_audit(self, batch_id: str, lead_id: str):
        """
        Process a single lead through the complete audit workflow

        Args:
            batch_id: Batch identifier
            lead_id: Lead identifier
        """
        state_key = f"{batch_id}_{lead_id}"
        lead_state = lead_audit_states.get(state_key)

        if not lead_state:
            self.logger.error(f"Lead state not found for {state_key}")
            return

        try:
            # Step 1: Lead Enrichment (if needed)
            if not audit_batches[batch_id]["request"]["skip_enrichment"]:
                await self._enrich_lead(lead_state)

            # Step 2: Form Discovery and Submission
            await self._submit_form(lead_state)

            # Step 3: Start Response Monitoring
            await self._start_monitoring(lead_state)

            # Step 4: Wait for monitoring period or early disqualification
            await self._monitor_responses(lead_state)

            # Step 5: Generate Report (if qualified)
            if lead_state["qualification_status"] != "disqualified":
                await self._generate_report(lead_state)

            # Update batch completion
            await self._update_batch_completion(batch_id)

        except Exception as e:
            self.logger.error(f"Error processing lead audit {state_key}: {str(e)}")
            lead_state["status"] = AuditStatus.FAILED
            lead_state["errors"].append(str(e))
            audit_batches[batch_id]["leads_failed"] += 1

    async def _enrich_lead(self, lead_state: Dict[str, Any]):
        """Enrich lead data if needed"""
        lead_state["current_step"] = "enriching"
        lead_state["status"] = AuditStatus.ENRICHING

        self.logger.info(f"Enriching lead {lead_state['lead_id']}")

        # TODO: Call lead enrichment service
        # For now, simulate enrichment
        await asyncio.sleep(2)

        lead_state["steps_completed"].append("enrichment")
        self.logger.info(f"Lead enrichment completed for {lead_state['lead_id']}")

    async def _submit_form(self, lead_state: Dict[str, Any]):
        """Submit form for the lead"""
        lead_state["current_step"] = "submitting"
        lead_state["status"] = AuditStatus.SUBMITTING

        self.logger.info(f"Submitting form for lead {lead_state['lead_id']}")

        # TODO: Call form discovery and submission service
        # For now, simulate form submission
        await asyncio.sleep(5)

        lead_state["form_submitted_at"] = datetime.datetime.utcnow()
        lead_state["steps_completed"].append("form_submission")
        self.logger.info(f"Form submitted for lead {lead_state['lead_id']}")

    async def _start_monitoring(self, lead_state: Dict[str, Any]):
        """Start response monitoring for the lead"""
        lead_state["current_step"] = "monitoring"
        lead_state["status"] = AuditStatus.MONITORING

        # Set monitoring end time
        batch_id = lead_state["batch_id"]
        monitoring_hours = audit_batches[batch_id]["request"]["monitoring_hours"]
        lead_state["monitoring_end_time"] = (
            lead_state["form_submitted_at"] + datetime.timedelta(hours=monitoring_hours)
        )

        self.logger.info(f"Started monitoring for lead {lead_state['lead_id']} until {lead_state['monitoring_end_time']}")

        # TODO: Call response monitoring service
        lead_state["steps_completed"].append("monitoring_started")

    async def _monitor_responses(self, lead_state: Dict[str, Any]):
        """Monitor responses and check for early disqualification"""
        self.logger.info(f"Monitoring responses for lead {lead_state['lead_id']}")

        monitoring_end = lead_state["monitoring_end_time"]
        form_submitted_at = lead_state["form_submitted_at"]

        # Simulate monitoring loop (in production, this would be event-driven)
        while datetime.datetime.utcnow() < monitoring_end:
            # Check for responses
            # TODO: Query response monitoring service for new responses

            # Simulate response check
            await asyncio.sleep(60)  # Check every minute

            # Check if we got a response within 10 minutes (disqualification criteria)
            if datetime.datetime.utcnow() - form_submitted_at < datetime.timedelta(minutes=10):
                # TODO: Check actual responses from monitoring service
                # For now, simulate random early response (10% chance)
                import random
                if random.random() < 0.1:  # 10% chance of early response
                    lead_state["qualification_status"] = "disqualified"
                    lead_state["status"] = AuditStatus.DISQUALIFIED
                    audit_batches[lead_state["batch_id"]]["leads_disqualified"] += 1
                    self.logger.info(f"Lead {lead_state['lead_id']} disqualified - responded within 10 minutes")
                    return

        # Monitoring period completed
        lead_state["qualification_status"] = "qualified"
        lead_state["steps_completed"].append("monitoring_completed")
        self.logger.info(f"Monitoring completed for lead {lead_state['lead_id']}")

    async def _generate_report(self, lead_state: Dict[str, Any]):
        """Generate audit report for the lead"""
        lead_state["current_step"] = "generating_report"

        self.logger.info(f"Generating report for lead {lead_state['lead_id']}")

        # TODO: Call analytics service to generate report
        # For now, simulate report generation
        await asyncio.sleep(3)

        # Calculate final score (mock)
        import random
        lead_state["final_score"] = random.randint(20, 95)

        lead_state["steps_completed"].append("report_generated")
        lead_state["status"] = AuditStatus.COMPLETED
        self.logger.info(f"Report generated for lead {lead_state['lead_id']}")

    async def _update_batch_completion(self, batch_id: str):
        """Update batch completion status"""
        batch_state = audit_batches[batch_id]

        # Count completed leads
        completed_count = 0
        for key, state in lead_audit_states.items():
            if (state["batch_id"] == batch_id and
                state["status"] in [AuditStatus.COMPLETED, AuditStatus.FAILED, AuditStatus.DISQUALIFIED]):
                completed_count += 1

        batch_state["leads_completed"] = completed_count

        # Check if batch is complete
        if completed_count >= batch_state["leads_total"]:
            batch_state["status"] = AuditStatus.COMPLETED
            batch_state["current_phase"] = "completed"
            self.logger.info(f"Batch {batch_id} completed")
        else:
            # Update current phase based on progress
            progress_pct = (completed_count / batch_state["leads_total"]) * 100
            if progress_pct < 25:
                batch_state["current_phase"] = "enrichment_and_submission"
            elif progress_pct < 75:
                batch_state["current_phase"] = "monitoring_responses"
            else:
                batch_state["current_phase"] = "generating_reports"

# Initialize orchestrator and scheduler
orchestrator = LeadAuditOrchestrator()
scheduler = LeadAuditScheduler(max_concurrent_tasks=20, rate_limit_per_minute=60)

@app.on_event("startup")
async def startup_event():
    """Start the scheduler when the service starts"""
    await scheduler.start()
    logger.info("Lead Audit Orchestrator Service started with scheduler")

@app.on_event("shutdown")
async def shutdown_event():
    """Stop the scheduler when the service shuts down"""
    await scheduler.stop()
    logger.info("Lead Audit Orchestrator Service stopped")

# API Endpoints
@app.post("/api/v1/audit/start", response_model=LeadAuditResponse)
async def start_lead_audit(
    request: LeadAuditRequest,
    background_tasks: BackgroundTasks
):
    """
    Start a new lead audit batch
    """
    try:
        batch_id = await orchestrator.start_audit_batch(request)

        # Process leads in background
        for lead_id in request.lead_ids:
            background_tasks.add_task(
                orchestrator.process_lead_audit,
                batch_id,
                lead_id
            )

        # Calculate estimated completion time
        estimated_hours = request.monitoring_hours + 2  # Add buffer for processing
        estimated_completion = datetime.datetime.utcnow() + datetime.timedelta(hours=estimated_hours)

        return LeadAuditResponse(
            batch_id=batch_id,
            status="started",
            message=f"Lead audit batch started with {len(request.lead_ids)} leads",
            leads_count=len(request.lead_ids),
            estimated_completion=estimated_completion.isoformat()
        )

    except Exception as e:
        logger.error(f"Error starting lead audit: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting lead audit: {str(e)}"
        )

@app.get("/api/v1/audit/status/{batch_id}", response_model=AuditBatchStatus)
async def get_audit_status(batch_id: str):
    """
    Get the status of an audit batch
    """
    batch_state = audit_batches.get(batch_id)

    if not batch_state:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Audit batch {batch_id} not found"
        )

    return AuditBatchStatus(**batch_state)

@app.get("/api/v1/audit/leads/{batch_id}")
async def get_lead_statuses(batch_id: str):
    """
    Get detailed status of all leads in a batch
    """
    if batch_id not in audit_batches:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Audit batch {batch_id} not found"
        )

    lead_statuses = []
    for key, state in lead_audit_states.items():
        if state["batch_id"] == batch_id:
            lead_statuses.append(state)

    return {
        "batch_id": batch_id,
        "leads": lead_statuses
    }

@app.post("/api/v1/audit/schedule-batch")
async def schedule_audit_batch(
    request: LeadAuditRequest
):
    """
    Schedule a batch audit using the scheduler (recommended approach)
    """
    try:
        batch_id = str(uuid.uuid4())

        # Convert priority
        priority_map = {1: TaskPriority.LOW, 5: TaskPriority.NORMAL, 8: TaskPriority.HIGH, 10: TaskPriority.URGENT}
        priority = priority_map.get(request.priority, TaskPriority.NORMAL)

        # Schedule the batch with the scheduler
        batch_task_ids = scheduler.schedule_batch_audit(
            lead_ids=request.lead_ids,
            batch_id=batch_id,
            priority=priority,
            skip_enrichment=request.skip_enrichment,
            monitoring_hours=request.monitoring_hours,
            stagger_minutes=2  # 2 minutes between each lead
        )

        logger.info(f"Scheduled batch audit {batch_id} with {len(request.lead_ids)} leads using scheduler")

        return {
            "batch_id": batch_id,
            "status": "scheduled",
            "message": f"Batch audit scheduled with {len(request.lead_ids)} leads",
            "leads_count": len(request.lead_ids),
            "task_ids": batch_task_ids,
            "estimated_completion": (datetime.datetime.utcnow() + datetime.timedelta(hours=request.monitoring_hours + 4)).isoformat()
        }

    except Exception as e:
        logger.error(f"Error scheduling batch audit: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error scheduling batch audit: {str(e)}"
        )

@app.get("/api/v1/scheduler/status")
async def get_scheduler_status():
    """Get scheduler status"""
    return scheduler.get_status()

@app.get("/api/v1/scheduler/lead/{lead_id}")
async def get_lead_scheduler_status(lead_id: str):
    """Get scheduler status for a specific lead"""
    return scheduler.get_lead_status(lead_id)

@app.post("/api/v1/audit/auto-process")
async def auto_process_leads(
    max_leads: int = 100,
    priority: int = 5,
    skip_enrichment: bool = False,
    monitoring_hours: int = 48
):
    """
    Automatically process leads from Supabase database
    """
    try:
        # TODO: Fetch leads from Supabase
        # For now, simulate with mock lead IDs
        mock_lead_ids = [f"lead_{i}" for i in range(min(max_leads, 10))]  # Limit to 10 for demo

        request = LeadAuditRequest(
            lead_ids=mock_lead_ids,
            batch_name=f"auto_batch_{datetime.datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            priority=priority,
            skip_enrichment=skip_enrichment,
            monitoring_hours=monitoring_hours
        )

        # Use the scheduled approach
        return await schedule_audit_batch(request)

    except Exception as e:
        logger.error(f"Error in auto-process: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error in auto-process: {str(e)}"
        )

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    scheduler_status = scheduler.get_status()
    return {
        "status": "healthy",
        "service": "lead-audit-orchestrator",
        "timestamp": datetime.datetime.utcnow().isoformat(),
        "active_batches": len(audit_batches),
        "active_leads": len(lead_audit_states),
        "scheduler": scheduler_status
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8008)
