import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API base URL
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

// Default request timeout
const DEFAULT_TIMEOUT = 30000; // 30 seconds

// API service class
class ApiService {
  private api: AxiosInstance;
  private token: string | null = null;

  constructor() {
    // Create axios instance
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: DEFAULT_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Add request interceptor for authentication
    this.api.interceptors.request.use(
      (config) => {
        // Add authorization header if token exists
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Add response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        // Handle authentication errors
        if (error.response && error.response.status === 401) {
          // Clear token and redirect to login
          this.clearToken();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Set authentication token
  public setToken(token: string): void {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  // Clear authentication token
  public clearToken(): void {
    this.token = null;
    localStorage.removeItem('auth_token');
  }

  // Load token from storage
  public loadToken(): void {
    const token = localStorage.getItem('auth_token');
    if (token) {
      this.token = token;
    }
  }

  // Generic request method
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.request(config);
      return response.data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // GET request
  public async get<T>(url: string, params?: any): Promise<T> {
    return this.request<T>({
      method: 'GET',
      url,
      params,
    });
  }

  // POST request
  public async post<T>(url: string, data?: any): Promise<T> {
    return this.request<T>({
      method: 'POST',
      url,
      data,
    });
  }

  // PUT request
  public async put<T>(url: string, data?: any): Promise<T> {
    return this.request<T>({
      method: 'PUT',
      url,
      data,
    });
  }

  // DELETE request
  public async delete<T>(url: string): Promise<T> {
    return this.request<T>({
      method: 'DELETE',
      url,
    });
  }

  // Authentication methods
  public async login(username: string, password: string): Promise<any> {
    const response = await this.post<{ access_token: string }>('/auth/token', {
      username,
      password,
    });
    
    if (response.access_token) {
      this.setToken(response.access_token);
    }
    
    return response;
  }

  public async logout(): Promise<void> {
    this.clearToken();
  }

  // Form Discovery methods
  public async discoverForms(data: {
    url: string;
    company_name?: string;
    industry?: string;
    priority?: number;
  }): Promise<any> {
    return this.post('/discover', data);
  }

  public async getDiscoveryStatus(jobId: string): Promise<any> {
    return this.get(`/discover/${jobId}`);
  }

  public async getDiscoveredForms(): Promise<any> {
    return this.get('/forms');
  }

  // Form Analysis methods
  public async analyzeForm(formId: string): Promise<any> {
    return this.post(`/forms/${formId}/analyze`);
  }

  public async getFormAnalysis(formId: string): Promise<any> {
    return this.get(`/forms/${formId}`);
  }

  // Lead Management methods
  public async getLeads(): Promise<any> {
    return this.get('/leads');
  }

  public async getLead(leadId: string): Promise<any> {
    return this.get(`/leads/${leadId}`);
  }

  // Response methods
  public async getResponses(leadId?: string): Promise<any> {
    return this.get('/responses', { lead_id: leadId });
  }

  // Conversation methods
  public async getConversations(): Promise<any> {
    return this.get('/conversations');
  }

  public async getConversation(conversationId: string): Promise<any> {
    return this.get(`/conversations/${conversationId}`);
  }

  // Analytics methods
  public async getMetrics(timeframe: string = 'week'): Promise<any> {
    return this.get('/analytics/metrics', { timeframe });
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
apiService.loadToken(); // Load token from storage on initialization

export default apiService;
