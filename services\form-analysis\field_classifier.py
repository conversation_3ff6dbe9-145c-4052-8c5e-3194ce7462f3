from typing import Dict, Any, List, Optional
import re

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.interfaces import FieldType

# Initialize logger
logger = get_service_logger("form-analysis", "field-classifier")


class FieldClassifier:
    """
    Classifier for form fields.
    """
    
    def __init__(self):
        """Initialize the field classifier."""
        self.logger = logger
        
        # Define patterns for field classification
        self.patterns = {
            FieldType.EMAIL: [
                r"email",
                r"e-mail",
                r"e_mail"
            ],
            FieldType.PHONE: [
                r"phone",
                r"mobile",
                r"cell",
                r"tel",
                r"telephone"
            ],
            FieldType.NAME: [
                r"name",
                r"first",
                r"last",
                r"full.?name",
                r"your.?name"
            ],
            FieldType.ADDRESS: [
                r"address",
                r"street",
                r"city",
                r"state",
                r"zip",
                r"postal",
                r"country"
            ],
            FieldType.COMPANY: [
                r"company",
                r"organization",
                r"organisation",
                r"business",
                r"employer"
            ],
            FieldType.MESSAGE: [
                r"message",
                r"comment",
                r"feedback",
                r"description",
                r"content",
                r"inquiry",
                r"enquiry"
            ]
        }
    
    def classify_field(
        self, 
        name: str, 
        id_attr: str, 
        html_type: str,
        label: Optional[str] = None,
        placeholder: Optional[str] = None,
        options: Optional[List[Dict[str, str]]] = None
    ) -> FieldType:
        """
        Classify a form field.
        
        Args:
            name: Name attribute of the field
            id_attr: ID attribute of the field
            html_type: HTML type of the field
            label: Label text for the field
            placeholder: Placeholder text for the field
            options: Options for select fields
            
        Returns:
            Field type classification
        """
        self.logger.info(f"Classifying field: {name}")
        
        # Convert to lowercase for case-insensitive matching
        name_lower = name.lower()
        id_lower = id_attr.lower()
        label_lower = label.lower() if label else ""
        placeholder_lower = placeholder.lower() if placeholder else ""
        
        # Check HTML type first
        if html_type == "email":
            return FieldType.EMAIL
        elif html_type == "tel":
            return FieldType.PHONE
        elif html_type == "checkbox":
            return FieldType.CHECKBOX
        elif html_type == "radio":
            return FieldType.RADIO
        elif html_type == "select":
            return FieldType.SELECT
        elif html_type == "date":
            return FieldType.DATE
        elif html_type == "file":
            return FieldType.FILE
        
        # Check for CAPTCHA
        if "captcha" in name_lower or "captcha" in id_lower or "captcha" in label_lower:
            return FieldType.CAPTCHA
        
        # Check patterns for other field types
        for field_type, patterns in self.patterns.items():
            for pattern in patterns:
                if (re.search(pattern, name_lower) or 
                    re.search(pattern, id_lower) or 
                    (label and re.search(pattern, label_lower)) or 
                    (placeholder and re.search(pattern, placeholder_lower))):
                    return field_type
        
        # Default to text if no match found
        return FieldType.TEXT
