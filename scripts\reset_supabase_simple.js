/**
 * Simple Supabase Database Reset Script
 * 
 * This script uses direct HTTP requests to the Supabase REST API
 * to delete all records from tables.
 */

require('dotenv').config();
const https = require('https');

// Get Supabase credentials from environment variables
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('Error: Supabase URL and key must be provided in .env file');
  console.error('Make sure you have SUPABASE_URL and SUPABASE_KEY or SUPABASE_SERVICE_ROLE_KEY defined');
  process.exit(1);
}

// Common tables in Supabase projects
const commonTables = [
  'leads',
  'users',
  'profiles',
  'form_submissions',
  'websites',
  'companies',
  'contacts',
  'jobs',
  'tasks',
  'settings',
  'logs',
  'analytics',
  'social_media',
  'pixels',
  'campaigns'
];

/**
 * Make a request to the Supabase REST API
 */
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, SUPABASE_URL);
    
    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'apikey': SUPABASE_KEY,
        'Authorization': `Bearer ${SUPABASE_KEY}`
      }
    };
    
    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            const parsedData = responseData ? JSON.parse(responseData) : {};
            resolve(parsedData);
          } catch (e) {
            resolve(responseData);
          }
        } else {
          reject(new Error(`Request failed with status code ${res.statusCode}: ${responseData}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * Delete all records from a table
 */
async function clearTable(tableName) {
  try {
    console.log(`Attempting to delete all records from table: ${tableName}`);
    
    // Use the DELETE method with no filters to delete all records
    await makeRequest('DELETE', `/rest/v1/${tableName}?`, null);
    
    console.log(`✓ Successfully cleared table: ${tableName}`);
    return true;
  } catch (error) {
    console.error(`Error clearing table ${tableName}:`, error.message);
    return false;
  }
}

/**
 * Reset the database by clearing all common tables
 */
async function resetDatabase() {
  console.log('Starting database reset...');
  console.log(`Will attempt to clear these common tables: ${commonTables.join(', ')}`);
  console.log('Starting in 3 seconds...');
  
  // Give a brief pause before proceeding
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Try to clear each table
  for (const table of commonTables) {
    await clearTable(table);
  }
  
  console.log('Database reset complete!');
}

// Run the script
resetDatabase();
