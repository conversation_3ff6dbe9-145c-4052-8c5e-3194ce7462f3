#!/usr/bin/env python
"""
Form Submission Engine
---------------------
Engine for submitting lead forms across websites.
Integrates with LangGraph for AI-powered form filling.
"""
import asyncio
import datetime
import os
import time
import uuid
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup
import aiohttp
import json
import re

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger

# Initialize logger
logger = get_service_logger("form-submission", "form-submission-engine")

# Import LangGraph components
from langgraph.graph import StateGraph, START, END
from langchain_openai import ChatOpenAI

# Define state schema
class LeadFormState(dict):
    """State for the lead form workflow."""
    pass

class FormSubmissionEngine:
    """
    Engine for submitting lead forms across websites.
    """
    
    def __init__(self):
        """Initialize the form submission engine."""
        self.logger = logger
        self.results = {}  # In-memory storage for results (would use a database in production)
        
        # Initialize LangGraph
        self._initialize_langgraph()
    
    def _initialize_langgraph(self):
        """Initialize the LangGraph workflow."""
        # Define the workflow graph
        graph = StateGraph(LeadFormState)
        
        # Add nodes to the workflow
        graph.add_node("llm_reasoning", self._llm_reasoning_node)
        graph.add_node("should_retry", self._should_retry_node)
        graph.add_node("llm_retry", self._llm_retry_node)
        graph.add_node("fill_form", self._fill_form_node)
        
        # Define the edges for the workflow
        graph.add_edge(START, "llm_reasoning")
        graph.add_edge("llm_reasoning", "should_retry")
        
        # Branch based on should_retry decision
        graph.add_conditional_edges(
            "should_retry",
            lambda state: state.get("should_retry", False),
            {
                True: "llm_retry",
                False: "fill_form"
            }
        )
        
        # From llm_retry back to fill_form
        graph.add_edge("llm_retry", "fill_form")
        
        # Complete the workflow
        graph.add_edge("fill_form", END)
        
        # Compile the graph
        self.workflow = graph.compile()
    
    async def _llm_reasoning_node(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Use LLM to decide how to fill the form or what strategy to use.
        
        Args:
            state: Current state
            
        Returns:
            Updated state
        """
        self.logger.info(f"LLM reasoning for form filling", props={
            "url": state.get("url")
        })
        
        # Extract domain from URL for domain-specific logging
        from urllib.parse import urlparse
        parsed_url = urlparse(state['url'])
        domain = parsed_url.netloc
        state_update = {"domain": domain}
        
        # Initialize retry count if not present
        if 'retry_count' not in state or state['retry_count'] is None:
            state_update["retry_count"] = 0
        
        try:
            # In a real implementation, this would use an LLM
            # For now, we'll simulate LLM reasoning
            
            # Simulate processing time
            await asyncio.sleep(1)
            
            # Extract critical vs optional fields with confidence scores
            critical_fields = {}
            optional_fields = {}
            confidence_score = 0.85  # Default confidence score
            
            # Process important fields as critical
            for field, value in state['lead_data'].items():
                if field in ['name', 'email', 'phone']:
                    critical_fields[field] = f"Required for contact: {value}"
                else:
                    optional_fields[field] = f"Optional information: {value}"
            
            # More critical fields = higher confidence
            confidence_score = min(0.95, 0.75 + (len(critical_fields) * 0.05))
            
            # Use the validated JSON string with additional metadata
            result = {
                **state,
                "llm_decision": json.dumps({
                    "important_fields": critical_fields,
                    "optional_fields": optional_fields
                }),
                "critical_fields": critical_fields,
                "optional_fields": optional_fields,
                "confidence_score": confidence_score
            }
            
            return result
            
        except Exception as e:
            fallback_json = {
                "important_fields": {
                    "name": "Primary contact information",
                    "email": "Primary contact method",
                    "phone": "Alternative contact method",
                    "message": "Details about the request"
                }
            }
            
            # Return error and minimum data needed for retry logic
            result = {
                **state,
                "llm_decision": json.dumps(fallback_json),
                "critical_fields": {
                    "name": "Primary contact information",
                    "email": "Primary contact method"
                },
                "optional_fields": {
                    "phone": "Alternative contact method",
                    "message": "Details about the request"
                },
                "confidence_score": 0.3,  # Very low confidence
                "error": f"LLM error: {e}",
                "retry_count": state.get("retry_count", 0) + 1  # Increment retry count
            }
            
            return result
    
    async def _should_retry_node(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate if retry is needed.
        
        Args:
            state: Current state
            
        Returns:
            Updated state with should_retry flag
        """
        retry_count = state.get("retry_count", 0)
        should_retry = bool(state.get("error") and retry_count < 3)
        
        return {**state, "should_retry": should_retry}
    
    async def _llm_retry_node(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retry the LLM with a different approach if the first attempt failed.
        
        Args:
            state: Current state
            
        Returns:
            Updated state
        """
        # Increment retry count
        current_retry = state.get("retry_count", 0) + 1
        
        try:
            # In a real implementation, this would use an LLM with a different prompt
            # For now, we'll simulate LLM retry
            
            # Simulate processing time
            await asyncio.sleep(1)
            
            # Extract fields with simplified approach
            critical_fields = {}
            optional_fields = {}
            
            # Simplified approach - just use the lead data directly
            for field, value in state['lead_data'].items():
                if field in ['name', 'email']:
                    critical_fields[field] = f"Required: {value}"
                else:
                    optional_fields[field] = f"Optional: {value}"
            
            result = {
                **state,
                "llm_decision": json.dumps({
                    "important_fields": critical_fields,
                    "optional_fields": optional_fields
                }),
                "critical_fields": critical_fields,
                "optional_fields": optional_fields,
                "confidence_score": 0.7,  # Moderate confidence for retry
                "retry_count": current_retry
            }
            
            return result
            
        except Exception as e:
            # Last-resort fallback
            return {
                **state,
                "llm_decision": '{"important_fields":{"name":"contact","email":"contact"}}',
                "critical_fields": {"name": "contact", "email": "contact"},
                "confidence_score": 0.3,
                "error": f"LLM retry error: {e}",
                "retry_count": current_retry
            }
    
    async def _fill_form_node(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fill and submit the lead form.
        
        Args:
            state: Current state
            
        Returns:
            Updated state with submission results
        """
        self.logger.info(f"Filling form", props={
            "url": state.get("url"),
            "confidence_score": state.get("confidence_score")
        })
        
        try:
            url = state['url']
            lead_data = state['lead_data']
            
            # In a real implementation, this would use a headless browser to fill and submit the form
            # For now, we'll simulate form filling and submission
            
            # Simulate processing time
            await asyncio.sleep(2)
            
            # Simulate form filling and submission
            form_id = state.get("form_id", f"form_{uuid.uuid4()}")
            fields_filled = len(state.get("critical_fields", {})) + len(state.get("optional_fields", {}))
            submitted = True
            
            # Create submission details
            submission_details = {
                "form_id": form_id,
                "url": url,
                "fields_filled": fields_filled,
                "submitted": submitted,
                "timestamp": datetime.datetime.now().isoformat(),
                "metadata": {
                    "confidence_score": state.get("confidence_score", 0),
                    "critical_fields": list(state.get("critical_fields", {}).keys()),
                    "optional_fields": list(state.get("optional_fields", {}).keys())
                }
            }
            
            return {
                **state,
                "fill_result": "success",
                "submission_status": "submitted",
                "submission_details": submission_details
            }
            
        except Exception as e:
            self.logger.error(f"Error filling form: {str(e)}")
            
            return {
                **state,
                "fill_result": "error",
                "error": f"Error filling form: {str(e)}"
            }
    
    async def submit_form(
        self,
        url: str,
        form_id: Optional[str] = None,
        lead_data: Dict[str, str] = {},
        timeout: int = 60,
        retry_count: int = 3,
        use_ai_reasoning: bool = True
    ) -> Dict[str, Any]:
        """
        Submit a lead form.
        
        Args:
            url: URL to submit form on
            form_id: Form ID to submit (optional)
            lead_data: Lead data to submit
            timeout: Timeout in seconds
            retry_count: Number of retry attempts
            use_ai_reasoning: Whether to use AI reasoning for form filling
            
        Returns:
            Submission result
        """
        self.logger.info(f"Submitting form on {url}", props={
            "form_id": form_id,
            "use_ai_reasoning": use_ai_reasoning
        })
        
        try:
            # Prepare initial state
            initial_state = {
                "url": url,
                "form_id": form_id,
                "lead_data": lead_data,
                "llm_decision": None,
                "fill_result": None,
                "error": None,
                "retry_count": 0
            }
            
            if use_ai_reasoning:
                # Run the LangGraph workflow
                result_state = await self.workflow.ainvoke(initial_state)
            else:
                # Skip AI reasoning and directly fill the form
                result_state = await self._fill_form_node(initial_state)
            
            if result_state.get("error"):
                self.logger.error(f"Error submitting form: {result_state['error']}")
                return {
                    "submitted": False,
                    "error": result_state['error'],
                    "form_id": form_id or "unknown"
                }
            else:
                submission_details = result_state.get("submission_details", {})
                return {
                    "submitted": submission_details.get("submitted", False),
                    "form_id": submission_details.get("form_id", form_id or "unknown"),
                    "fields_filled": submission_details.get("fields_filled", 0),
                    "metadata": submission_details.get("metadata", {})
                }
                
        except Exception as e:
            self.logger.error(f"Error submitting form: {str(e)}")
            return {
                "submitted": False,
                "error": str(e),
                "form_id": form_id or "unknown"
            }
    
    async def store_submission_result(self, job_id: str, result: Dict[str, Any]) -> None:
        """
        Store a form submission result.
        
        Args:
            job_id: Job ID
            result: Submission result
        """
        # In a real implementation, this would store the result in a database
        # For now, we'll store it in memory
        self.results[job_id] = result
    
    async def get_submission_result(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a form submission result.
        
        Args:
            job_id: Job ID
            
        Returns:
            Submission result or None if not found
        """
        # In a real implementation, this would retrieve the result from a database
        # For now, we'll retrieve it from memory
        return self.results.get(job_id)
