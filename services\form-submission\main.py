#!/usr/bin/env python
"""
Form Submission Service
----------------------
Microservice for submitting lead forms across websites.
Part of the Unified AI-Powered Lead Generation System.
"""
import asyncio
import datetime
import os
import time
import uuid
from typing import Dict, List, Any, Optional

from fastapi import FastAPI, BackgroundTasks, HTTPException, Depends, status
from pydantic import BaseModel, HttpUrl, Field

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.auth import get_current_active_user, User
from libs.interfaces import Event, EventType, FormSubmittedPayload

# Import service-specific modules
from .form_submission import FormSubmissionEngine
from .event_producer import EventProducer

# Initialize FastAPI app
app = FastAPI(
    title="Form Submission Service",
    description="Service for submitting lead forms across websites",
    version="0.1.0"
)

# Initialize logger
logger = get_service_logger("form-submission")

# Initialize event producer
event_producer = EventProducer(
    bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092"),
    topic=os.getenv("FORM_SUBMITTED_TOPIC", "form.submitted")
)

# Initialize form submission engine
submission_engine = FormSubmissionEngine()

# Request/Response models
class SubmitFormRequest(BaseModel):
    url: HttpUrl
    form_id: Optional[str] = None
    lead_data: Dict[str, str]
    timeout: int = Field(default=60, ge=10, le=300)
    retry_count: int = Field(default=3, ge=1, le=5)
    use_ai_reasoning: bool = True

class SubmitFormResponse(BaseModel):
    job_id: str
    message: str
    status: str

class FormSubmissionResult(BaseModel):
    job_id: str
    url: str
    form_id: str
    fields_filled: int
    submitted: bool
    status: str
    error: Optional[str] = None
    processing_time: float
    submission_timestamp: Optional[datetime.datetime] = None
    metadata: Dict[str, Any] = {}

# API endpoints
@app.post("/api/v1/submit", response_model=SubmitFormResponse)
async def submit_form(
    request: SubmitFormRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """
    Submit a lead form.
    
    This endpoint submits lead data to a form on the specified URL.
    """
    logger.info(f"Submitting form on {request.url}", props={
        "user": current_user.username,
        "url": str(request.url),
        "form_id": request.form_id
    })
    
    try:
        # Create job ID
        job_id = str(uuid.uuid4())
        
        # Process form submission in background
        background_tasks.add_task(
            process_form_submission,
            job_id=job_id,
            url=str(request.url),
            form_id=request.form_id,
            lead_data=request.lead_data,
            timeout=request.timeout,
            retry_count=request.retry_count,
            use_ai_reasoning=request.use_ai_reasoning
        )
        
        return SubmitFormResponse(
            job_id=job_id,
            message=f"Form submission initiated for {request.url}",
            status="processing"
        )
        
    except Exception as e:
        logger.error(f"Error initiating form submission: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error initiating form submission: {str(e)}"
        )

@app.get("/api/v1/submit/{job_id}", response_model=FormSubmissionResult)
async def get_submission_result(
    job_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get the result of a form submission job.
    
    This endpoint returns the result of a form submission job by its ID.
    """
    logger.info(f"Getting form submission result for job {job_id}", props={"user": current_user.username})
    
    try:
        # Get form submission result
        result = await submission_engine.get_submission_result(job_id)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Form submission result for job {job_id} not found"
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting form submission result: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting form submission result: {str(e)}"
        )

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

# Background processing function
async def process_form_submission(
    job_id: str, 
    url: str, 
    form_id: Optional[str], 
    lead_data: Dict[str, str], 
    timeout: int,
    retry_count: int,
    use_ai_reasoning: bool
):
    """
    Process a form submission job.
    
    Args:
        job_id: Job ID
        url: URL to submit form on
        form_id: Form ID to submit (optional)
        lead_data: Lead data to submit
        timeout: Timeout in seconds
        retry_count: Number of retry attempts
        use_ai_reasoning: Whether to use AI reasoning for form filling
    """
    logger.info(f"Processing form submission job {job_id}", props={
        "url": url,
        "form_id": form_id
    })
    
    start_time = time.time()
    
    try:
        # Submit form
        submission_result = await submission_engine.submit_form(
            url=url,
            form_id=form_id,
            lead_data=lead_data,
            timeout=timeout,
            retry_count=retry_count,
            use_ai_reasoning=use_ai_reasoning
        )
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Create result
        result = FormSubmissionResult(
            job_id=job_id,
            url=url,
            form_id=submission_result.get("form_id", "unknown"),
            fields_filled=submission_result.get("fields_filled", 0),
            submitted=submission_result.get("submitted", False),
            status="completed" if submission_result.get("submitted", False) else "failed",
            error=submission_result.get("error"),
            processing_time=processing_time,
            submission_timestamp=datetime.datetime.now(),
            metadata=submission_result.get("metadata", {})
        )
        
        # Store result
        await submission_engine.store_submission_result(job_id, result)
        
        # Publish form submitted event if successful
        if submission_result.get("submitted", False):
            event = Event(
                event_id=str(uuid.uuid4()),
                event_type=EventType.FORM_SUBMITTED,
                producer="form-submission-service",
                payload=FormSubmittedPayload(
                    job_id=job_id,
                    url=url,
                    form_id=submission_result.get("form_id", "unknown"),
                    lead_data=lead_data,
                    fields_filled=submission_result.get("fields_filled", 0),
                    metadata=submission_result.get("metadata", {})
                ).dict()
            )
            
            await event_producer.produce_event(event)
        
        logger.info(f"Completed form submission job {job_id}", props={
            "url": url,
            "form_id": submission_result.get("form_id", "unknown"),
            "submitted": submission_result.get("submitted", False),
            "processing_time": processing_time
        })
        
    except Exception as e:
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Create error result
        result = FormSubmissionResult(
            job_id=job_id,
            url=url,
            form_id=form_id or "unknown",
            fields_filled=0,
            submitted=False,
            status="failed",
            error=str(e),
            processing_time=processing_time,
            submission_timestamp=datetime.datetime.now(),
            metadata={}
        )
        
        # Store result
        await submission_engine.store_submission_result(job_id, result)
        
        logger.error(f"Error processing form submission job {job_id}: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
