# Supabase Database Reset Scripts

These scripts help you reset your Supabase database by deleting all records and optionally dropping all tables.

## Prerequisites

1. Node.js installed
2. Supabase project with appropriate credentials
3. `.env` file with Supabase credentials

## Setup

1. Make sure your `.env` file contains the following variables:
   ```
   SUPABASE_URL=https://your-project-id.supabase.co
   SUPABASE_KEY=your-service-role-key
   ```

   Note: For this operation, you need to use the **service role key** (from Project Settings > API), not the anon key, as it requires elevated permissions.

2. Install dependencies:
   ```
   bash install_deps.sh
   ```
   or manually:
   ```
   npm install @supabase/supabase-js dotenv
   ```

3. (Optional) Execute the SQL functions in Supabase:
   - Go to your Supabase dashboard
   - Navigate to the SQL Editor
   - Copy and paste the contents of `supabase_functions.sql`
   - Run the SQL to create the helper functions

## Usage

Run the reset script:

```
node reset_supabase.js
```

The script will:
1. Connect to your Supabase database
2. List all tables in the public schema
3. Ask for confirmation before proceeding
4. Delete all records from each table
5. Optionally drop all tables (if confirmed)

## Warning

**This is a destructive operation that cannot be undone.** Make sure you have backups of any important data before running this script.

## Troubleshooting

If you encounter permission errors, make sure:
1. You're using the service role key, not the anon key
2. You've executed the SQL functions in the Supabase SQL editor
3. Your database user has the necessary permissions
