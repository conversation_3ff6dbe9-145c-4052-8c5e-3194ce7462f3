from typing import List, Dict, Any, Optional
import uuid
from datetime import datetime
import asyncio
import os
import json
from sqlalchemy import create_engine, Column, String, Boolean, DateTime, JSON, Table, MetaData, select, insert, update, delete, and_
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.interfaces import Conversation, ResponseChannel

# Initialize logger
logger = get_service_logger("conversation-management", "conversation-service")

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@postgres:5432/leadgen")

# Create async engine
engine = create_async_engine(DATABASE_URL)
async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

# Define metadata
metadata = MetaData()

# Define conversations table
conversations_table = Table(
    "conversations",
    metadata,
    Column("conversation_id", String, primary_key=True),
    Column("lead_id", String, nullable=False),
    Column("responses", JSON, default=list),
    Column("started_at", DateTime, default=datetime.utcnow),
    Column("last_updated", DateTime, default=datetime.utcnow),
    Column("status", String, nullable=False),
    Column("channel", String, nullable=False),
    Column("metadata", JSON, default=dict),
    Column("tags", JSON, default=list),
    Column("assigned_to", String, nullable=True)
)


class ConversationService:
    """
    Service for managing conversations.
    """
    
    def __init__(self):
        """Initialize the conversation service."""
        self.logger = logger
    
    async def create_tables(self):
        """Create database tables if they don't exist."""
        async with engine.begin() as conn:
            await conn.run_sync(metadata.create_all)
    
    async def create_conversation(self, conversation_data: Dict[str, Any]) -> str:
        """
        Create a new conversation.
        
        Args:
            conversation_data: Conversation data
            
        Returns:
            Conversation ID
        """
        self.logger.info(f"Creating conversation for lead {conversation_data.get('lead_id')}")
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Insert conversation into database
        async with async_session() as session:
            async with session.begin():
                await session.execute(
                    insert(conversations_table).values(**conversation_data)
                )
        
        self.logger.info(f"Created conversation {conversation_data.get('conversation_id')}")
        
        return conversation_data.get('conversation_id')
    
    async def get_conversations(
        self, 
        lead_id: Optional[str] = None,
        channel: Optional[ResponseChannel] = None,
        status: Optional[str] = None,
        assigned_to: Optional[str] = None,
        skip: int = 0, 
        limit: int = 100
    ) -> List[Conversation]:
        """
        Get a list of conversations.
        
        Args:
            lead_id: Filter by lead ID
            channel: Filter by channel
            status: Filter by status
            assigned_to: Filter by assigned user
            skip: Number of conversations to skip
            limit: Maximum number of conversations to return
            
        Returns:
            List of conversations
        """
        self.logger.info(f"Getting conversations", props={
            "lead_id": lead_id,
            "channel": channel,
            "status": status,
            "assigned_to": assigned_to,
            "skip": skip,
            "limit": limit
        })
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Build query
        query = select(conversations_table)
        
        # Apply filters
        if lead_id:
            query = query.where(conversations_table.c.lead_id == lead_id)
        
        if channel:
            query = query.where(conversations_table.c.channel == channel)
        
        if status:
            query = query.where(conversations_table.c.status == status)
        
        if assigned_to:
            query = query.where(conversations_table.c.assigned_to == assigned_to)
        
        # Apply pagination
        query = query.offset(skip).limit(limit)
        
        # Execute query
        async with async_session() as session:
            result = await session.execute(query)
            rows = result.fetchall()
        
        # Convert rows to Conversation objects
        conversations = []
        for row in rows:
            conversation_dict = {column.name: getattr(row, column.name) for column in conversations_table.columns}
            conversation = Conversation(**conversation_dict)
            conversations.append(conversation)
        
        self.logger.info(f"Got {len(conversations)} conversations")
        
        return conversations
    
    async def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """
        Get a conversation by ID.
        
        Args:
            conversation_id: Conversation ID
            
        Returns:
            Conversation or None if not found
        """
        self.logger.info(f"Getting conversation {conversation_id}")
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Build query
        query = select(conversations_table).where(conversations_table.c.conversation_id == conversation_id)
        
        # Execute query
        async with async_session() as session:
            result = await session.execute(query)
            row = result.fetchone()
        
        if not row:
            self.logger.warning(f"Conversation {conversation_id} not found")
            return None
        
        # Convert row to Conversation object
        conversation_dict = {column.name: getattr(row, column.name) for column in conversations_table.columns}
        conversation = Conversation(**conversation_dict)
        
        self.logger.info(f"Got conversation {conversation_id}")
        
        return conversation
    
    async def update_conversation(self, conversation_id: str, conversation_data: Dict[str, Any]) -> bool:
        """
        Update a conversation.
        
        Args:
            conversation_id: Conversation ID
            conversation_data: Conversation data to update
            
        Returns:
            True if the conversation was updated, False if not found
        """
        self.logger.info(f"Updating conversation {conversation_id}")
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Update conversation in database
        async with async_session() as session:
            async with session.begin():
                result = await session.execute(
                    update(conversations_table)
                    .where(conversations_table.c.conversation_id == conversation_id)
                    .values(**conversation_data)
                )
        
        # Check if conversation was updated
        if result.rowcount == 0:
            self.logger.warning(f"Conversation {conversation_id} not found")
            return False
        
        self.logger.info(f"Updated conversation {conversation_id}")
        
        return True
