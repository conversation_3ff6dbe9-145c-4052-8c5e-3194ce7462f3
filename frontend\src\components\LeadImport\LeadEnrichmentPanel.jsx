import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  FormControlLabel,
  LinearProgress,
  Alert,
  Chip,
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Grid,
  Card,
  CardContent,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Search as SearchIcon,
  Language as LanguageIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Facebook as FacebookIcon,
  LinkedIn as LinkedInIcon,
  Instagram as InstagramIcon,
  Twitter as TwitterIcon,
  YouTube as YouTubeIcon,
  MusicNote as MusicNoteIcon,
  Settings as SettingsIcon,
  PlayArrow as StartIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
} from '@mui/icons-material';
import leadEnrichmentService from '../../services/leadEnrichmentService';

/**
 * Lead Enrichment Panel Component
 *
 * This component allows users to enrich lead data with additional information:
 * - Find company website if not provided
 * - Scrape contact information from website
 * - Detect Facebook Pixel on website
 */
const LeadEnrichmentPanel = ({ leads, onLeadsEnriched }) => {
  // State for enrichment options
  const [enrichmentOptions, setEnrichmentOptions] = useState({
    findWebsite: true,
    findContactInfo: true,
    detectFacebookPixel: true,
    concurrency: 3,
  });

  // State for enrichment process
  const [isEnriching, setIsEnriching] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [progress, setProgress] = useState(0);
  const [enrichedLeads, setEnrichedLeads] = useState([]);
  const [selectedLeads, setSelectedLeads] = useState([]);
  const [error, setError] = useState(null);
  const [showSettings, setShowSettings] = useState(false);

  // Stats for enrichment results
  const [stats, setStats] = useState({
    total: 0,
    websitesFound: 0,
    emailsFound: 0,
    phonesFound: 0,
    facebookPixelFound: 0,
    facebookFound: 0,
    linkedinFound: 0,
    instagramFound: 0,
    youtubeFound: 0,
    tiktokFound: 0,
    twitterFound: 0,
  });

  // Initialize selected leads
  useEffect(() => {
    if (leads && leads.length > 0) {
      setSelectedLeads(leads.map((_, index) => index));
    }
  }, [leads]);

  // Handle lead selection
  const handleSelectLead = (index) => {
    setSelectedLeads(prev => {
      if (prev.includes(index)) {
        return prev.filter(i => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  // Handle select all leads
  const handleSelectAllLeads = (event) => {
    if (event.target.checked) {
      setSelectedLeads(leads.map((_, index) => index));
    } else {
      setSelectedLeads([]);
    }
  };

  // Handle enrichment options change
  const handleOptionChange = (option, value) => {
    setEnrichmentOptions(prev => ({
      ...prev,
      [option]: value
    }));
  };

  // Start the enrichment process
  const startEnrichment = async () => {
    if (selectedLeads.length === 0) {
      setError('Please select at least one lead to enrich.');
      return;
    }

    setIsEnriching(true);
    setIsPaused(false);
    setProgress(0);
    setError(null);
    setStats({
      total: selectedLeads.length,
      websitesFound: 0,
      emailsFound: 0,
      phonesFound: 0,
      facebookPixelFound: 0,
    });

    try {
      // Get selected leads
      const leadsToEnrich = selectedLeads.map(index => leads[index]);

      // Track progress
      const progressCallback = (completed, total) => {
        setProgress(Math.floor((completed / total) * 100));
      };

      // Enrich leads
      const enriched = await leadEnrichmentService.enrichLeads(
        leadsToEnrich,
        progressCallback,
        enrichmentOptions.concurrency
      );

      // Update stats
      const newStats = {
        total: enriched.length,
        websitesFound: enriched.filter(lead => lead.website).length,
        emailsFound: enriched.filter(lead => lead.email).length,
        phonesFound: enriched.filter(lead => lead.phone).length,
        facebookPixelFound: enriched.filter(lead => lead.facebook_pixel).length,
        facebookFound: enriched.filter(lead => lead.facebook).length,
        linkedinFound: enriched.filter(lead => lead.linkedin).length,
        instagramFound: enriched.filter(lead => lead.instagram).length,
        youtubeFound: enriched.filter(lead => lead.youtube).length,
        tiktokFound: enriched.filter(lead => lead.tiktok).length,
        twitterFound: enriched.filter(lead => lead.twitter).length,
      };
      setStats(newStats);

      // Update enriched leads
      setEnrichedLeads(enriched);

      // Update original leads with enriched data
      const updatedLeads = [...leads];
      selectedLeads.forEach((index, i) => {
        updatedLeads[index] = enriched[i];
      });

      // Notify parent component
      if (onLeadsEnriched) {
        onLeadsEnriched(updatedLeads);
      }

    } catch (error) {
      console.error('Error enriching leads:', error);
      setError('An error occurred while enriching leads. Please try again.');
    } finally {
      setIsEnriching(false);
    }
  };

  // Pause the enrichment process
  const pauseEnrichment = () => {
    setIsPaused(true);
  };

  // Resume the enrichment process
  const resumeEnrichment = () => {
    setIsPaused(false);
  };

  // Stop the enrichment process
  const stopEnrichment = () => {
    setIsEnriching(false);
    setIsPaused(false);
  };

  // Render settings dialog
  const renderSettingsDialog = () => (
    <Dialog open={showSettings} onClose={() => setShowSettings(false)}>
      <DialogTitle>Enrichment Settings</DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 1 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={enrichmentOptions.findWebsite}
                onChange={(e) => handleOptionChange('findWebsite', e.target.checked)}
              />
            }
            label="Find website if not provided"
          />
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', ml: 4, mb: 2 }}>
            Uses search API to find company website if not already provided
          </Typography>

          <FormControlLabel
            control={
              <Checkbox
                checked={enrichmentOptions.findContactInfo}
                onChange={(e) => handleOptionChange('findContactInfo', e.target.checked)}
              />
            }
            label="Find contact information if missing"
          />
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', ml: 4, mb: 2 }}>
            Scrapes website to find email and phone if not already provided
          </Typography>

          <FormControlLabel
            control={
              <Checkbox
                checked={enrichmentOptions.detectFacebookPixel}
                onChange={(e) => handleOptionChange('detectFacebookPixel', e.target.checked)}
              />
            }
            label="Detect Facebook Pixel"
          />
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', ml: 4, mb: 2 }}>
            Checks if Facebook Pixel is installed on the website
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setShowSettings(false)}>Close</Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Lead Enrichment</Typography>
        <Box>
          <Tooltip title="Enrichment Settings">
            <IconButton onClick={() => setShowSettings(true)}>
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Typography variant="body2" paragraph>
        Enrich your leads with additional information such as website, contact details, and Facebook Pixel detection.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {isEnriching && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress variant="determinate" value={progress} sx={{ height: 10, borderRadius: 5 }} />
          <Typography variant="body2" sx={{ mt: 1 }}>
            Progress: {progress}%
          </Typography>
        </Box>
      )}

      {stats.total > 0 && !isEnriching && (
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <Typography variant="h6">{stats.websitesFound}</Typography>
                <Typography variant="body2" color="text.secondary">Websites Found</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <Typography variant="h6">{stats.emailsFound}</Typography>
                <Typography variant="body2" color="text.secondary">Emails Found</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <Typography variant="h6">{stats.phonesFound}</Typography>
                <Typography variant="body2" color="text.secondary">Phones Found</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <Typography variant="h6">{stats.facebookPixelFound}</Typography>
                <Typography variant="body2" color="text.secondary">Facebook Pixels</Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
              Social Media Profiles Found
            </Typography>
          </Grid>

          <Grid item xs={4} sm={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <Typography variant="h6">{stats.facebookFound}</Typography>
                <Typography variant="body2" color="text.secondary">Facebook</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={4} sm={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <Typography variant="h6">{stats.linkedinFound}</Typography>
                <Typography variant="body2" color="text.secondary">LinkedIn</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={4} sm={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <Typography variant="h6">{stats.instagramFound}</Typography>
                <Typography variant="body2" color="text.secondary">Instagram</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={4} sm={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <Typography variant="h6">{stats.youtubeFound}</Typography>
                <Typography variant="body2" color="text.secondary">YouTube</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={4} sm={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <Typography variant="h6">{stats.tiktokFound}</Typography>
                <Typography variant="body2" color="text.secondary">TikTok</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={4} sm={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <Typography variant="h6">{stats.twitterFound}</Typography>
                <Typography variant="body2" color="text.secondary">Twitter</Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      <Paper sx={{ mb: 3 }}>
        <TableContainer sx={{ maxHeight: 400 }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selectedLeads.length > 0 && selectedLeads.length < leads.length}
                    checked={selectedLeads.length > 0 && selectedLeads.length === leads.length}
                    onChange={handleSelectAllLeads}
                  />
                </TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Company</TableCell>
                <TableCell>Website</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Phone</TableCell>
                <TableCell>Facebook Pixel</TableCell>
                <TableCell>Social Media</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {leads.map((lead, index) => (
                <TableRow
                  key={index}
                  hover
                  selected={selectedLeads.includes(index)}
                  onClick={() => handleSelectLead(index)}
                  sx={{ cursor: 'pointer' }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedLeads.includes(index)}
                      onChange={() => handleSelectLead(index)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </TableCell>
                  <TableCell>{lead.name || `${lead.first_name || ''} ${lead.last_name || ''}`}</TableCell>
                  <TableCell>{lead.company}</TableCell>
                  <TableCell>
                    {lead.website ? (
                      <Tooltip title={lead.website}>
                        <Chip
                          icon={<LanguageIcon />}
                          label="Yes"
                          color="success"
                          size="small"
                        />
                      </Tooltip>
                    ) : (
                      <Chip
                        icon={<SearchIcon />}
                        label="No"
                        color="default"
                        size="small"
                      />
                    )}
                  </TableCell>
                  <TableCell>
                    {lead.email ? (
                      <Tooltip title={lead.email}>
                        <Chip
                          icon={<EmailIcon />}
                          label="Yes"
                          color="success"
                          size="small"
                        />
                      </Tooltip>
                    ) : (
                      <Chip
                        label="No"
                        color="default"
                        size="small"
                      />
                    )}
                  </TableCell>
                  <TableCell>
                    {lead.phone ? (
                      <Tooltip title={lead.phone}>
                        <Chip
                          icon={<PhoneIcon />}
                          label="Yes"
                          color="success"
                          size="small"
                        />
                      </Tooltip>
                    ) : (
                      <Chip
                        label="No"
                        color="default"
                        size="small"
                      />
                    )}
                  </TableCell>
                  <TableCell>
                    {lead.facebook_pixel === true ? (
                      <Tooltip title="Facebook Pixel detected">
                        <Chip
                          icon={<FacebookIcon />}
                          label="Yes"
                          color="success"
                          size="small"
                        />
                      </Tooltip>
                    ) : lead.facebook_pixel === false ? (
                      <Tooltip title="No Facebook Pixel detected">
                        <Chip
                          label="No"
                          color="error"
                          size="small"
                        />
                      </Tooltip>
                    ) : (
                      <Chip
                        label="Unknown"
                        color="default"
                        size="small"
                      />
                    )}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      {lead.facebook && (
                        <Tooltip title={lead.facebook}>
                          <IconButton size="small" color="primary">
                            <FacebookIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      {lead.linkedin && (
                        <Tooltip title={lead.linkedin}>
                          <IconButton size="small" color="primary">
                            <LinkedInIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      {lead.instagram && (
                        <Tooltip title={lead.instagram}>
                          <IconButton size="small" color="primary">
                            <InstagramIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      {lead.twitter && (
                        <Tooltip title={lead.twitter}>
                          <IconButton size="small" color="primary">
                            <TwitterIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      {lead.youtube && (
                        <Tooltip title={lead.youtube}>
                          <IconButton size="small" color="primary">
                            <YouTubeIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      {lead.tiktok && (
                        <Tooltip title={lead.tiktok}>
                          <IconButton size="small" color="primary">
                            <MusicNoteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        {!isEnriching ? (
          <Button
            variant="contained"
            color="primary"
            startIcon={<StartIcon />}
            onClick={startEnrichment}
            disabled={selectedLeads.length === 0}
          >
            Start Enrichment
          </Button>
        ) : (
          <>
            {!isPaused ? (
              <Button
                variant="outlined"
                color="warning"
                startIcon={<PauseIcon />}
                onClick={pauseEnrichment}
                sx={{ mr: 1 }}
              >
                Pause
              </Button>
            ) : (
              <Button
                variant="outlined"
                color="primary"
                startIcon={<StartIcon />}
                onClick={resumeEnrichment}
                sx={{ mr: 1 }}
              >
                Resume
              </Button>
            )}
            <Button
              variant="outlined"
              color="error"
              startIcon={<StopIcon />}
              onClick={stopEnrichment}
            >
              Stop
            </Button>
          </>
        )}
      </Box>

      {renderSettingsDialog()}
    </Box>
  );
};

export default LeadEnrichmentPanel;
