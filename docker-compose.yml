version: '3.8'

services:
  # API Gateway
  gateway:
    build:
      context: ./gateway
    ports:
      - "8000:8000"
    depends_on:
      - form-discovery
      - form-analysis
      - lead-management
      - response-ingestion
      - conversation-management
      - ai-orchestrator
      - analytics
      - lead-audit-orchestrator
    environment:
      - FORM_DISCOVERY_URL=http://form-discovery:8001
      - FORM_ANALYSIS_URL=http://form-analysis:8002
      - LEAD_MANAGEMENT_URL=http://lead-management:8003
      - RESPONSE_INGESTION_URL=http://response-ingestion:8004
      - CONVERSATION_MANAGEMENT_URL=http://conversation-management:8005
      - AI_ORCHESTRATOR_URL=http://ai-orchestrator:8006
      - ANALYTICS_URL=http://analytics:8007
      - LEAD_AUDIT_ORCHESTRATOR_URL=http://lead-audit-orchestrator:8008

  # Form Discovery Service
  form-discovery:
    build:
      context: ./services/form-discovery
    ports:
      - "8001:8001"
    depends_on:
      - postgres
      - redis
      - mongodb
      - kafka

  # Form Analysis Service
  form-analysis:
    build:
      context: ./services/form-analysis
    ports:
      - "8002:8002"
    depends_on:
      - postgres
      - redis
      - mongodb
      - kafka

  # Lead Management Service
  lead-management:
    build:
      context: ./services/lead-management
    ports:
      - "8003:8003"
    depends_on:
      - postgres
      - redis
      - kafka

  # Response Ingestion Service
  response-ingestion:
    build:
      context: ./services/response-ingestion
    ports:
      - "8004:8004"
    depends_on:
      - postgres
      - redis
      - kafka

  # Conversation Management Service
  conversation-management:
    build:
      context: ./services/conversation-management
    ports:
      - "8005:8005"
    depends_on:
      - postgres
      - redis
      - kafka

  # AI Orchestrator Service
  ai-orchestrator:
    build:
      context: ./services/ai-orchestrator
    ports:
      - "8006:8006"
    depends_on:
      - postgres
      - redis
      - mongodb
      - kafka

  # Analytics Service
  analytics:
    build:
      context: ./services/analytics
    ports:
      - "8007:8007"
    depends_on:
      - postgres
      - redis
      - clickhouse
      - kafka

  # Lead Audit Orchestrator Service
  lead-audit-orchestrator:
    build:
      context: ./services/lead-audit-orchestrator
    ports:
      - "8008:8008"
    depends_on:
      - postgres
      - redis
      - kafka
      - form-discovery
      - lead-management
      - response-ingestion
      - analytics
    environment:
      - FORM_DISCOVERY_URL=http://form-discovery:8001
      - LEAD_MANAGEMENT_URL=http://lead-management:8003
      - RESPONSE_MONITORING_URL=http://response-ingestion:8004
      - ANALYTICS_URL=http://analytics:8007
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}

  # Frontend
  frontend:
    build:
      context: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - gateway

  # Databases and Infrastructure
  postgres:
    image: postgres:15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: leadgen
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  mongodb:
    image: mongo:6
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

  clickhouse:
    image: clickhouse/clickhouse-server:23
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - clickhouse_data:/var/lib/clickhouse

  kafka:
    image: bitnami/kafka:3.4
    ports:
      - "9092:9092"
    environment:
      - KAFKA_CFG_NODE_ID=0
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka:9093
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - ALLOW_PLAINTEXT_LISTENER=yes
    volumes:
      - kafka_data:/bitnami/kafka

volumes:
  postgres_data:
  redis_data:
  mongodb_data:
  clickhouse_data:
  kafka_data:
