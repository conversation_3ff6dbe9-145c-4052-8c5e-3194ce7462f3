/**
 * Supabase Service
 *
 * This service provides functions to interact with the Supabase database
 * for lead management, form submissions, and other operations.
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
const supabaseServiceKey = import.meta.env.VITE_SUPABASE_SERVICE_KEY;

// Log Supabase configuration (without exposing full keys)
console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Anon Key available:', !!supabaseAnonKey);
console.log('Supabase Anon Key prefix:', supabaseAnonKey ? supabaseAnonKey.substring(0, 10) + '...' : 'undefined');
console.log('Supabase Service Key available:', !!supabaseServiceKey);
console.log('Supabase Service Key prefix:', supabaseServiceKey ? supabaseServiceKey.substring(0, 10) + '...' : 'undefined');

// Create Supabase clients
const supabase = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Lead Management Functions
 */

// Create a new lead
const createLead = async (leadData) => {
  try {
    console.log('Creating lead with data:', leadData);

    // Use admin client for write operations
    const { data, error } = await supabaseAdmin
      .from('leads')
      .insert(leadData)
      .select();

    if (error) {
      console.error('Supabase error creating lead:', error);
      throw error;
    }

    console.log('Lead created successfully:', data[0]);
    return data[0];
  } catch (error) {
    console.error('Error creating lead:', error);
    throw error;
  }
};

// Create multiple leads in a batch
const createLeads = async (leadsData, batchInfo = {}) => {
  try {
    console.log('Creating leads batch with data:', leadsData);
    console.log('Batch info:', batchInfo);

    // First create an import batch record
    console.log('Creating import batch record...');
    const { data: batchData, error: batchError } = await supabaseAdmin
      .from('import_batches')
      .insert({
        name: batchInfo.name || 'CSV Import',
        file_name: batchInfo.fileName || 'import.csv',
        record_count: leadsData.length,
        successful_count: 0,
        failed_count: 0,
        status: 'processing',
        mapping: batchInfo.mapping || {},
        settings: batchInfo.settings || {}
      })
      .select();

    if (batchError) {
      console.error('Error creating batch:', batchError);
      throw batchError;
    }

    console.log('Batch created:', batchData);
    const batchId = batchData[0].id;

    // Add the batch ID to each lead
    const leadsWithBatchId = leadsData.map(lead => ({
      ...lead,
      import_batch_id: batchId,
      imported_at: new Date().toISOString()
    }));

    console.log('Inserting leads with batch ID:', leadsWithBatchId);

    // Insert all leads
    const { data, error } = await supabaseAdmin
      .from('leads')
      .insert(leadsWithBatchId)
      .select();

    if (error) {
      console.error('Error inserting leads:', error);

      // Update batch with error information
      console.log('Updating batch with error information...');
      await supabaseAdmin
        .from('import_batches')
        .update({
          status: 'failed',
          failed_count: leadsData.length
        })
        .eq('id', batchId);

      throw error;
    }

    console.log('Leads inserted successfully:', data);

    // Update batch with success information
    console.log('Updating batch with success information...');
    await supabaseAdmin
      .from('import_batches')
      .update({
        status: 'completed',
        successful_count: data.length
      })
      .eq('id', batchId);

    return {
      leads: data,
      batch: {
        id: batchId,
        count: data.length
      }
    };
  } catch (error) {
    console.error('Error creating leads batch:', error);
    throw error;
  }
};

// Get a lead by ID
const getLeadById = async (id) => {
  try {
    const { data, error } = await supabase
      .from('leads')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error getting lead with ID ${id}:`, error);
    throw error;
  }
};

// Update a lead
const updateLead = async (id, leadData) => {
  try {
    // Add updated_at timestamp
    const updatedData = {
      ...leadData,
      updated_at: new Date().toISOString()
    };

    // Use admin client for write operations
    const { data, error } = await supabaseAdmin
      .from('leads')
      .update(updatedData)
      .eq('id', id)
      .select();

    if (error) throw error;
    return data[0];
  } catch (error) {
    console.error(`Error updating lead with ID ${id}:`, error);
    throw error;
  }
};

// Delete a lead
const deleteLead = async (id) => {
  try {
    const { error } = await supabase
      .from('leads')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error deleting lead with ID ${id}:`, error);
    throw error;
  }
};

// Get all leads with optional filtering (with pagination)
const getLeads = async (filters = {}, page = 1, pageSize = 1000) => {
  try {
    let query = supabase
      .from('leads')
      .select('*', { count: 'exact' });

    // Apply filters
    if (filters.status) {
      query = query.eq('status', filters.status);
    }

    if (filters.enriched !== undefined) {
      query = query.eq('enriched', filters.enriched);
    }

    if (filters.formSubmitted !== undefined) {
      query = query.eq('form_submitted', filters.formSubmitted);
    }

    if (filters.search) {
      query = query.textSearch('search_vector', filters.search);
    }

    if (filters.importBatchId) {
      query = query.eq('import_batch_id', filters.importBatchId);
    }

    // Apply pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;
    query = query.range(from, to);

    // Apply sorting
    if (filters.sortBy) {
      const order = filters.sortDesc ? 'desc' : 'asc';
      query = query.order(filters.sortBy, { ascending: !filters.sortDesc });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    const { data, error, count } = await query;

    if (error) throw error;

    console.log(`Fetched ${data.length} leads out of ${count} total leads (page ${page})`);

    return {
      leads: data,
      total: count,
      page,
      pageSize,
      totalPages: Math.ceil(count / pageSize)
    };
  } catch (error) {
    console.error('Error getting leads:', error);
    throw error;
  }
};

// Get ALL leads with optional filtering (fetches all pages)
const getAllLeads = async (filters = {}, batchSize = 1000, onProgress = null) => {
  try {
    // First, get the total count
    let initialQuery = supabase
      .from('leads')
      .select('*', { count: 'exact' });

    // Apply filters
    if (filters.status) {
      initialQuery = initialQuery.eq('status', filters.status);
    }

    if (filters.enriched !== undefined) {
      initialQuery = initialQuery.eq('enriched', filters.enriched);
    }

    if (filters.formSubmitted !== undefined) {
      initialQuery = initialQuery.eq('form_submitted', filters.formSubmitted);
    }

    if (filters.search) {
      initialQuery = initialQuery.textSearch('search_vector', filters.search);
    }

    if (filters.importBatchId) {
      initialQuery = initialQuery.eq('import_batch_id', filters.importBatchId);
    }

    // Just get one record to get the count
    initialQuery = initialQuery.range(0, 0);

    const { count, error: countError } = await initialQuery;

    if (countError) throw countError;

    console.log(`Total leads to fetch: ${count}`);

    // Calculate number of pages
    const totalPages = Math.ceil(count / batchSize);
    let allLeads = [];

    // Fetch all pages
    for (let page = 1; page <= totalPages; page++) {
      const { leads } = await getLeads(filters, page, batchSize);
      allLeads = [...allLeads, ...leads];

      // Report progress if callback provided
      if (onProgress) {
        onProgress({
          loaded: allLeads.length,
          total: count,
          progress: Math.round((allLeads.length / count) * 100),
          page,
          totalPages
        });
      }

      console.log(`Fetched batch ${page}/${totalPages}, total leads so far: ${allLeads.length}`);
    }

    return {
      leads: allLeads,
      total: count
    };
  } catch (error) {
    console.error('Error getting all leads:', error);
    throw error;
  }
};

// Search leads
const searchLeads = async (searchTerm, page = 1, pageSize = 50) => {
  try {
    const { data, error, count } = await supabase
      .from('leads')
      .select('*', { count: 'exact' })
      .textSearch('search_vector', searchTerm)
      .range((page - 1) * pageSize, page * pageSize - 1);

    if (error) throw error;

    return {
      leads: data,
      total: count,
      page,
      pageSize,
      totalPages: Math.ceil(count / pageSize)
    };
  } catch (error) {
    console.error(`Error searching leads for "${searchTerm}":`, error);
    throw error;
  }
};

/**
 * Form Submission Functions
 */

// Create a form submission record
const createFormSubmission = async (submissionData) => {
  try {
    const { data, error } = await supabase
      .from('form_submissions')
      .insert(submissionData)
      .select();

    if (error) throw error;

    // Update the lead's form_submitted status
    if (submissionData.lead_id && submissionData.success) {
      await supabase
        .from('leads')
        .update({ form_submitted: true })
        .eq('id', submissionData.lead_id);
    }

    return data[0];
  } catch (error) {
    console.error('Error creating form submission:', error);
    throw error;
  }
};

// Update a form submission
const updateFormSubmission = async (id, submissionData) => {
  try {
    const { data, error } = await supabase
      .from('form_submissions')
      .update(submissionData)
      .eq('id', id)
      .select();

    if (error) throw error;

    // Update the lead's form_submitted status if submission was successful
    if (submissionData.lead_id && submissionData.success) {
      await supabase
        .from('leads')
        .update({ form_submitted: true })
        .eq('id', submissionData.lead_id);
    }

    return data[0];
  } catch (error) {
    console.error(`Error updating form submission with ID ${id}:`, error);
    throw error;
  }
};

// Get form submissions for a lead
const getFormSubmissionsForLead = async (leadId) => {
  try {
    const { data, error } = await supabase
      .from('form_submissions')
      .select('*')
      .eq('lead_id', leadId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error getting form submissions for lead ${leadId}:`, error);
    throw error;
  }
};

/**
 * Settings Functions
 */

// Get settings by key
const getSettings = async (key) => {
  try {
    const { data, error } = await supabase
      .from('settings')
      .select('*')
      .eq('key', key)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No settings found with this key
        return null;
      }
      throw error;
    }

    return data;
  } catch (error) {
    console.error(`Error getting settings for key ${key}:`, error);
    throw error;
  }
};

// Update settings
const updateSettings = async (key, value) => {
  try {
    // Check if settings exist
    const existing = await getSettings(key);

    if (existing) {
      // Update existing settings
      const { data, error } = await supabase
        .from('settings')
        .update({ value })
        .eq('key', key)
        .select();

      if (error) throw error;
      return data[0];
    } else {
      // Create new settings
      const { data, error } = await supabase
        .from('settings')
        .insert({
          key,
          value,
          description: `Settings for ${key}`
        })
        .select();

      if (error) throw error;
      return data[0];
    }
  } catch (error) {
    console.error(`Error updating settings for key ${key}:`, error);
    throw error;
  }
};

// Export all functions
const supabaseService = {
  // Lead functions
  createLead,
  createLeads,
  getLeadById,
  updateLead,
  deleteLead,
  getLeads,
  getAllLeads,
  searchLeads,

  // Form submission functions
  createFormSubmission,
  updateFormSubmission,
  getFormSubmissionsForLead,

  // Settings functions
  getSettings,
  updateSettings,

  // Supabase clients (for direct access if needed)
  supabase,
  supabaseAdmin
};

export default supabaseService;
