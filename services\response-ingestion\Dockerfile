FROM python:3.9-slim

WORKDIR /app

# Copy requirements files
COPY services/requirements.txt /app/requirements.txt
COPY services/response-ingestion/requirements.txt /app/service-requirements.txt

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r service-requirements.txt

# Copy shared libraries
COPY libs /app/libs

# Copy service code
COPY services/response-ingestion /app/services/response-ingestion

# Set environment variables
ENV PYTHONPATH=/app
ENV SERVICE_NAME=response-ingestion

# Expose port
EXPOSE 8004

# Run the service
CMD ["python", "services/response-ingestion/main.py"]
