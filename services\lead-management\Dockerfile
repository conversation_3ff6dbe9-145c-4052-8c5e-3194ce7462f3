FROM python:3.9-slim

WORKDIR /app

# Copy requirements files
COPY services/requirements.txt /app/requirements.txt
COPY services/lead-management/requirements.txt /app/service-requirements.txt

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r service-requirements.txt

# Copy shared libraries
COPY libs /app/libs

# Copy service code
COPY services/lead-management /app/services/lead-management

# Set environment variables
ENV PYTHONPATH=/app
ENV SERVICE_NAME=lead-management

# Expose port
EXPOSE 8003

# Run the service
CMD ["python", "services/lead-management/main.py"]
