import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Close as CloseIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Language as LanguageIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';
import supabaseService from '../../services/supabaseService';

// Lead status options
const LEAD_STATUS_OPTIONS = [
  { value: 'new', label: 'New', color: 'primary' },
  { value: 'contacted', label: 'Contacted', color: 'info' },
  { value: 'qualified', label: 'Qualified', color: 'success' },
  { value: 'not_interested', label: 'Not Interested', color: 'error' },
  { value: 'converted', label: 'Converted', color: 'secondary' },
  { value: 'nurturing', label: 'Nurturing', color: 'warning' }
];

/**
 * Lead Details Dialog Component
 *
 * This component displays detailed information about a lead and allows editing.
 */
const LeadDetailsDialog = ({ open, leadId, onClose, onLeadUpdated }) => {
  const [lead, setLead] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({});

  // Fetch lead data when dialog opens
  useEffect(() => {
    if (open && leadId) {
      fetchLeadDetails();
    }
  }, [open, leadId]);

  // Fetch lead details from Supabase
  const fetchLeadDetails = async () => {
    setLoading(true);
    setError(null);

    try {
      const leadData = await supabaseService.getLeadById(leadId);
      setLead(leadData);
      setFormData(leadData);
    } catch (err) {
      console.error('Error fetching lead details:', err);
      setError('Failed to load lead details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Save lead changes
  const handleSave = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await supabaseService.updateLead(leadId, formData);
      setLead(formData);
      setSuccess('Lead updated successfully');
      setEditMode(false);
      if (onLeadUpdated) onLeadUpdated(formData);
    } catch (err) {
      console.error('Error updating lead:', err);
      setError('Failed to update lead. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Update lead status directly (quick action)
  const updateLeadStatus = async (status) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Get the status label for the success message
      const statusOption = LEAD_STATUS_OPTIONS.find(option => option.value === status);
      const statusLabel = statusOption ? statusOption.label : status;

      // Update the lead status
      const updatedLead = await supabaseService.updateLead(leadId, { status });

      if (!updatedLead) {
        throw new Error('Failed to update lead status. No response from server.');
      }

      // Update local state
      setLead(prev => ({ ...prev, status }));
      setFormData(prev => ({ ...prev, status }));
      setSuccess(`Lead marked as "${statusLabel}"`);

      // Notify parent component
      if (onLeadUpdated) onLeadUpdated({ ...lead, status });
    } catch (err) {
      console.error('Error updating lead status:', err);
      setError(`Failed to update lead status: ${err.message || 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  // Cancel edit mode
  const handleCancel = () => {
    setFormData(lead);
    setEditMode(false);
  };

  // Get status chip color
  const getStatusColor = (status) => {
    const statusOption = LEAD_STATUS_OPTIONS.find(option => option.value === status);
    return statusOption ? statusOption.color : 'default';
  };

  // Get status label
  const getStatusLabel = (status) => {
    const statusOption = LEAD_STATUS_OPTIONS.find(option => option.value === status);
    return statusOption ? statusOption.label : status;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{ sx: { borderRadius: 2 } }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box component="span">Lead Details</Box>
          {lead && (
            <Chip
              label={getStatusLabel(lead.status || 'new')}
              color={getStatusColor(lead.status || 'new')}
              size="small"
              sx={{ ml: 2 }}
            />
          )}
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 2 }}>
        {loading && !lead ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : error && !lead ? (
          <Alert severity="error">{error}</Alert>
        ) : lead ? (
          <>
            {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}
            {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

            <Grid container spacing={3}>
              {/* Basic Information */}
              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                    Basic Information
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      {editMode ? (
                        <TextField
                          fullWidth
                          label="First Name"
                          name="first_name"
                          value={formData.first_name || ''}
                          onChange={handleChange}
                          size="small"
                          margin="dense"
                        />
                      ) : (
                        <Box>
                          <Typography variant="caption" color="text.secondary">First Name</Typography>
                          <Typography variant="body1">{lead.first_name || '-'}</Typography>
                        </Box>
                      )}
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      {editMode ? (
                        <TextField
                          fullWidth
                          label="Last Name"
                          name="last_name"
                          value={formData.last_name || ''}
                          onChange={handleChange}
                          size="small"
                          margin="dense"
                        />
                      ) : (
                        <Box>
                          <Typography variant="caption" color="text.secondary">Last Name</Typography>
                          <Typography variant="body1">{lead.last_name || '-'}</Typography>
                        </Box>
                      )}
                    </Grid>

                    <Grid item xs={12}>
                      {editMode ? (
                        <TextField
                          fullWidth
                          label="Email"
                          name="email"
                          value={formData.email || ''}
                          onChange={handleChange}
                          size="small"
                          margin="dense"
                        />
                      ) : (
                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                            <EmailIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} /> Email
                          </Typography>
                          <Typography variant="body1">{lead.email || '-'}</Typography>
                        </Box>
                      )}
                    </Grid>

                    <Grid item xs={12}>
                      {editMode ? (
                        <TextField
                          fullWidth
                          label="Phone"
                          name="phone"
                          value={formData.phone || ''}
                          onChange={handleChange}
                          size="small"
                          margin="dense"
                        />
                      ) : (
                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                            <PhoneIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} /> Phone
                          </Typography>
                          <Typography variant="body1">{lead.phone || '-'}</Typography>
                        </Box>
                      )}
                    </Grid>
                  </Grid>
                </Box>

                <Box>
                  <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <BusinessIcon sx={{ mr: 1, color: 'primary.main' }} />
                    Company Information
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      {editMode ? (
                        <TextField
                          fullWidth
                          label="Company"
                          name="company"
                          value={formData.company || ''}
                          onChange={handleChange}
                          size="small"
                          margin="dense"
                        />
                      ) : (
                        <Box>
                          <Typography variant="caption" color="text.secondary">Company</Typography>
                          <Typography variant="body1">{lead.company || '-'}</Typography>
                        </Box>
                      )}
                    </Grid>

                    <Grid item xs={12}>
                      {editMode ? (
                        <TextField
                          fullWidth
                          label="Website"
                          name="website"
                          value={formData.website || ''}
                          onChange={handleChange}
                          size="small"
                          margin="dense"
                        />
                      ) : (
                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                            <LanguageIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} /> Website
                          </Typography>
                          <Typography variant="body1">
                            {lead.website ? (
                              <a href={lead.website.startsWith('http') ? lead.website : `https://${lead.website}`}
                                 target="_blank"
                                 rel="noopener noreferrer"
                                 style={{ color: 'inherit', textDecoration: 'underline' }}>
                                {lead.website}
                              </a>
                            ) : '-'}
                          </Typography>
                        </Box>
                      )}
                    </Grid>
                  </Grid>
                </Box>
              </Grid>

              {/* Status and Notes */}
              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ mb: 1 }}>Lead Status</Typography>

                  {editMode ? (
                    <FormControl fullWidth size="small" margin="dense">
                      <InputLabel>Status</InputLabel>
                      <Select
                        name="status"
                        value={formData.status || 'new'}
                        onChange={handleChange}
                        label="Status"
                      >
                        {LEAD_STATUS_OPTIONS.map(option => (
                          <MenuItem key={option.value} value={option.value}>
                            <Chip
                              label={option.label}
                              color={option.color}
                              size="small"
                              sx={{ mr: 1 }}
                            />
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  ) : (
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                        {LEAD_STATUS_OPTIONS.map(option => (
                          <Chip
                            key={option.value}
                            label={option.label}
                            color={option.value === (lead.status || 'new') ? option.color : 'default'}
                            variant={option.value === (lead.status || 'new') ? 'filled' : 'outlined'}
                            onClick={() => updateLeadStatus(option.value)}
                            disabled={loading || option.value === (lead.status || 'new')}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                </Box>

                <Box>
                  <Typography variant="subtitle1" sx={{ mb: 1 }}>Notes</Typography>

                  {editMode ? (
                    <TextField
                      fullWidth
                      label="Notes"
                      name="notes"
                      value={formData.notes || ''}
                      onChange={handleChange}
                      multiline
                      rows={4}
                      margin="dense"
                    />
                  ) : (
                    <Box sx={{
                      p: 2,
                      bgcolor: 'background.default',
                      borderRadius: 1,
                      minHeight: '100px'
                    }}>
                      <Typography variant="body2">
                        {lead.notes || 'No notes available for this lead.'}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Grid>
            </Grid>
          </>
        ) : null}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ px: 3, py: 2 }}>
        {lead && (
          <>
            {editMode ? (
              <>
                <Button
                  variant="outlined"
                  color="error"
                  onClick={handleCancel}
                  startIcon={<CancelIcon />}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSave}
                  startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                  disabled={loading}
                >
                  Save Changes
                </Button>
              </>
            ) : (
              <>
                <Button onClick={onClose} color="inherit">Close</Button>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => setEditMode(true)}
                  startIcon={<EditIcon />}
                >
                  Edit Lead
                </Button>
              </>
            )}
          </>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default LeadDetailsDialog;
