-- SQL script to reset all tables in the Supabase database
-- Run this in the Supabase SQL Editor

-- First, get a list of all tables in the public schema
DO $$
DECLARE
    table_record RECORD;
BEGIN
    -- Disable triggers temporarily to avoid foreign key constraints
    EXECUTE 'SET session_replication_role = replica';
    
    -- Loop through all tables in the public schema
    FOR table_record IN 
        SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    LOOP
        -- Truncate each table (faster than DELETE and resets sequences)
        EXECUTE 'TRUNCATE TABLE "' || table_record.tablename || '" CASCADE';
        RAISE NOTICE 'Truncated table: %', table_record.tablename;
    END LOOP;
    
    -- Re-enable triggers
    EXECUTE 'SET session_replication_role = DEFAULT';
    
    RAISE NOTICE 'All tables have been reset successfully.';
END $$;

-- Optional: If you want to drop all tables instead of just truncating them
-- Uncomment the section below

/*
DO $$
DECLARE
    table_record RECORD;
BEGIN
    -- Loop through all tables in the public schema
    FOR table_record IN 
        SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    LOOP
        -- Drop each table
        EXECUTE 'DROP TABLE IF EXISTS "' || table_record.tablename || '" CASCADE';
        RAISE NOTICE 'Dropped table: %', table_record.tablename;
    END LOOP;
    
    RAISE NOTICE 'All tables have been dropped successfully.';
END $$;
*/
