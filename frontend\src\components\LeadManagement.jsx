import React, { useState, useEffect } from 'react';
import {
  <PERSON>, Card, CardContent, Typography, Grid, Chip, Button, Table, TableBody,
  TableCell, TableContainer, TableHead, TableRow, Paper, Dialog, DialogTitle,
  DialogContent, DialogActions, Tabs, Tab, IconButton, Tooltip, LinearProgress,
  Alert, Divider, Stack, Avatar, Badge
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Email as EmailIcon,
  LinkedIn as LinkedInIcon,
  Assessment as ReportIcon,
  Phone as PhoneIcon,
  Business as BusinessIcon,
  Language as WebsiteIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Pending as PendingIcon,
  Send as SendIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

const LeadManagement = () => {
  const [leads, setLeads] = useState([]);
  const [selectedLead, setSelectedLead] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [auditReports, setAuditReports] = useState({});
  const [outreachMessages, setOutreachMessages] = useState({});
  const [leadStates, setLeadStates] = useState({});

  // Mock data for demonstration
  const mockLeads = [
    {
      id: '1',
      first_name: 'John',
      last_name: 'Smith',
      email: '<EMAIL>',
      phone: '(*************',
      company: 'Acme Roofing Solutions',
      website: 'acmeroofing.com',
      title: 'Owner',
      created_at: '2023-11-01T10:00:00Z',
      status: 'active'
    },
    {
      id: '2',
      first_name: 'Sarah',
      last_name: 'Johnson',
      email: '<EMAIL>',
      phone: '(*************',
      company: 'Fast Response Inc',
      website: 'fastresponse.com',
      title: 'Marketing Director',
      created_at: '2023-11-02T14:30:00Z',
      status: 'active'
    }
  ];

  const mockAuditReports = {
    '1': {
      id: 'audit_1',
      lead_id: '1',
      audit_date: '2023-11-03T09:00:00Z',
      responded: true,
      response_time_seconds: 2180,
      response_time_human: '36 minutes',
      response_channel: 'sms',
      grade: 'C',
      total_score: 75,
      conversion_rate_current: 52,
      facebook_pixel_detected: false,
      audit_status: 'completed'
    },
    '2': {
      id: 'audit_2',
      lead_id: '2',
      audit_date: '2023-11-03T11:00:00Z',
      responded: true,
      response_time_seconds: 240,
      response_time_human: '4 minutes',
      response_channel: 'phone',
      grade: 'A',
      total_score: 120,
      conversion_rate_current: 85,
      facebook_pixel_detected: true,
      audit_status: 'completed'
    }
  };

  const mockOutreachMessages = {
    '1': [
      // Email sequence
      {
        id: 'msg_1',
        message_type: 'email',
        status: 'draft',
        sequence_position: 1,
        send_delay_days: 0,
        template_type: 'audit_reveal',
        template_focus: 'slow_response_cost',
        subject_line: 'Quick question about Acme Roofing\'s lead response',
        message_body: 'Hi John,\n\nI noticed Acme Roofing is responding to leads in about 36 minutes. While you\'re responding (which is great!), industry leaders who respond in under 5 minutes see 85% conversion rates vs your current 52%.\n\nI ran a quick audit of your response system and found some opportunities that could boost your conversions significantly.\n\nWould you be interested in a 15-minute call to discuss the findings?\n\nBest regards,\nAlex',
        created_at: '2023-11-03T12:00:00Z'
      },
      {
        id: 'msg_2',
        message_type: 'email',
        status: 'draft',
        sequence_position: 2,
        send_delay_days: 3,
        template_type: 'value_demonstration',
        template_focus: 'revenue_opportunity',
        subject_line: 'The $47,000 opportunity I mentioned for Acme Roofing',
        message_body: 'Hi John,\n\nFollowing up on my previous email about Acme Roofing\'s response time.\n\nI calculated the revenue impact: if you improved your response time from 36 minutes to under 5 minutes, you could increase your conversion rate from 52% to 85%.\n\nFor a company your size, that\'s approximately $47,000 in additional annual revenue from the same lead volume.\n\nWould you like me to show you exactly how we calculated this and what steps could get you there?\n\nBest,\nAlex',
        created_at: '2023-11-03T12:00:00Z'
      },
      {
        id: 'msg_3',
        message_type: 'email',
        status: 'draft',
        sequence_position: 3,
        send_delay_days: 7,
        template_type: 'educational_content',
        template_focus: 'how_to_improve',
        subject_line: 'Free guide: 5-minute response time setup for roofing companies',
        message_body: 'Hi John,\n\nI put together a quick guide specifically for roofing companies on how to achieve sub-5-minute response times.\n\nIt covers:\n• Lead routing automation\n• SMS notification setup\n• Response templates that convert\n• Common mistakes that slow you down\n\nNo strings attached - just want to help Acme Roofing capture more of those leads you\'re working hard to generate.\n\nDownload here: [link]\n\nBest,\nAlex',
        created_at: '2023-11-03T12:00:00Z'
      },
      {
        id: 'msg_4',
        message_type: 'email',
        status: 'draft',
        sequence_position: 4,
        send_delay_days: 14,
        template_type: 'success_story',
        template_focus: 'similar_company',
        subject_line: 'How ABC Roofing increased leads by 67% (case study)',
        message_body: 'Hi John,\n\nThought you\'d find this interesting - ABC Roofing (similar size to Acme) was in the exact same situation 6 months ago.\n\nThey were responding to leads in 45 minutes and converting about 48%.\n\nAfter implementing the response optimization system:\n• Response time: 3 minutes\n• Conversion rate: 81%\n• Lead volume increased 67% (better reputation = more referrals)\n\nThe owner said it was the best investment he\'d made in 10 years.\n\nWant to see the full case study?\n\nBest,\nAlex',
        created_at: '2023-11-03T12:00:00Z'
      },
      {
        id: 'msg_5',
        message_type: 'email',
        status: 'draft',
        sequence_position: 5,
        send_delay_days: 21,
        template_type: 'consultation_offer',
        template_focus: 'free_optimization',
        subject_line: 'Final offer: Free response optimization consultation',
        message_body: 'Hi John,\n\nThis is my final email about Acme Roofing\'s response optimization opportunity.\n\nI\'ve shared the audit findings, revenue calculations, implementation guide, and a success story from a similar company.\n\nIf you\'re interested in exploring this further, I\'d be happy to offer a complimentary 30-minute consultation where we can:\n• Review your specific situation\n• Map out a custom optimization plan\n• Discuss implementation timeline and costs\n\nNo pressure - if the timing isn\'t right, I completely understand.\n\nJust reply if you\'d like to schedule.\n\nBest regards,\nAlex',
        created_at: '2023-11-03T12:00:00Z'
      },
      // LinkedIn sequence
      {
        id: 'msg_6',
        message_type: 'linkedin',
        status: 'draft',
        sequence_position: 1,
        send_delay_days: 0,
        template_type: 'connection_request',
        template_focus: 'improvement_opportunity',
        message_body: 'Hi John! I ran a quick audit on Acme Roofing\'s lead response and found a 33% conversion improvement opportunity. Mind if I connect and share the insights?',
        created_at: '2023-11-03T12:05:00Z'
      },
      {
        id: 'msg_7',
        message_type: 'linkedin',
        status: 'draft',
        sequence_position: 2,
        send_delay_days: 5,
        template_type: 'insight_share',
        template_focus: 'specific_findings',
        message_body: 'Thanks for connecting! Your 36-minute response time is costing ~$47K annually. Companies responding in <5 min see 85% conversion vs your 52%. Quick call to discuss?',
        created_at: '2023-11-03T12:05:00Z'
      },
      {
        id: 'msg_8',
        message_type: 'linkedin',
        status: 'draft',
        sequence_position: 3,
        send_delay_days: 12,
        template_type: 'consultation_offer',
        template_focus: 'free_advice',
        message_body: 'Last note - happy to share the free response optimization guide I created for roofing companies. No strings attached, just want to help. Interested?',
        created_at: '2023-11-03T12:05:00Z'
      }
    ],
    '2': [
      {
        id: 'msg_3',
        message_type: 'email',
        status: 'sent',
        subject_line: 'Impressed by Fast Response Inc\'s lead handling',
        message_body: 'Hi Sarah,\n\nImpressive! Fast Response Inc is responding to leads in 4 minutes - you\'re already beating 90% of your competitors.\n\nI did notice one optimization opportunity: your website is missing Facebook Pixel tracking, which means you\'re losing retargeting opportunities.\n\nWould you like to see the full audit results?\n\nBest,\nAlex',
        created_at: '2023-11-03T13:00:00Z',
        sent_at: '2023-11-03T13:30:00Z'
      }
    ]
  };

  const mockLeadStates = {
    '1': {
      current_state: 'outreach_generated',
      enrichment_completed: true,
      form_submission_completed: true,
      audit_completed: true,
      outreach_generated: true,
      outreach_sent: false,
      response_received: false
    },
    '2': {
      current_state: 'outreach_sent',
      enrichment_completed: true,
      form_submission_completed: true,
      audit_completed: true,
      outreach_generated: true,
      outreach_sent: true,
      response_received: false
    }
  };

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setLeads(mockLeads);
      setAuditReports(mockAuditReports);
      setOutreachMessages(mockOutreachMessages);
      setLeadStates(mockLeadStates);
      setLoading(false);
    }, 1000);
  }, []);

  const getStateColor = (state) => {
    const colors = {
      'imported': '#9e9e9e',
      'enriched': '#2196f3',
      'form_submitted': '#ff9800',
      'audit_completed': '#9c27b0',
      'outreach_generated': '#3f51b5',
      'outreach_sent': '#4caf50',
      'responded': '#8bc34a',
      'qualified': '#4caf50',
      'disqualified': '#f44336'
    };
    return colors[state] || '#9e9e9e';
  };

  const getGradeColor = (grade) => {
    const colors = {
      'A+': '#4caf50', 'A': '#8bc34a', 'B': '#ffeb3b',
      'C': '#ff9800', 'D': '#f44336', 'F': '#9e9e9e'
    };
    return colors[grade] || '#9e9e9e';
  };

  const handleViewLead = (lead) => {
    setSelectedLead(lead);
    setTabValue(0);
    setDialogOpen(true);
  };

  const handleGenerateOutreach = async (leadId) => {
    // TODO: Implement API call to generate outreach
    console.log('Generating outreach for lead:', leadId);
  };

  const handleSendMessage = async (messageId) => {
    // TODO: Implement API call to send message
    console.log('Sending message:', messageId);
  };

  const renderLeadProgress = (leadId) => {
    const state = leadStates[leadId];
    if (!state) return null;

    const steps = [
      { key: 'enrichment_completed', label: 'Enriched', icon: <BusinessIcon /> },
      { key: 'form_submission_completed', label: 'Form Submitted', icon: <SendIcon /> },
      { key: 'audit_completed', label: 'Audited', icon: <ReportIcon /> },
      { key: 'outreach_generated', label: 'Outreach Generated', icon: <EditIcon /> },
      { key: 'outreach_sent', label: 'Outreach Sent', icon: <EmailIcon /> },
      { key: 'response_received', label: 'Response Received', icon: <CheckCircleIcon /> }
    ];

    const completedSteps = steps.filter(step => state[step.key]).length;
    const progress = (completedSteps / steps.length) * 100;

    return (
      <Box>
        <LinearProgress variant="determinate" value={progress} sx={{ mb: 1 }} />
        <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap' }}>
          {steps.map((step, index) => (
            <Tooltip key={step.key} title={step.label}>
              <Avatar
                sx={{
                  width: 24,
                  height: 24,
                  bgcolor: state[step.key] ? '#4caf50' : '#e0e0e0',
                  color: state[step.key] ? 'white' : '#9e9e9e'
                }}
              >
                {React.cloneElement(step.icon, { fontSize: 'small' })}
              </Avatar>
            </Tooltip>
          ))}
        </Stack>
      </Box>
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading leads...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          Lead Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<RefreshIcon />}
          onClick={() => window.location.reload()}
        >
          Refresh
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Lead</TableCell>
              <TableCell>Company</TableCell>
              <TableCell>Contact</TableCell>
              <TableCell>State</TableCell>
              <TableCell>Progress</TableCell>
              <TableCell>Audit Grade</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {leads.map((lead) => {
              const auditReport = auditReports[lead.id];
              const leadState = leadStates[lead.id];
              const messages = outreachMessages[lead.id] || [];

              return (
                <TableRow key={lead.id}>
                  <TableCell>
                    <Box>
                      <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                        {lead.first_name} {lead.last_name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {lead.title}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body1">{lead.company}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {lead.website}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2">{lead.email}</Typography>
                      <Typography variant="body2">{lead.phone}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={leadState?.current_state || 'imported'}
                      size="small"
                      sx={{
                        bgcolor: getStateColor(leadState?.current_state),
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </TableCell>
                  <TableCell sx={{ minWidth: 200 }}>
                    {renderLeadProgress(lead.id)}
                  </TableCell>
                  <TableCell>
                    {auditReport ? (
                      <Chip
                        label={auditReport.grade}
                        sx={{
                          bgcolor: getGradeColor(auditReport.grade),
                          color: 'white',
                          fontWeight: 'bold'
                        }}
                      />
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No audit
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Stack direction="row" spacing={1}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleViewLead(lead)}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      {auditReport && !leadState?.outreach_generated && (
                        <Tooltip title="Generate Outreach">
                          <IconButton
                            size="small"
                            onClick={() => handleGenerateOutreach(lead.id)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                      {messages.length > 0 && (
                        <Badge badgeContent={messages.length} color="primary">
                          <EmailIcon fontSize="small" />
                        </Badge>
                      )}
                    </Stack>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Lead Detail Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        {selectedLead && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  {selectedLead.first_name} {selectedLead.last_name} - {selectedLead.company}
                </Typography>
                <Chip
                  label={leadStates[selectedLead.id]?.current_state || 'imported'}
                  sx={{
                    bgcolor: getStateColor(leadStates[selectedLead.id]?.current_state),
                    color: 'white',
                    fontWeight: 'bold'
                  }}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                <Tab label="Lead Info" />
                <Tab label="Audit Report" />
                <Tab label="Outreach Messages" />
                <Tab label="Activity Timeline" />
              </Tabs>

              <Box sx={{ mt: 3 }}>
                {/* Lead Info Tab */}
                {tabValue === 0 && (
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>Contact Information</Typography>
                          <Stack spacing={2}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                              <Typography>{selectedLead.email}</Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                              <Typography>{selectedLead.phone}</Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <BusinessIcon sx={{ mr: 1, color: 'text.secondary' }} />
                              <Typography>{selectedLead.company}</Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <WebsiteIcon sx={{ mr: 1, color: 'text.secondary' }} />
                              <Typography>{selectedLead.website}</Typography>
                            </Box>
                          </Stack>
                        </CardContent>
                      </Card>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>Progress Overview</Typography>
                          {renderLeadProgress(selectedLead.id)}
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                )}

                {/* Audit Report Tab */}
                {tabValue === 1 && (
                  <Box>
                    {auditReports[selectedLead.id] ? (
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={4}>
                          <Card>
                            <CardContent sx={{ textAlign: 'center' }}>
                              <Typography variant="h6" gutterBottom>Overall Grade</Typography>
                              <Typography
                                variant="h2"
                                sx={{
                                  fontWeight: 'bold',
                                  color: getGradeColor(auditReports[selectedLead.id].grade)
                                }}
                              >
                                {auditReports[selectedLead.id].grade}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {auditReports[selectedLead.id].total_score}/125 points
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <Card>
                            <CardContent sx={{ textAlign: 'center' }}>
                              <Typography variant="h6" gutterBottom>Response Time</Typography>
                              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                                {auditReports[selectedLead.id].response_time_human}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                via {auditReports[selectedLead.id].response_channel}
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <Card>
                            <CardContent sx={{ textAlign: 'center' }}>
                              <Typography variant="h6" gutterBottom>Conversion Rate</Typography>
                              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#2196f3' }}>
                                {auditReports[selectedLead.id].conversion_rate_current}%
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Potential: 85%
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                        <Grid item xs={12}>
                          <Card>
                            <CardContent>
                              <Typography variant="h6" gutterBottom>Facebook Pixel Status</Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                {auditReports[selectedLead.id].facebook_pixel_detected ? (
                                  <>
                                    <CheckCircleIcon sx={{ color: '#4caf50', mr: 1 }} />
                                    <Typography>Facebook Pixel Detected</Typography>
                                  </>
                                ) : (
                                  <>
                                    <CancelIcon sx={{ color: '#f44336', mr: 1 }} />
                                    <Typography>No Facebook Pixel Found</Typography>
                                  </>
                                )}
                              </Box>
                            </CardContent>
                          </Card>
                        </Grid>
                        <Grid item xs={12}>
                          <Button
                            variant="contained"
                            startIcon={<ReportIcon />}
                            fullWidth
                            onClick={() => {
                              // TODO: Navigate to full audit report
                              console.log('View full audit report');
                            }}
                          >
                            View Full Audit Report
                          </Button>
                        </Grid>
                      </Grid>
                    ) : (
                      <Alert severity="info">
                        No audit report available for this lead yet.
                      </Alert>
                    )}
                  </Box>
                )}

                {/* Outreach Messages Tab */}
                {tabValue === 2 && (
                  <Box>
                    {outreachMessages[selectedLead.id] && outreachMessages[selectedLead.id].length > 0 ? (
                      <Box>
                        {/* Group messages by type and show sequences */}
                        {['email', 'linkedin'].map((messageType) => {
                          const typeMessages = outreachMessages[selectedLead.id]
                            .filter(msg => msg.message_type === messageType)
                            .sort((a, b) => a.sequence_position - b.sequence_position);

                          if (typeMessages.length === 0) return null;

                          return (
                            <Box key={messageType} sx={{ mb: 4 }}>
                              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                                {messageType === 'email' ? (
                                  <EmailIcon sx={{ mr: 1, color: '#1976d2' }} />
                                ) : (
                                  <LinkedInIcon sx={{ mr: 1, color: '#0077b5' }} />
                                )}
                                {messageType === 'email' ? 'Email Sequence' : 'LinkedIn Sequence'} ({typeMessages.length} messages)
                              </Typography>

                              <Grid container spacing={2}>
                                {typeMessages.map((message, index) => (
                                  <Grid item xs={12} key={message.id}>
                                    <Card sx={{
                                      border: message.sequence_position === 1 ? '2px solid #1976d2' : '1px solid #e0e0e0',
                                      position: 'relative'
                                    }}>
                                      <CardContent>
                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                            <Chip
                                              label={`#${message.sequence_position}`}
                                              size="small"
                                              sx={{
                                                mr: 1,
                                                bgcolor: '#1976d2',
                                                color: 'white',
                                                fontWeight: 'bold'
                                              }}
                                            />
                                            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                              {message.template_type?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                            </Typography>
                                            {message.send_delay_days > 0 && (
                                              <Chip
                                                label={`Send in ${message.send_delay_days} days`}
                                                size="small"
                                                variant="outlined"
                                                sx={{ ml: 1 }}
                                              />
                                            )}
                                          </Box>
                                          <Chip
                                            label={message.status}
                                            size="small"
                                            color={message.status === 'sent' ? 'success' : 'default'}
                                            sx={{ textTransform: 'capitalize' }}
                                          />
                                        </Box>

                                        {message.subject_line && (
                                          <Box sx={{ mb: 2 }}>
                                            <Typography variant="body2" color="text.secondary">Subject:</Typography>
                                            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                              {message.subject_line}
                                            </Typography>
                                          </Box>
                                        )}

                                        <Box sx={{ mb: 2 }}>
                                          <Typography variant="body2" color="text.secondary">Message:</Typography>
                                          <Paper sx={{ p: 2, bgcolor: '#f5f5f5', mt: 1 }}>
                                            <Typography variant="body1" sx={{ whiteSpace: 'pre-line' }}>
                                              {message.message_body}
                                            </Typography>
                                          </Paper>
                                        </Box>

                                        {message.template_focus && (
                                          <Box sx={{ mb: 2 }}>
                                            <Chip
                                              label={message.template_focus.replace('_', ' ')}
                                              size="small"
                                              variant="outlined"
                                              sx={{ textTransform: 'capitalize' }}
                                            />
                                          </Box>
                                        )}

                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                          <Typography variant="body2" color="text.secondary">
                                            Created: {new Date(message.created_at).toLocaleString()}
                                            {message.sent_at && (
                                              <span> • Sent: {new Date(message.sent_at).toLocaleString()}</span>
                                            )}
                                          </Typography>
                                          {message.status === 'draft' && (
                                            <Button
                                              variant="contained"
                                              size="small"
                                              startIcon={<SendIcon />}
                                              onClick={() => handleSendMessage(message.id)}
                                            >
                                              Send
                                            </Button>
                                          )}
                                        </Box>
                                      </CardContent>
                                    </Card>
                                  </Grid>
                                ))}
                              </Grid>
                            </Box>
                          );
                        })}
                      </Box>
                    ) : (
                      <Box sx={{ textAlign: 'center', py: 4 }}>
                        <Alert severity="info" sx={{ mb: 2 }}>
                          No outreach messages generated for this lead yet.
                        </Alert>
                        {auditReports[selectedLead.id] && (
                          <Button
                            variant="contained"
                            startIcon={<EditIcon />}
                            onClick={() => handleGenerateOutreach(selectedLead.id)}
                          >
                            Generate Outreach Messages
                          </Button>
                        )}
                      </Box>
                    )}
                  </Box>
                )}

                {/* Activity Timeline Tab */}
                {tabValue === 3 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>Activity Timeline</Typography>
                    <Stack spacing={2}>
                      <Card>
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <BusinessIcon sx={{ mr: 1, color: '#2196f3' }} />
                            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                              Lead Created
                            </Typography>
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            {new Date(selectedLead.created_at).toLocaleString()}
                          </Typography>
                        </CardContent>
                      </Card>

                      {leadStates[selectedLead.id]?.form_submission_completed && (
                        <Card>
                          <CardContent>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <SendIcon sx={{ mr: 1, color: '#ff9800' }} />
                              <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                Form Submitted
                              </Typography>
                            </Box>
                            <Typography variant="body2" color="text.secondary">
                              Form submission completed
                            </Typography>
                          </CardContent>
                        </Card>
                      )}

                      {auditReports[selectedLead.id] && (
                        <Card>
                          <CardContent>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <ReportIcon sx={{ mr: 1, color: '#9c27b0' }} />
                              <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                Audit Completed
                              </Typography>
                            </Box>
                            <Typography variant="body2" color="text.secondary">
                              Grade: {auditReports[selectedLead.id].grade} •
                              Response: {auditReports[selectedLead.id].response_time_human}
                            </Typography>
                          </CardContent>
                        </Card>
                      )}

                      {leadStates[selectedLead.id]?.outreach_generated && (
                        <Card>
                          <CardContent>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <EditIcon sx={{ mr: 1, color: '#3f51b5' }} />
                              <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                Outreach Generated
                              </Typography>
                            </Box>
                            <Typography variant="body2" color="text.secondary">
                              {outreachMessages[selectedLead.id]?.length || 0} messages created
                            </Typography>
                          </CardContent>
                        </Card>
                      )}

                      {leadStates[selectedLead.id]?.outreach_sent && (
                        <Card>
                          <CardContent>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <EmailIcon sx={{ mr: 1, color: '#4caf50' }} />
                              <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                Outreach Sent
                              </Typography>
                            </Box>
                            <Typography variant="body2" color="text.secondary">
                              Messages sent to prospect
                            </Typography>
                          </CardContent>
                        </Card>
                      )}
                    </Stack>
                  </Box>
                )}
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDialogOpen(false)}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default LeadManagement;
