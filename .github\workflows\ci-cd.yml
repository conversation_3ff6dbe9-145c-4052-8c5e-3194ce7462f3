name: CI/CD Pipeline

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]

jobs:
  test-backend:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [form-discovery, form-analysis, lead-management, response-ingestion, conversation-management, ai-orchestrator, analytics]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r services/requirements.txt
          if [ -f services/${{ matrix.service }}/requirements.txt ]; then
            pip install -r services/${{ matrix.service }}/requirements.txt
          fi
      - name: Run tests
        run: |
          cd services/${{ matrix.service }}
          pytest

  test-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: |
          cd frontend
          npm ci
      - name: Run tests
        run: |
          cd frontend
          npm test

  build-and-push:
    needs: [test-backend, test-frontend]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev')
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [gateway, form-discovery, form-analysis, lead-management, response-ingestion, conversation-management, ai-orchestrator, analytics, frontend]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: ./${{ matrix.service == 'gateway' && 'gateway' || matrix.service == 'frontend' && 'frontend' || format('services/{0}', matrix.service) }}
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/leadgen-${{ matrix.service }}:${{ github.ref == 'refs/heads/main' && 'latest' || 'dev' }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Kubernetes
        uses: azure/k8s-set-context@v3
        with:
          kubeconfig: ${{ secrets.KUBE_CONFIG }}
      - name: Deploy to staging
        if: github.ref == 'refs/heads/dev'
        run: |
          cd infra/k8s
          kubectl apply -f staging/
      - name: Deploy to production
        if: github.ref == 'refs/heads/main'
        run: |
          cd infra/k8s
          kubectl apply -f production/
