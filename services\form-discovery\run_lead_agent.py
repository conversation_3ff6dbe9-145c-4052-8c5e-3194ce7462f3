#!/usr/bin/env python
"""
Lead Form Agent Runner - Bulk Processing Script
----------------------------------------------
Loads a CSV of domain leads, processes each with the <PERSON><PERSON><PERSON>h agent,
and maintains detailed audit logs in real-time.
"""
import asyncio
import csv
import datetime
import os
import time
import pandas as pd
from typing import Dict, List, Any, Optional
from lead_form_agent import run_lead_form_agent_async

# Configuration
CSV_INPUT_DIR = "leads"
AUDIT_OUTPUT_DIR = "form_submissions"
DEFAULT_LEAD_DATA = {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "************",
    "message": "I'm interested in learning more about your roofing services and would like to schedule a free estimate."
}

# Ensure output directories exist
os.makedirs(CSV_INPUT_DIR, exist_ok=True)
os.makedirs(AUDIT_OUTPUT_DIR, exist_ok=True)
FAILED_OUTPUT_FILE = os.path.join(AUDIT_OUTPUT_DIR, "failed_domains.csv")

import asyncio
try:
    from tqdm import tqdm
    HAS_TQDM = True
except ImportError:
    HAS_TQDM = False

class LeadFormRunner:
    """Orchestrates the processing of multiple domains with the Lead Form Agent."""
    
    def __init__(self, input_csv: str, output_csv: str, lead_data: Dict[str, str], concurrency: int = 10, batch_size: int = 10):
        """
        Initialize the runner.
        
        Args:
            input_csv: Path to CSV with domains to process
            output_csv: Path to output audit CSV
            lead_data: Default lead data to use for form filling
        """
        self.input_csv = input_csv
        self.output_csv = output_csv
        self.lead_data = lead_data
        self.results = []
        self.domains_processed = 0
        self.form_submissions = 0
        self.concurrency = concurrency
        self.batch_size = batch_size
        
        # Initialize output CSV if it doesn't exist
        if not os.path.exists(output_csv):
            with open(output_csv, 'w', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=[
                    'timestamp', 'url', 'final_url', 'form_id', 
                    'fields_filled', 'confidence_score', 'submitted',
                    'error', 'form_type', 'processing_time'
                ])
                writer.writeheader()
    
    async def process_domain(self, domain: str) -> Dict[str, Any]:
        """
        Process a single domain with the Lead Form Agent.
        
        Args:
            domain: URL to process
            
        Returns:
            Dict with audit results
        """
        # Ensure URL has protocol
        if not domain.startswith(('http://', 'https://')):
            url = f"http://{domain}"
        else:
            url = domain
            
        # print(f"\n[PROCESSING] {url}")
        start_time = time.time()
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Initialize audit record
        audit = {
            'timestamp': timestamp,
            'url': url,
            'final_url': url,
            'form_id': None,
            'fields_filled': 0,
            'confidence_score': 0,
            'submitted': False,
            'error': None,
            'form_type': None,
            'processing_time': 0
        }
        
        try:
            # Call the LangGraph agent
            initial_state = {
                "url": url, 
                "lead_data": self.lead_data, 
                "llm_decision": None, 
                "fill_result": None, 
                "error": None
            }
            
            # Execute the agent
            result_state = await run_lead_form_agent_async(url, self.lead_data)
            processing_time = time.time() - start_time
            
            # Update audit with results
            if result_state.get("error"):
                audit['error'] = result_state["error"]
                # print(f"[ERROR] {audit['error']}")
                # Track failed domain
                with open(FAILED_OUTPUT_FILE, 'a') as f:
                    f.write(url + '\n')
            else:
                # Extract data from successful run
                if 'submission_details' in result_state.get('fill_result', {}):
                    details = result_state['fill_result']['submission_details']
                    audit['final_url'] = details.get('url', url)
                    audit['form_id'] = details.get('form_id', 'unknown')
                    audit['fields_filled'] = len(details.get('fields_filled', []))
                    audit['submitted'] = details.get('success', False)
                    audit['form_type'] = 'synthetic' if 'synthetic' in str(details.get('form_id', '')) else 'real'
                    # Estimate confidence from number of fields and matches
                    match_quality = sum(1 for f in details.get('fields_filled', []) 
                                     if f.get('matched_by') in ['name', 'id', 'label_for'])
                    audit['confidence_score'] = min(100, match_quality * 20 + audit['fields_filled'] * 10)
                    
                    if audit['submitted']:
                        # print(f"[SUCCESS] Form submitted at {audit['final_url']}")
                        # print(f"[DETAILS] Filled {audit['fields_filled']} fields with confidence {audit['confidence_score']}%")
                        self.form_submissions += 1
                    else:
                        # print(f"[PARTIAL] Form found but not submitted at {audit['final_url']}")
                        pass
            
            audit['processing_time'] = round(processing_time, 2)
        except Exception as e:
            audit['error'] = f"Processing error: {str(e)}"
            audit['processing_time'] = round(time.time() - start_time, 2)
            # print(f"[EXCEPTION] {audit['error']}")
            pass
        
        # Write audit to CSV immediately
        with open(self.output_csv, 'a', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=audit.keys())
            writer.writerow(audit)
        
        self.domains_processed += 1
        return audit
    
    async def run(self):
        """Process all domains in the input CSV with concurrency and batching."""
        # print(f"[START] Processing domains from {self.input_csv}")
        start_time_overall = time.time()
        
        try:
            # Load domains from CSV
            if self.input_csv.endswith('.csv'):
                df = pd.read_csv(self.input_csv)
                url_column = next((col for col in df.columns if col.lower() in 
                                ['url', 'domain', 'website', 'site', 'web']), None)
                if not url_column:
                    if len(df.columns) > 0:
                        url_column = df.columns[0]
                    else:
                        raise ValueError("CSV file has no columns")
                domains = df[url_column].tolist()
            else:
                with open(self.input_csv, 'r') as f:
                    domains = [line.strip() for line in f if line.strip()]
        
            # print(f"[INFO] Found {len(domains)} domains to process")
            
            # Auto-split large batches
            if len(domains) > 1000:
                # print("[INFO] Splitting input into smaller batches")
                chunk_size = 1000
                domain_chunks = [domains[i:i+chunk_size] for i in range(0, len(domains), chunk_size)]
            else:
                domain_chunks = [domains]

            chunk_idx = 0
            for chunk in domain_chunks:
                chunk_idx += 1
                # print(f"[INFO] Processing chunk {chunk_idx}/{len(domain_chunks)} [{len(chunk)} domains]")

                # Create concurrency control and progress tracking
                semaphore = asyncio.Semaphore(self.concurrency)
                batch = []
                total = len(chunk)
                progress = tqdm(total=total, desc=f"Chunk {chunk_idx}", ncols=80) if HAS_TQDM else None
                
                # Define the worker function
                async def worker(domain, idx):
                    async with semaphore:
                        # Process the domain
                        res = await self.process_domain(domain)
                        batch.append(res)
                        
                        # Update progress
                        if progress:
                            progress.update(1)
                        elif (idx+1) % 10 == 0 or idx == total-1:
                            pass  # print(f"[PROGRESS] {idx+1}/{total} domains processed")
                            
                        # Write batch if it reaches threshold
                        if len(batch) >= self.batch_size:
                            self._write_batch(batch)
                            batch.clear()
                
                # Create and run tasks
                tasks = [worker(domain, i) for i, domain in enumerate(chunk)]
                await asyncio.gather(*tasks)
                
                # Cleanup
                if batch:
                    self._write_batch(batch)
                if progress:
                    progress.close()

        except Exception as e:
            # print(f"[CRITICAL ERROR] {str(e)}")
            pass
        finally:
            total_time = time.time() - start_time_overall
            # print(f"\n[SUMMARY] Processing complete")
            # print(f"  - Domains processed: {self.domains_processed}")
            # print(f"  - Successful submissions: {self.form_submissions}")
            # print(f"  - Success rate: {self.form_submissions/max(1, self.domains_processed):.1%}")
            # print(f"  - Audit log: {self.output_csv}")
            # print(f"  - Failed domains log: {FAILED_OUTPUT_FILE}")
            # print(f"  - Total processing time: {round(total_time / 60, 2)} minutes")
            # print(f"  - Avg time per domain: {round(total_time / max(1, self.domains_processed), 2)}s")
            pass

    def _write_batch(self, batch):
        """Write a batch of audit records to the CSV."""
        if not batch:
            return
        with open(self.output_csv, 'a', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=batch[0].keys())
            for row in batch:
                writer.writerow(row)

def create_sample_input_csv():
    """Create a sample input CSV with some roof company domains."""
    sample_file = os.path.join(CSV_INPUT_DIR, "sample_roofers.csv")
    if not os.path.exists(sample_file):
        with open(sample_file, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Domain', 'Company', 'State'])
            writer.writerow(['www.hahnroofing.com', 'Hahn Roofing', 'CA'])
            writer.writerow(['www.valleyroofing.org', 'Valley Roofing', 'AZ'])
            writer.writerow(['www.globalroofinggroup.com', 'Global Roofing Group', 'TX'])
            writer.writerow(['www.aaaroofingco.com', 'AAA Roofing', 'CO'])
            writer.writerow(['www.abetterchoiceinc.com', 'A Better Choice', 'FL'])
            writer.writerow(['www.advancedroofingsystem.com', 'Advanced Roofing', 'GA'])
        # print(f"Created sample input file: {sample_file}")
    return sample_file

async def main():
    """Main execution function."""
    # Create a sample input file if needed
    sample_csv = create_sample_input_csv()
    
    # Set up output file
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_csv = os.path.join(AUDIT_OUTPUT_DIR, f"lead_form_results_{timestamp}.csv")
    
    # Create and run the Lead Form Runner
    runner = LeadFormRunner(
        input_csv=sample_csv,
        output_csv=output_csv,
        lead_data=DEFAULT_LEAD_DATA
    )
    
    await runner.run()

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Bulk Lead Form Agent Runner")
    parser.add_argument('--input', type=str, default=os.path.join(CSV_INPUT_DIR, "sample_roofers.csv"), help='Input CSV file')
    parser.add_argument('--output', type=str, default=os.path.join(AUDIT_OUTPUT_DIR, "audit.csv"), help='Output audit CSV file')
    parser.add_argument('--concurrency', type=int, default=10, help='How many domains to process in parallel')
    parser.add_argument('--batch-size', type=int, default=10, help='How many results to write at once')
    args = parser.parse_args()

    def main():
        runner = LeadFormRunner(args.input, args.output, DEFAULT_LEAD_DATA, concurrency=args.concurrency, batch_size=args.batch_size)
        asyncio.run(runner.run())

    main()
