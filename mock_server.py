from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import uuid
import json
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
import datetime

app = FastAPI(title="Mock Lead Generation API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock data storage
leads = []
form_submissions = []
response_audits = []
settings = []

# Models
class Lead(BaseModel):
    id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    company: Optional[str] = None
    website: Optional[str] = None
    created_at: datetime.datetime
    updated_at: datetime.datetime

class LeadCreate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    company: Optional[str] = None
    website: Optional[str] = None

class FormSubmission(BaseModel):
    id: str
    lead_id: str
    website: str
    form_id: Optional[str] = None
    status: str
    submitted_at: datetime.datetime
    fields_submitted: Dict[str, str]

class FormSubmissionCreate(BaseModel):
    lead_id: str
    website: str
    form_id: Optional[str] = None
    fields_submitted: Dict[str, str]

class ResponseAudit(BaseModel):
    id: str
    submission_id: str
    response_time_seconds: Optional[float] = None
    response_channel: Optional[str] = None
    score: Optional[int] = None
    created_at: datetime.datetime

# API Endpoints
@app.get("/")
async def root():
    return {"message": "Mock Lead Generation API"}

@app.get("/api/leads", response_model=List[Lead])
async def get_leads():
    return leads

@app.post("/api/leads", response_model=Lead)
async def create_lead(lead_data: LeadCreate):
    now = datetime.datetime.now()
    new_lead = Lead(
        id=str(uuid.uuid4()),
        first_name=lead_data.first_name,
        last_name=lead_data.last_name,
        email=lead_data.email,
        phone=lead_data.phone,
        company=lead_data.company,
        website=lead_data.website,
        created_at=now,
        updated_at=now
    )
    leads.append(new_lead)
    return new_lead

@app.get("/api/leads/{lead_id}", response_model=Lead)
async def get_lead(lead_id: str):
    for lead in leads:
        if lead.id == lead_id:
            return lead
    raise HTTPException(status_code=404, detail="Lead not found")

@app.get("/api/form-submissions", response_model=List[FormSubmission])
async def get_form_submissions():
    return form_submissions

@app.post("/api/form-submissions", response_model=FormSubmission)
async def create_form_submission(submission_data: FormSubmissionCreate):
    # Check if lead exists
    lead_exists = False
    for lead in leads:
        if lead.id == submission_data.lead_id:
            lead_exists = True
            break

    if not lead_exists:
        raise HTTPException(status_code=404, detail="Lead not found")

    new_submission = FormSubmission(
        id=str(uuid.uuid4()),
        lead_id=submission_data.lead_id,
        website=submission_data.website,
        form_id=submission_data.form_id,
        status="submitted",
        submitted_at=datetime.datetime.now(),
        fields_submitted=submission_data.fields_submitted
    )
    form_submissions.append(new_submission)

    # Create a mock response audit
    response_time = round(30 + 120 * (len(response_audits) % 5) / 4, 1)  # Between 30 and 150 seconds
    channels = ["email", "phone", "sms"]
    channel = channels[len(response_audits) % 3]

    # Calculate score based on response time and channel
    score = 100 - min(int(response_time / 3), 100)  # Time-based score
    if channel == "phone":
        score += 25
    elif channel == "sms":
        score += 15
    elif channel == "email":
        score += 5

    score = min(score, 100)  # Cap at 100

    new_audit = ResponseAudit(
        id=str(uuid.uuid4()),
        submission_id=new_submission.id,
        response_time_seconds=response_time,
        response_channel=channel,
        score=score,
        created_at=datetime.datetime.now() + datetime.timedelta(seconds=response_time)
    )
    response_audits.append(new_audit)

    return new_submission

@app.get("/api/response-audits", response_model=List[ResponseAudit])
async def get_response_audits():
    return response_audits

@app.get("/api/response-audits/{submission_id}", response_model=ResponseAudit)
async def get_response_audit(submission_id: str):
    for audit in response_audits:
        if audit.submission_id == submission_id:
            return audit
    raise HTTPException(status_code=404, detail="Response audit not found")

# Add some sample data
def add_sample_data():
    # Add sample leads
    sample_leads = [
        LeadCreate(first_name="John", last_name="Doe", email="<EMAIL>", phone="************", company="Acme Inc", website="acme.com"),
        LeadCreate(first_name="Jane", last_name="Smith", email="<EMAIL>", phone="************", company="XYZ Corp", website="xyzcorp.com"),
        LeadCreate(first_name="Bob", last_name="Johnson", email="<EMAIL>", phone="************", company="ABC Ltd", website="abcltd.com"),
    ]

    for lead_data in sample_leads:
        now = datetime.datetime.now()
        new_lead = Lead(
            id=str(uuid.uuid4()),
            first_name=lead_data.first_name,
            last_name=lead_data.last_name,
            email=lead_data.email,
            phone=lead_data.phone,
            company=lead_data.company,
            website=lead_data.website,
            created_at=now,
            updated_at=now
        )
        leads.append(new_lead)

    # Add sample form submissions and response audits
    for lead in leads:
        submission_data = FormSubmissionCreate(
            lead_id=lead.id,
            website=f"https://www.{lead.website}/contact",
            form_id=f"contact-{lead.id[:8]}",
            fields_submitted={
                "name": f"{lead.first_name} {lead.last_name}",
                "email": lead.email,
                "phone": lead.phone,
                "message": "I'm interested in your services."
            }
        )

        new_submission = FormSubmission(
            id=str(uuid.uuid4()),
            lead_id=submission_data.lead_id,
            website=submission_data.website,
            form_id=submission_data.form_id,
            status="submitted",
            submitted_at=datetime.datetime.now() - datetime.timedelta(days=1),
            fields_submitted=submission_data.fields_submitted
        )
        form_submissions.append(new_submission)

        # Create a mock response audit
        response_time = round(30 + 120 * (leads.index(lead) % 5) / 4, 1)  # Between 30 and 150 seconds
        channels = ["email", "phone", "sms"]
        channel = channels[leads.index(lead) % 3]

        # Calculate score based on response time and channel
        score = 100 - min(int(response_time / 3), 100)  # Time-based score
        if channel == "phone":
            score += 25
        elif channel == "sms":
            score += 15
        elif channel == "email":
            score += 5

        score = min(score, 100)  # Cap at 100

        new_audit = ResponseAudit(
            id=str(uuid.uuid4()),
            submission_id=new_submission.id,
            response_time_seconds=response_time,
            response_channel=channel,
            score=score,
            created_at=datetime.datetime.now() - datetime.timedelta(days=1) + datetime.timedelta(seconds=response_time)
        )
        response_audits.append(new_audit)

# Supabase-style REST API endpoints
@app.get("/rest/v1/leads")
async def supabase_get_leads(request: Request, response: Response):
    # Add Supabase-style headers
    response.headers["Content-Type"] = "application/json"
    response.headers["Content-Range"] = f"0-{len(leads)}/{len(leads)}"

    # Parse query parameters
    params = dict(request.query_params)

    # Handle select parameter (ignored in this mock)

    # Handle limit parameter
    limit = int(params.get("limit", "1000"))

    # Handle offset parameter
    offset = int(params.get("offset", "0"))

    # Handle order parameter (ignored in this mock)

    # Return the leads with pagination
    return leads[offset:offset+limit]

@app.post("/rest/v1/leads")
async def supabase_create_lead(request: Request, response: Response):
    # Parse the request body
    body = await request.json()

    # Create a new lead
    now = datetime.datetime.now()
    new_lead = Lead(
        id=str(uuid.uuid4()),
        first_name=body.get("first_name"),
        last_name=body.get("last_name"),
        email=body.get("email"),
        phone=body.get("phone"),
        company=body.get("company"),
        website=body.get("website"),
        created_at=now,
        updated_at=now
    )

    # Add the lead to the list
    leads.append(new_lead)

    # Return the created lead
    return [new_lead.dict()]

@app.get("/rest/v1/form_submissions")
async def supabase_get_form_submissions(request: Request, response: Response):
    # Add Supabase-style headers
    response.headers["Content-Type"] = "application/json"
    response.headers["Content-Range"] = f"0-{len(form_submissions)}/{len(form_submissions)}"

    # Parse query parameters
    params = dict(request.query_params)

    # Handle select parameter (ignored in this mock)

    # Handle limit parameter
    limit = int(params.get("limit", "1000"))

    # Handle offset parameter
    offset = int(params.get("offset", "0"))

    # Return the form submissions with pagination
    return form_submissions[offset:offset+limit]

@app.post("/rest/v1/form_submissions")
async def supabase_create_form_submission(request: Request, response: Response):
    # Parse the request body
    body = await request.json()

    # Create a new form submission
    new_submission = FormSubmission(
        id=str(uuid.uuid4()),
        lead_id=body.get("lead_id"),
        website=body.get("website"),
        form_id=body.get("form_id"),
        status="submitted",
        submitted_at=datetime.datetime.now(),
        fields_submitted=body.get("fields_submitted", {})
    )

    # Add the form submission to the list
    form_submissions.append(new_submission)

    # Create a mock response audit
    response_time = round(30 + 120 * (len(response_audits) % 5) / 4, 1)  # Between 30 and 150 seconds
    channels = ["email", "phone", "sms"]
    channel = channels[len(response_audits) % 3]

    # Calculate score based on response time and channel
    score = 100 - min(int(response_time / 3), 100)  # Time-based score
    if channel == "phone":
        score += 25
    elif channel == "sms":
        score += 15
    elif channel == "email":
        score += 5

    score = min(score, 100)  # Cap at 100

    new_audit = ResponseAudit(
        id=str(uuid.uuid4()),
        submission_id=new_submission.id,
        response_time_seconds=response_time,
        response_channel=channel,
        score=score,
        created_at=datetime.datetime.now() + datetime.timedelta(seconds=response_time)
    )
    response_audits.append(new_audit)

    # Return the created form submission
    return [new_submission.dict()]

@app.get("/rest/v1/settings")
async def supabase_get_settings(request: Request, response: Response):
    # Add Supabase-style headers
    response.headers["Content-Type"] = "application/json"
    response.headers["Content-Range"] = f"0-{len(settings)}/{len(settings)}"

    # Parse query parameters
    params = dict(request.query_params)

    # Handle eq parameter for key
    key = params.get("key", None)
    if key:
        # Find settings with matching key
        matching_settings = [s for s in settings if s.get("key") == key]
        return matching_settings

    # Return all settings
    return settings

@app.post("/rest/v1/settings")
async def supabase_create_settings(request: Request, response: Response):
    # Parse the request body
    body = await request.json()

    # Create new settings
    new_settings = {
        "id": str(uuid.uuid4()),
        "key": body.get("key"),
        "value": body.get("value"),
        "description": body.get("description", f"Settings for {body.get('key')}")
    }

    # Add the settings to the list
    settings.append(new_settings)

    # Return the created settings
    return [new_settings]

# Add sample settings
settings.append({
    "id": str(uuid.uuid4()),
    "key": "default_form_fields",
    "value": json.dumps({
        "name": True,
        "email": True,
        "phone": True,
        "company": False,
        "message": True
    }),
    "description": "Default form fields to fill out"
})

# Add sample data
add_sample_data()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
