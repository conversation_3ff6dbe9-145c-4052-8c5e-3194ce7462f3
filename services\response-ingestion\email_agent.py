"""
Email Agent Module

This module provides functionality to monitor email responses through IMAP
or Gmail API integration.
"""
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("python-dotenv not installed; skipping .env loading.")

import imaplib
import email
from email.header import decode_header
import os
import time
from typing import Dict, List, Optional, Any, Tuple
from __init__ import ResponseState
from lead_context_store import LeadContextStore

class EmailAgent:
    """
    Email Agent for monitoring incoming emails via IMAP or Gmail API.
    """
    
    def __init__(self, 
                 username: str,
                 password: str,
                 imap_server: str = "imap.gmail.com",
                 imap_port: int = 993):
        """
        Initialize the email agent (IMAP only).
        
        Args:
            username: Email username or address
            password: Email password or app password for IMAP
            imap_server: IMAP server address
            imap_port: IMAP server port
        """
        self.username = username
        self.password = password
        self.imap_server = imap_server
        self.imap_port = imap_port
        self.mail = None
        
    def connect_imap(self) -> bool:
        """
        Connect to the IMAP server.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            self.mail.login(self.username, self.password)
            return True
        except Exception as e:
            print(f"Error connecting to IMAP server: {e}")
            return False
            
    def connect_gmail_api(self) -> bool:
        """
        Connect to Gmail API.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        if not self.use_gmail_api:
            return False
            
        try:
            # This is a placeholder for Gmail API integration
            # Actual implementation would require google-api-python-client
            # from googleapiclient.discovery import build
            # from google_auth_oauthlib.flow import InstalledAppFlow
            # from google.auth.transport.requests import Request
            
            # SCOPES = ['https://www.googleapis.com/auth/gmail.readonly']
            # creds = None
            # # Load or refresh credentials
            # self.gmail_service = build('gmail', 'v1', credentials=creds)
            print("Gmail API integration is a placeholder. Implement with google-api-python-client")
            return True
        except Exception as e:
            print(f"Error connecting to Gmail API: {e}")
            return False
    
    def fetch_emails(self, 
                     mailbox: str = "INBOX", 
                     search_criteria: str = "UNSEEN",
                     max_emails: int = 10,
                     lead_store: Optional[LeadContextStore] = None) -> List[ResponseState]:
        """
        Fetch emails from the mailbox based on search criteria and format as ResponseState.
        Args:
            mailbox: Name of the mailbox to search
            search_criteria: IMAP search criteria
            max_emails: Maximum number of emails to fetch
            lead_store: Optional LeadContextStore for lead enrichment
        Returns:
            List of ResponseState dicts
        """

        if not self.mail:
            if not self.connect_imap():
                return []
        
        if lead_store is None:
            lead_store = LeadContextStore()
        try:
            self.mail.select(mailbox)
            status, message_ids = self.mail.search(None, search_criteria)
            if status != 'OK':
                print(f"Error searching for emails: {status}")
                return []
            message_id_list = message_ids[0].split()
            responses = []
            for i in range(min(max_emails, len(message_id_list))):
                msg_id = message_id_list[-(i+1)]
                email_data = self._fetch_email_by_id(msg_id)
                if isinstance(email_data, dict):
                    from_email = email_data.get("from")
                    matched_lead = lead_store.find_by_email(from_email) if from_email else None
                    response: ResponseState = {
                        "id": email_data.get("id", f"generated_{time.time()}"),  # Add ID directly from source
                        "message": email_data.get("body", ""),
                        "from_email": from_email,
                        "from_phone": None,
                        "channel": "email",
                        "received_at": email_data.get("date", ""),
                        "matched_lead": matched_lead,
                        "inferred_match_score": None,
                        "opportunity_score": None,
                        "error": None
                    }
                    responses.append(response)
                else:
                    print(f"[WARN] Malformed email, wrapping in fallback response: {repr(email_data)}")
                    responses.append({
                        "id": f"malformed_{time.time()}",  # Generate a unique ID
                        "message": str(email_data),
                        "from_email": None,
                        "from_phone": None,
                        "channel": "email",
                        "received_at": None,
                        "matched_lead": None,
                        "inferred_match_score": None,
                        "opportunity_score": None,
                        "error": f"Malformed email: {email_data!r}"
                    })
            return responses
        except Exception as e:
            print(f"Error fetching emails: {e}")
            return []
    
    def _fetch_email_by_id(self, msg_id: bytes) -> Optional[Dict[str, Any]]:
        try:
            status, msg_data = self.mail.fetch(msg_id, '(RFC822)')
            if status != 'OK':
                return {"error": f"Fetch failed for msg_id {msg_id}: {status}"}
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)
            subject = decode_header(email_message["Subject"])[0][0]
            subject = subject.decode(decode_header(email_message["Subject"])[0][1] or "utf-8") if isinstance(subject, bytes) else subject
            from_ = decode_header(email_message.get("From", ""))[0][0]
            from_ = from_.decode(decode_header(email_message.get("From", ""))[0][1] or "utf-8") if isinstance(from_, bytes) else from_
            date_ = email_message.get("Date", "")
            body = ""
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_type() == "text/plain" and "attachment" not in str(part.get("Content-Disposition")):
                        body_bytes = part.get_payload(decode=True)
                        charset = part.get_content_charset() or "utf-8"
                        body = body_bytes.decode(charset, errors="ignore")
                        break
            else:
                body_bytes = email_message.get_payload(decode=True)
                charset = email_message.get_content_charset() or "utf-8"
                body = body_bytes.decode(charset, errors="ignore")
            return {
                "id": msg_id.decode(),
                "subject": subject,
                "from": from_,
                "date": date_,
                "body": body
            }
        except Exception as e:
            return {"error": f"Failed to parse email {msg_id}: {str(e)}"}
        """
        Fetch and parse a single email by its ID.
        
        Args:
            msg_id: Message ID
            
        Returns:
            Dictionary containing email data or None if error
        """
        try:
            status, msg_data = self.mail.fetch(msg_id, '(RFC822)')
            if status != 'OK':
                return None
            
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)
            
            # Get header info
            subject, encoding = decode_header(email_message["Subject"])[0]
            if isinstance(subject, bytes):
                subject = subject.decode(encoding if encoding else "utf-8")
            
            from_, encoding = decode_header(email_message.get("From", ""))[0]
            if isinstance(from_, bytes):
                from_ = from_.decode(encoding if encoding else "utf-8")
            
            date_ = email_message.get("Date", "")
            
            # Get body content
            body = ""
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    
                    if "attachment" not in content_disposition:
                        if content_type == "text/plain":
                            body_bytes = part.get_payload(decode=True)
                            charset = part.get_content_charset() or "utf-8"
                            body = body_bytes.decode(charset)
                            break
            else:
                body_bytes = email_message.get_payload(decode=True)
                charset = email_message.get_content_charset() or "utf-8"
                body = body_bytes.decode(charset)
            
            return {
                "id": msg_id.decode(),
                "subject": subject,
                "from": from_,
                "date": date_,
                "body": body
            }
        except Exception as e:
            print(f"Error fetching email {msg_id}: {e}")
            return {"error": str(e), "raw": str(msg_id)}
    

        """
        Fetch emails using Gmail API.
        
        Args:
            max_emails: Maximum number of emails to fetch
            
        Returns:
            List of email data dictionaries
        """
        # Placeholder for Gmail API implementation
        # Example implementation:
        # results = self.gmail_service.users().messages().list(
        #     userId='me', labelIds=['INBOX'], maxResults=max_emails
        # ).execute()
        # messages = results.get('messages', [])
        # emails = []
        # for message in messages:
        #     msg = self.gmail_service.users().messages().get(
        #         userId='me', id=message['id'], format='full'
        #     ).execute()
        #     # Process message contents
        #     # ...
        #     emails.append(email_data)
        # return emails
        return []
    
    def mark_as_read(self, msg_id: str) -> bool:
        """
        Mark an email as read.
        
        Args:
            msg_id: Message ID
            
        Returns:
            bool: Success status
        """
        if self.use_gmail_api:
            # Placeholder for Gmail API implementation
            return False
        
        if not self.mail:
            if not self.connect_imap():
                return False
        
        try:
            self.mail.store(msg_id.encode(), '+FLAGS', '\\Seen')
            return True
        except Exception as e:
            print(f"Error marking message as read: {e}")
            return False
    
    def close(self):
        """Close the connection to the email server."""
        if self.mail:
            try:
                self.mail.close()
                self.mail.logout()
            except:
                pass
            finally:
                self.mail = None

    def monitor_emails(self, 
                       callback, 
                       interval: int = 60, 
                       mailbox: str = "INBOX",
                       search_criteria: str = "UNSEEN"):
        """
        Continuously monitor emails and process new ones using the callback.
        
        Args:
            callback: Function to call with each new email
            interval: Polling interval in seconds
            mailbox: Mailbox to monitor
            search_criteria: Search criteria for emails
        """
        print("[DEBUG] Starting monitor_emails function with updated type checking")
        processed_ids = set()
        
        while True:
            try:
                emails = self.fetch_emails(mailbox, search_criteria)
                print(f"[DEBUG] Fetched {len(emails)} emails")
                
                # Process each email with robust type checking
                for i, item in enumerate(emails):
                    try:
                        # CRITICAL TYPE CHECK: Skip anything that's not a dict
                        if not isinstance(item, dict):
                            print(f"[WARN] Item #{i} is not a dict: {type(item).__name__}, value: {repr(item)[:100]}")
                            try:
                                with open("malformed_emails.log", "a", encoding="utf-8") as f:
                                    f.write(f"{repr(item)}\n")
                            except Exception as log_exc:
                                print(f"[ERROR] Failed to log malformed email: {log_exc}")
                            continue
                            
                        # Safe dict access
                        email_id = None
                        if "id" in item:
                            email_id = item["id"]
                        elif "message_id" in item:
                            email_id = item["message_id"]
                        elif "message" in item and isinstance(item["message"], dict) and "id" in item["message"]:
                            email_id = item["message"]["id"]
                        
                        if not email_id:
                            print(f"[WARN] No ID found in email: {repr(item)[:100]}")
                            continue
                            
                        if email_id not in processed_ids:
                            print(f"[INFO] Processing new email with ID: {email_id}")
                            callback(item)
                            processed_ids.add(email_id)
                    except Exception as item_exc:
                        print(f"[ERROR] Error processing item #{i}: {item_exc}")
                        continue
                        
            except Exception as e:
                print(f"[ERROR] Error in monitor_emails loop: {e}")
            
            print(f"[INFO] Sleeping for {interval} seconds...")
            time.sleep(interval)


# Example usage
if __name__ == "__main__":
    import os
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("python-dotenv not installed; skipping .env loading.")

    # For security, you can set these as environment variables:
    #   set EMAIL_USERNAME=<EMAIL>
    #   set EMAIL_APP_PASSWORD=lium engf wbft gihk
    USERNAME = os.getenv("EMAIL_USERNAME", "<EMAIL>")
    PASSWORD = os.getenv("EMAIL_APP_PASSWORD", "lium engf wbft gihk")

    from match_infer_graph import MatchInferGraph
    import json

    agent = EmailAgent(username=USERNAME, password=PASSWORD)
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    match_infer_agent = MatchInferGraph(openai_api_key=OPENAI_API_KEY)

    def process_email(response: ResponseState):
        print(f"From: {response.get('from_email')}")
        print(f"Received: {response.get('received_at')}")
        result = match_infer_agent.process_message(response)
        matched_lead = result.get('matched_lead') or (result.get('message', {}) if isinstance(result.get('message'), dict) else {}).get('matched_lead')
        if matched_lead:
            print("Matched Lead:")
            print(f"  Name: {matched_lead.get('first_name', '')} {matched_lead.get('last_name', '')}")
            print(f"  Email: {matched_lead.get('email', '')}")
            print(f"  Phone: {matched_lead.get('phone', '')}")
            print(f"  Domain: {matched_lead.get('domain', '')}")
            print(f"  Company: {matched_lead.get('company', '')}")
        else:
            print("Matched Lead: None")
        if result.get('error'):
            print(f"Error: {result.get('error')}")
        print(f"Classification: {result.get('classification', {}).get('intent', 'N/A')}")
        print(f"Response Explanation: {result.get('response_explanation', '')}")
        print()

    def retroactive_process_all_emails():
        print("\n--- Retroactive Processing: Fetching ALL emails in mailbox ---\n")
        all_responses = agent.fetch_emails(search_criteria="ALL", max_emails=1000)  # Adjust max_emails as needed
        for response in all_responses:
            process_email(response)
        print(f"\nProcessed {len(all_responses)} emails retroactively.\n")

    try:
        mode = input("Choose mode: [1] Live monitoring (default), [2] Retroactive process all emails: ").strip()
        if mode == "2":
            retroactive_process_all_emails()
        else:
            # For testing, just fetch and process emails once
            responses = agent.fetch_emails()
            for response in responses:
                process_email(response)
            
            # For continuous monitoring, poll every 60 seconds
            print("\n--- Starting continuous inbox monitoring (Ctrl+C to stop) ---\n")
            agent.monitor_emails(process_email, interval=60)
    finally:
        agent.close()
