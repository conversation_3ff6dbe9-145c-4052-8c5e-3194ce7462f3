"""
Response Audit Module

This module provides functionality to generate audit reports for lead responses,
analyzing response times, channels, and effectiveness.
"""
import os
import json
import csv
import datetime
from typing import Dict, Any, List, Optional, Union, Tuple
import statistics
from collections import defaultdict

try:
    from supabase import create_client, Client
except ImportError:
    print("[WARN] supabase-py not installed. Please install with 'pip install supabase'.")
    create_client = None

class ResponseAudit:
    """
    Generates audit reports for lead responses.
    """
    
    def __init__(self, 
                 supabase_url: Optional[str] = None,
                 supabase_key: Optional[str] = None,
                 output_dir: str = "./reports"):
        """
        Initialize the response audit.
        
        Args:
            supabase_url: Supabase URL
            supabase_key: Supabase service role key
            output_dir: Directory for output reports
        """
        self.supabase_url = supabase_url or os.environ.get("SUPABASE_URL")
        self.supabase_key = supabase_key or os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
        self.output_dir = output_dir
        self.supabase = None
        
        # Initialize Supabase client
        if create_client and self.supabase_url and self.supabase_key:
            try:
                self.supabase = create_client(self.supabase_url, self.supabase_key)
                print(f"[INFO] Supabase client initialized: {self.supabase_url}")
            except Exception as e:
                print(f"[ERROR] Failed to initialize Supabase client: {e}")
        else:
            print("[WARN] Supabase client not initialized: Missing dependencies or credentials")
        
        # Create output directory if it doesn't exist
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def fetch_leads(self, 
                   start_date: Optional[datetime.datetime] = None,
                   end_date: Optional[datetime.datetime] = None,
                   limit: int = 1000) -> List[Dict[str, Any]]:
        """
        Fetch leads from Supabase.
        
        Args:
            start_date: Start date for filtering
            end_date: End date for filtering
            limit: Maximum number of leads to fetch
            
        Returns:
            List of leads
        """
        if not self.supabase:
            print("[ERROR] Supabase client not initialized")
            return []
        
        try:
            query = self.supabase.table("leads").select("*")
            
            # Apply date filters if provided
            if start_date:
                query = query.gte("created_at", start_date.isoformat())
            if end_date:
                query = query.lte("created_at", end_date.isoformat())
            
            # Apply limit
            query = query.limit(limit)
            
            # Execute query
            response = query.execute()
            
            # Extract data
            leads = response.data if hasattr(response, 'data') else response
            
            print(f"[INFO] Fetched {len(leads)} leads")
            
            return leads
        except Exception as e:
            print(f"[ERROR] Failed to fetch leads: {e}")
            return []
    
    def fetch_responses(self, 
                       lead_ids: Optional[List[str]] = None,
                       start_date: Optional[datetime.datetime] = None,
                       end_date: Optional[datetime.datetime] = None,
                       limit: int = 5000) -> List[Dict[str, Any]]:
        """
        Fetch responses from Supabase.
        
        Args:
            lead_ids: List of lead IDs to filter by
            start_date: Start date for filtering
            end_date: End date for filtering
            limit: Maximum number of responses to fetch
            
        Returns:
            List of responses
        """
        if not self.supabase:
            print("[ERROR] Supabase client not initialized")
            return []
        
        try:
            query = self.supabase.table("responses").select("*")
            
            # Apply lead ID filter if provided
            if lead_ids:
                query = query.in_("lead_id", lead_ids)
            
            # Apply date filters if provided
            if start_date:
                query = query.gte("received_at", start_date.isoformat())
            if end_date:
                query = query.lte("received_at", end_date.isoformat())
            
            # Apply limit
            query = query.limit(limit)
            
            # Execute query
            response = query.execute()
            
            # Extract data
            responses = response.data if hasattr(response, 'data') else response
            
            print(f"[INFO] Fetched {len(responses)} responses")
            
            return responses
        except Exception as e:
            print(f"[ERROR] Failed to fetch responses: {e}")
            return []
    
    def fetch_form_submissions(self, 
                              lead_ids: Optional[List[str]] = None,
                              start_date: Optional[datetime.datetime] = None,
                              end_date: Optional[datetime.datetime] = None,
                              limit: int = 5000) -> List[Dict[str, Any]]:
        """
        Fetch form submissions from Supabase.
        
        Args:
            lead_ids: List of lead IDs to filter by
            start_date: Start date for filtering
            end_date: End date for filtering
            limit: Maximum number of form submissions to fetch
            
        Returns:
            List of form submissions
        """
        if not self.supabase:
            print("[ERROR] Supabase client not initialized")
            return []
        
        try:
            query = self.supabase.table("form_submissions").select("*")
            
            # Apply lead ID filter if provided
            if lead_ids:
                query = query.in_("lead_id", lead_ids)
            
            # Apply date filters if provided
            if start_date:
                query = query.gte("created_at", start_date.isoformat())
            if end_date:
                query = query.lte("created_at", end_date.isoformat())
            
            # Apply limit
            query = query.limit(limit)
            
            # Execute query
            response = query.execute()
            
            # Extract data
            submissions = response.data if hasattr(response, 'data') else response
            
            print(f"[INFO] Fetched {len(submissions)} form submissions")
            
            return submissions
        except Exception as e:
            print(f"[ERROR] Failed to fetch form submissions: {e}")
            return []
    
    def generate_response_time_report(self, 
                                     start_date: Optional[datetime.datetime] = None,
                                     end_date: Optional[datetime.datetime] = None,
                                     output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a report on response times.
        
        Args:
            start_date: Start date for filtering
            end_date: End date for filtering
            output_file: Output file path
            
        Returns:
            Report data
        """
        # Set default dates if not provided
        if not end_date:
            end_date = datetime.datetime.now()
        if not start_date:
            start_date = end_date - datetime.timedelta(days=30)
        
        # Set default output file if not provided
        if not output_file:
            date_str = datetime.datetime.now().strftime("%Y%m%d")
            output_file = os.path.join(self.output_dir, f"response_time_report_{date_str}.csv")
        
        # Fetch data
        leads = self.fetch_leads(start_date, end_date)
        lead_ids = [lead.get("id") for lead in leads]
        responses = self.fetch_responses(lead_ids, start_date, end_date)
        form_submissions = self.fetch_form_submissions(lead_ids, start_date, end_date)
        
        # Index form submissions by lead ID
        submissions_by_lead = {}
        for submission in form_submissions:
            lead_id = submission.get("lead_id")
            if lead_id:
                # Keep the earliest submission for each lead
                if lead_id not in submissions_by_lead or submission.get("created_at", "") < submissions_by_lead[lead_id].get("created_at", ""):
                    submissions_by_lead[lead_id] = submission
        
        # Index responses by lead ID
        responses_by_lead = defaultdict(list)
        for response in responses:
            lead_id = response.get("lead_id")
            if lead_id:
                responses_by_lead[lead_id].append(response)
        
        # Calculate response times
        report_rows = []
        for lead in leads:
            lead_id = lead.get("id")
            lead_name = f"{lead.get('first_name', '')} {lead.get('last_name', '')}".strip()
            lead_email = lead.get("email", "")
            lead_company = lead.get("company", "")
            
            # Get form submission for this lead
            submission = submissions_by_lead.get(lead_id)
            if not submission:
                # Skip leads without form submissions
                continue
            
            submission_time = submission.get("created_at") or submission.get("submitted_at")
            if not submission_time:
                continue
            
            # Parse submission time
            try:
                submission_dt = datetime.datetime.fromisoformat(submission_time.replace("Z", "+00:00"))
            except Exception as e:
                print(f"[WARN] Could not parse submission time for lead {lead_id}: {submission_time}")
                continue
            
            # Get responses for this lead
            lead_responses = responses_by_lead.get(lead_id, [])
            
            if not lead_responses:
                # Include leads with no responses
                report_rows.append({
                    "lead_id": lead_id,
                    "lead_name": lead_name,
                    "lead_email": lead_email,
                    "lead_company": lead_company,
                    "submission_time": submission_time,
                    "channel": "NO_RESPONSE",
                    "response_time": None,
                    "delay_seconds": None,
                    "delay_human": None
                })
                continue
            
            # Process each response
            for response in lead_responses:
                channel = response.get("channel", "unknown")
                response_time = response.get("received_at")
                if not response_time:
                    continue
                
                # Parse response time
                try:
                    response_dt = datetime.datetime.fromisoformat(response_time.replace("Z", "+00:00"))
                except Exception as e:
                    print(f"[WARN] Could not parse response time for response {response.get('id')}: {response_time}")
                    continue
                
                # Calculate delay
                delay_seconds = (response_dt - submission_dt).total_seconds()
                delay_human = str(datetime.timedelta(seconds=int(delay_seconds)))
                
                # Add to report
                report_rows.append({
                    "lead_id": lead_id,
                    "lead_name": lead_name,
                    "lead_email": lead_email,
                    "lead_company": lead_company,
                    "submission_time": submission_time,
                    "channel": channel,
                    "response_time": response_time,
                    "delay_seconds": delay_seconds,
                    "delay_human": delay_human
                })
        
        # Write report to CSV
        if report_rows:
            with open(output_file, "w", newline="", encoding="utf-8") as f:
                writer = csv.DictWriter(f, fieldnames=report_rows[0].keys())
                writer.writeheader()
                writer.writerows(report_rows)
            print(f"[INFO] Response time report written to {output_file} ({len(report_rows)} rows)")
        else:
            print("[WARN] No data for response time report")
        
        # Calculate summary statistics
        summary = self.calculate_response_time_summary(report_rows)
        
        return {
            "report_file": output_file,
            "row_count": len(report_rows),
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "summary": summary
        }
    
    def calculate_response_time_summary(self, report_rows: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate summary statistics from response time report rows.
        
        Args:
            report_rows: Report rows
            
        Returns:
            Summary statistics
        """
        if not report_rows:
            return {
                "total_leads": 0,
                "responded_leads": 0,
                "response_rate": 0,
                "channels": {}
            }
        
        # Count unique leads
        lead_ids = set(row.get("lead_id") for row in report_rows)
        total_leads = len(lead_ids)
        
        # Count leads with responses
        responded_lead_ids = set(
            row.get("lead_id") for row in report_rows 
            if row.get("channel") != "NO_RESPONSE" and row.get("delay_seconds") is not None
        )
        responded_leads = len(responded_lead_ids)
        
        # Calculate response rate
        response_rate = responded_leads / total_leads if total_leads > 0 else 0
        
        # Group by channel
        channels = defaultdict(list)
        for row in report_rows:
            channel = row.get("channel")
            delay_seconds = row.get("delay_seconds")
            if channel and delay_seconds is not None:
                channels[channel].append(delay_seconds)
        
        # Calculate channel statistics
        channel_stats = {}
        for channel, delays in channels.items():
            if delays:
                channel_stats[channel] = {
                    "count": len(delays),
                    "min_seconds": min(delays),
                    "max_seconds": max(delays),
                    "avg_seconds": sum(delays) / len(delays),
                    "median_seconds": statistics.median(delays) if len(delays) > 0 else 0,
                    "min_human": str(datetime.timedelta(seconds=int(min(delays)))),
                    "max_human": str(datetime.timedelta(seconds=int(max(delays)))),
                    "avg_human": str(datetime.timedelta(seconds=int(sum(delays) / len(delays)))),
                    "median_human": str(datetime.timedelta(seconds=int(statistics.median(delays)))) if len(delays) > 0 else "0:00:00"
                }
        
        return {
            "total_leads": total_leads,
            "responded_leads": responded_leads,
            "response_rate": response_rate,
            "channels": channel_stats
        }


# Example usage
if __name__ == "__main__":
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("[WARN] python-dotenv not installed; skipping .env loading.")
    
    # Initialize response audit
    audit = ResponseAudit()
    
    # Generate response time report for the last 30 days
    end_date = datetime.datetime.now()
    start_date = end_date - datetime.timedelta(days=30)
    
    report = audit.generate_response_time_report(start_date, end_date)
    
    # Print summary
    print("\nResponse Time Summary:")
    print(f"Total Leads: {report['summary']['total_leads']}")
    print(f"Responded Leads: {report['summary']['responded_leads']}")
    print(f"Response Rate: {report['summary']['response_rate']:.2%}")
    
    print("\nChannel Statistics:")
    for channel, stats in report['summary']['channels'].items():
        print(f"\n{channel}:")
        print(f"  Count: {stats['count']}")
        print(f"  Min Time: {stats['min_human']}")
        print(f"  Max Time: {stats['max_human']}")
        print(f"  Avg Time: {stats['avg_human']}")
        print(f"  Median Time: {stats['median_human']}")
