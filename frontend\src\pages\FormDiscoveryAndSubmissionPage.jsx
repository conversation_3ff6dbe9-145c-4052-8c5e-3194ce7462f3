import React from 'react';
import { Box, Paper } from '@mui/material';
import PageHeader from '../components/common/PageHeader';
import FormDiscoveryAndSubmissionPanel from '../components/FormDiscoveryAndSubmission/FormDiscoveryAndSubmissionPanel';
import { Search as SearchIcon } from '@mui/icons-material';

/**
 * Form Discovery & Submission Page
 *
 * This page combines form discovery and submission functionality into a single interface.
 * It allows users to:
 * 1. Discover and submit forms for a single website (with lead enrichment if needed)
 * 2. Submit forms in bulk for multiple leads
 */
const FormDiscoveryAndSubmissionPage = () => {
  return (
    <Box>
      <PageHeader
        title="Form Submitter"
        description="Discover and submit lead forms on websites"
        breadcrumbs={[
          { label: 'Form Submitter', icon: <SearchIcon fontSize="inherit" /> }
        ]}
      />

      <Paper sx={{ p: 3 }}>
        <FormDiscoveryAndSubmissionPanel />
      </Paper>
    </Box>
  );
};

export default FormDiscoveryAndSubmissionPage;
