import apiService from '../services/apiService';

// User interface
export interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: string;
}

// Authentication utilities
class AuthUtils {
  private currentUser: User | null = null;

  // Check if user is authenticated
  public isAuthenticated(): boolean {
    return !!localStorage.getItem('auth_token');
  }

  // Get current user
  public async getCurrentUser(): Promise<User | null> {
    if (this.currentUser) {
      return this.currentUser;
    }

    if (!this.isAuthenticated()) {
      return null;
    }

    try {
      // Fetch user profile from API
      const user = await apiService.get<User>('/auth/profile');
      this.currentUser = user;
      return user;
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  // Login user
  public async login(username: string, password: string): Promise<User> {
    const response = await apiService.login(username, password);
    
    // Fetch user profile after successful login
    this.currentUser = await apiService.get<User>('/auth/profile');
    
    return this.currentUser;
  }

  // Logout user
  public async logout(): Promise<void> {
    await apiService.logout();
    this.currentUser = null;
  }

  // Check if user has a specific role
  public async hasRole(role: string): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user?.role === role;
  }

  // Refresh authentication token
  public async refreshToken(): Promise<void> {
    try {
      const response = await apiService.post<{ access_token: string }>('/auth/refresh');
      
      if (response.access_token) {
        apiService.setToken(response.access_token);
      }
    } catch (error) {
      console.error('Failed to refresh token:', error);
      // If refresh fails, log out the user
      await this.logout();
    }
  }
}

// Create and export a singleton instance
const authUtils = new AuthUtils();

export default authUtils;
