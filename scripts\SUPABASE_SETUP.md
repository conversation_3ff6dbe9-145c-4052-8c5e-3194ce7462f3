# Supabase Database Setup for Lead Generation System

This document provides instructions for setting up the Supabase database for the lead generation system.

## Option 1: Using the SQL Editor (Recommended)

1. **Log in to Supabase**:
   - Go to https://app.supabase.com/
   - Sign in to your account
   - Select your project

2. **Open the SQL Editor**:
   - In the left sidebar, click on "SQL Editor"
   - Create a new query

3. **Execute the SQL Script**:
   - Open the `create_lead_tables.sql` file in this directory
   - Copy the entire contents
   - Paste it into the SQL Editor
   - Click "Run" to execute the script

4. **Verify the Tables**:
   - Go to the "Table Editor" in the left sidebar
   - You should see the following tables:
     - `leads`
     - `import_batches`
     - `form_submissions`
     - `websites`
     - `settings`

## Option 2: Using the Setup Script

If you prefer to use a script to set up the database, follow these steps:

1. **Install Dependencies**:
   ```
   cd scripts
   npm install @supabase/supabase-js dotenv
   ```

2. **Create the exec_sql Function**:
   - Go to the Supabase SQL Editor
   - Execute the following SQL:
   ```sql
   CREATE OR REPLACE FUNCTION exec_sql(sql text)
   RETURNS void AS $$
   BEGIN
     EXECUTE sql;
   END;
   $$ LANGUAGE plpgsql SECURITY DEFINER;
   ```

3. **Run the Setup Script**:
   ```
   node setup_supabase.js
   ```

## Database Schema

The database includes the following tables:

### leads
- Primary table for storing lead information
- Contains basic contact info, social media links, and status fields
- Tracks enrichment and form submission status

### import_batches
- Tracks CSV imports
- Stores metadata about each import batch
- Links to the leads imported in each batch

### form_submissions
- Records form submissions for each lead
- Tracks status, response data, and errors
- Supports retry logic for failed submissions

### websites
- Stores information about discovered websites
- Tracks forms found on each website
- Records Facebook Pixel status

### settings
- Stores system-wide settings
- Includes default form fields and other configuration

## Updating the Frontend

Make sure your frontend application has the correct Supabase credentials:

1. **Check the .env file**:
   - Open `frontend/.env`
   - Verify that `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY` are set correctly

2. **Restart the Frontend**:
   - Stop and restart your frontend development server to apply the changes

## Testing the Integration

After setting up the database, you can test the integration:

1. **Import Leads**:
   - Go to the Lead Import page
   - Upload a CSV file
   - Complete the import process
   - Verify that the leads are saved to the database

2. **Submit Forms**:
   - Go to the Bulk Submission page
   - Select leads and target websites
   - Start the submission process
   - Verify that form submissions are recorded in the database

## Troubleshooting

If you encounter issues:

1. **Check Supabase Logs**:
   - Go to the "Database" section in Supabase
   - Click on "Logs" to view database logs

2. **Verify Permissions**:
   - Make sure Row Level Security (RLS) policies are correctly set up
   - Check that your application is using the correct API keys

3. **Reset the Database**:
   - If needed, you can reset the database using the `reset_all_tables.sql` script
   - This will delete all data but keep the table structure
