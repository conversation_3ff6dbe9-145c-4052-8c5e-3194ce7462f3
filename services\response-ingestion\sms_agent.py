"""
SMS Agent Module

This module provides functionality to monitor and process SMS responses via Telnyx webhook integration.
It uses a LangGraph agent for entity extraction and lead matching, mirroring the logic of the email agent.
"""
import os
# Load environment variables from .env before anything else
try:
    from dotenv import load_dotenv
    load_dotenv()
    print('[INFO] .env file loaded.')
except ImportError:
    print('[WARN] python-dotenv not installed; skipping .env loading.')
import json
from typing import Dict, Any
from flask import Flask, request, jsonify
from match_infer_graph import MatchInferGraph
from lead_context_store import LeadContextStore

# Initialize Flask app for webhook
app = Flask(__name__)

# Initialize the LangGraph agent
sms_agent = MatchInferGraph()
lead_store = LeadContextStore()

@app.route('/sms_webhook', methods=['POST'])
def sms_webhook():
    """
    Telnyx will POST inbound SMS events to this endpoint.
    """
    data = request.json
    # Extract relevant fields from Telnyx payload
    # (Assume Telnyx standard webhook format; adjust as needed)
    sms_data = data.get('data', {})
    payload = sms_data.get('payload', {})
    # Robust extraction of from/to numbers (Telnyx can return dict or list)
    def extract_phone(obj):
        if isinstance(obj, dict):
            return obj.get('phone_number')
        elif isinstance(obj, list) and obj and isinstance(obj[0], dict):
            return obj[0].get('phone_number')
        return None

    from_number = extract_phone(payload.get('from', {}))
    to_obj = payload.get('to', {})
    to_number = extract_phone(to_obj)
    text = payload.get('text')
    received_at = payload.get('received_at') or payload.get('timestamp')

    # Log payload for debugging if extraction fails
    if not from_number or not to_number:
        print(f"[ERROR] Could not extract phone numbers from payload: {json.dumps(payload, indent=2)}")

    # Compose message dict for the agent
    message = {
        "message": text,
        "from_phone": from_number,
        "from_email": None,
        "channel": "sms",
        "received_at": received_at,
    }

    # Process the message through the LangGraph agent
    result = sms_agent.process_message(message)

    matched_lead = result.get('matched_lead') or (result.get('message', {}) if isinstance(result.get('message'), dict) else {}).get('matched_lead')
    print(f"From: {from_number}")
    print(f"Received: {received_at}")
    if matched_lead:
        print("Matched Lead:")
        print(f"  Name: {matched_lead.get('first_name', '')} {matched_lead.get('last_name', '')}")
        print(f"  Email: {matched_lead.get('email', '')}")
        print(f"  Phone: {matched_lead.get('phone', '')}")
        print(f"  Domain: {matched_lead.get('domain', '')}")
        print(f"  Company: {matched_lead.get('company', '')}")
    else:
        print("Matched Lead: None")
    if result.get('error'):
        print(f"Error: {result.get('error')}")
    print(f"Classification: {result.get('classification', {}).get('intent', 'N/A')}")
    print(f"Response Explanation: {result.get('response_explanation', '')}")
    print()

    # Optionally log to audit logger if available
    try:
        from audit_logger import AuditLogger
        logger = AuditLogger()
        logger.log_result(result)
    except Exception as e:
        print(f"[WARN] Could not log SMS result to audit logger: {e}")

    return jsonify({"status": "ok", "result": result})

if __name__ == "__main__":
    port = int(os.environ.get("SMS_AGENT_PORT", 5001))
    print(f"[INFO] Starting SMS Agent webhook on port {port}")
    app.run(host="0.0.0.0", port=port)
