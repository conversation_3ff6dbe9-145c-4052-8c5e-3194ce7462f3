FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy shared libraries
COPY ../../libs /app/libs

# Copy service code
COPY . .

# Expose port
EXPOSE 8008

# Run the service
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8008"]
