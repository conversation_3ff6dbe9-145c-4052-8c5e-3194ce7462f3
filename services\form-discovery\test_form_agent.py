#!/usr/bin/env python
"""
Test script for the Form Agent.

This script tests the Form Agent's ability to discover and submit forms.
"""
import asyncio
import json
import sys
import os
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set OpenAI API key for testing
if not os.getenv("OPENAI_API_KEY"):
    os.environ["OPENAI_API_KEY"] = "sk-dummy-key-for-testing"

# Import the Form Agent
from form_agent import FormAgent

async def test_discover_and_submit():
    """Test form discovery and submission."""
    # Initialize the Form Agent
    agent = FormAgent(use_ai_reasoning=False)  # Use False to avoid actual API calls

    # Test URLs (just using example.com for testing)
    urls = [
        "http://www.example.com/contact"
    ]

    # Test lead data
    lead_data = {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "************",
        "message": "I'm interested in learning more about your services and would like to schedule a free estimate."
    }

    # Test each URL
    for url in urls:
        print(f"\n=== Testing {url} ===")

        # Discover and submit form
        result = await agent.discover_and_submit_form(
            url=url,
            lead_data=lead_data,
            job_id=f"test_{url.replace('http://', '').replace('https://', '').replace('/', '_')}"
        )

        # Print result
        print(f"Success: {result.get('success', False)}")
        print(f"Form ID: {result.get('form_id')}")
        print(f"Fields filled: {result.get('fields_filled', 0)}")

        if result.get('error'):
            print(f"Error: {result.get('error')}")

        # Print metadata
        if result.get('metadata'):
            print("Metadata:")
            for key, value in result.get('metadata', {}).items():
                print(f"  {key}: {value}")

    print("\nTests completed.")

async def test_discovery_only():
    """Test form discovery only."""
    # Initialize the Form Agent
    agent = FormAgent(use_ai_reasoning=False)

    # Test URLs (just using example.com for testing)
    urls = [
        "http://www.example.com/contact"
    ]

    # Test each URL
    for url in urls:
        print(f"\n=== Testing discovery for {url} ===")

        # Discover forms
        result = await agent.discover_and_submit_form(
            url=url,
            lead_data={},  # Empty lead data for discovery only
            job_id=f"discovery_{url.replace('http://', '').replace('https://', '').replace('/', '_')}"
        )

        # Print result
        print(f"Success: {result.get('success', False)}")
        print(f"Forms discovered: {len(result.get('discovered_forms', []))}")

        if result.get('error'):
            print(f"Error: {result.get('error')}")

        # Print discovered forms
        for i, form in enumerate(result.get('discovered_forms', [])):
            print(f"\nForm {i+1}:")
            print(f"  Form ID: {form.get('form_id')}")
            print(f"  URL: {form.get('url')}")
            print(f"  Title: {form.get('title')}")
            print(f"  Fields: {len(form.get('fields', []))}")

    print("\nDiscovery tests completed.")

async def main():
    """Run all tests."""
    print("=== Testing Form Agent ===")

    # Test discovery only
    await test_discovery_only()

    # Test discovery and submission
    await test_discover_and_submit()

if __name__ == "__main__":
    # Run the tests
    asyncio.run(main())
