FROM python:3.9-slim

WORKDIR /app

# Copy requirements files
COPY services/requirements.txt /app/requirements.txt
COPY services/ai-orchestrator/requirements.txt /app/service-requirements.txt

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r service-requirements.txt

# Copy shared libraries
COPY libs /app/libs

# Copy service code
COPY services/ai-orchestrator /app/services/ai-orchestrator

# Set environment variables
ENV PYTHONPATH=/app
ENV SERVICE_NAME=ai-orchestrator

# Expose port
EXPOSE 8006

# Run the service
CMD ["python", "services/ai-orchestrator/main.py"]
