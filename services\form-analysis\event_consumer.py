import json
import asyncio
from typing import Dict, Any, Optional, Callable, Awaitable
import uuid

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.interfaces import Event, EventType

# Initialize logger
logger = get_service_logger("form-analysis", "event-consumer")


class EventConsumer:
    """
    Consumer for receiving events from Kafka.
    """
    
    def __init__(self, bootstrap_servers: str, topic: str, group_id: str):
        """
        Initialize the event consumer.
        
        Args:
            bootstrap_servers: Kafka bootstrap servers
            topic: Kafka topic to consume from
            group_id: Consumer group ID
        """
        self.bootstrap_servers = bootstrap_servers
        self.topic = topic
        self.group_id = group_id
        self.logger = logger
        self.running = False
        self.consumer_task = None
        
        # In a real implementation, this would initialize a Kafka consumer
        # For now, we'll simulate consuming events
    
    async def start(self, callback: Callable[[Any], Awaitable[None]]):
        """
        Start consuming events.
        
        Args:
            callback: Callback function to process events
        """
        self.logger.info(f"Starting event consumer for topic {self.topic}")
        
        self.running = True
        self.consumer_task = asyncio.create_task(self._consume_events(callback))
        
        self.logger.info(f"Event consumer started")
    
    async def stop(self):
        """Stop consuming events."""
        self.logger.info(f"Stopping event consumer")
        
        self.running = False
        
        if self.consumer_task:
            await self.consumer_task
        
        self.logger.info(f"Event consumer stopped")
    
    async def _consume_events(self, callback: Callable[[Any], Awaitable[None]]):
        """
        Consume events from Kafka.
        
        Args:
            callback: Callback function to process events
        """
        self.logger.info(f"Consuming events from topic {self.topic}")
        
        # In a real implementation, this would consume events from Kafka
        # For now, we'll simulate consuming events
        
        try:
            while self.running:
                # Simulate receiving an event
                await asyncio.sleep(5)
                
                # In a real implementation, we would receive events from Kafka
                # For now, we'll simulate a form discovery event
                
                # Create a mock event
                event = Event(
                    event_id=str(uuid.uuid4()),
                    event_type=EventType.FORM_DISCOVERED,
                    producer="form-discovery-service",
                    payload={
                        "form_id": str(uuid.uuid4()),
                        "url": "https://example.com/contact",
                        "discovered_by": "form-discovery-service",
                        "screenshot_path": "/screenshots/example.png",
                        "html_content": """
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>Example Page</title>
                        </head>
                        <body>
                            <h1>Contact Us</h1>
                            <form action="/contact" method="POST">
                                <div>
                                    <label for="name">Name:</label>
                                    <input type="text" id="name" name="name" required>
                                </div>
                                <div>
                                    <label for="email">Email:</label>
                                    <input type="email" id="email" name="email" required>
                                </div>
                                <div>
                                    <label for="message">Message:</label>
                                    <textarea id="message" name="message" required></textarea>
                                </div>
                                <div>
                                    <button type="submit">Send</button>
                                </div>
                            </form>
                        </body>
                        </html>
                        """,
                        "metadata": {
                            "company_name": "Example Company",
                            "industry": "Technology",
                            "priority": 1
                        }
                    }
                )
                
                self.logger.info(f"Received event", props={
                    "event_id": event.event_id,
                    "event_type": event.event_type
                })
                
                # Process the event
                try:
                    # Create a request object for the callback
                    from pydantic import HttpUrl
                    from main import FormAnalysisRequest
                    
                    request = FormAnalysisRequest(
                        form_id=event.payload["form_id"],
                        url=event.payload["url"],
                        html_content=event.payload["html_content"],
                        screenshot_path=event.payload.get("screenshot_path")
                    )
                    
                    # Call the callback
                    await callback(request)
                    
                    self.logger.info(f"Event processed successfully", props={
                        "event_id": event.event_id,
                        "event_type": event.event_type
                    })
                    
                except Exception as e:
                    self.logger.error(f"Error processing event: {str(e)}", props={
                        "event_id": event.event_id,
                        "event_type": event.event_type
                    })
                
        except asyncio.CancelledError:
            self.logger.info(f"Event consumer task cancelled")
        except Exception as e:
            self.logger.error(f"Error in event consumer: {str(e)}")
            raise
