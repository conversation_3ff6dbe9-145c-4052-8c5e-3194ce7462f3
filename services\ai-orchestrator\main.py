import os
import uuid
from typing import List, Dict, Any, Optional
from fastapi import FastAP<PERSON>, BackgroundTasks, HTTPException, Depends, status
from pydantic import BaseModel
from datetime import datetime

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.auth import get_current_active_user, User
from libs.interfaces import (
    AIModelType,
    AIModelRequest,
    AIModelResponse,
    Event, 
    EventType, 
    AIModelRequestedPayload,
    AIModelRespondedPayload
)

# Import service-specific modules
from .model_orchestrator import ModelOrchestrator
from .event_producer import EventProducer

# Initialize FastAPI app
app = FastAPI(
    title="AI Orchestrator Service",
    description="Service for orchestrating AI models and pipelines",
    version="0.1.0"
)

# Initialize logger
logger = get_service_logger("ai-orchestrator")

# Initialize event producer
event_producer = EventProducer(
    bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092"),
    topic=os.getenv("AI_MODEL_RESPONDED_TOPIC", "ai.model.responded")
)

# Initialize model orchestrator
model_orchestrator = ModelOrchestrator()


# Request/Response models
class ModelRequestInput(BaseModel):
    model_type: AIModelType
    input_data: Dict[str, Any]
    context: Dict[str, Any] = {}


class ModelRequestResponse(BaseModel):
    request_id: str
    message: str


class ModelResponse(BaseModel):
    response_id: str
    request_id: str
    model_type: AIModelType
    output_data: Dict[str, Any]
    confidence: float
    processing_time: float


# API endpoints
@app.post("/api/v1/models/request", response_model=ModelRequestResponse)
async def request_model(
    request: ModelRequestInput,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """
    Request a model prediction.
    
    This endpoint requests a model prediction and publishes an event.
    """
    logger.info(f"Requesting model prediction", props={
        "user": current_user.username,
        "model_type": request.model_type
    })
    
    try:
        # Create request ID
        request_id = str(uuid.uuid4())
        
        # Create model request
        model_request = AIModelRequest(
            request_id=request_id,
            model_type=request.model_type,
            input_data=request.input_data,
            context=request.context,
            timestamp=datetime.utcnow()
        )
        
        # Process model request in background
        background_tasks.add_task(
            process_model_request,
            model_request=model_request
        )
        
        return ModelRequestResponse(
            request_id=request_id,
            message=f"Model request for {request.model_type} submitted successfully"
        )
        
    except Exception as e:
        logger.error(f"Error requesting model prediction: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error requesting model prediction: {str(e)}"
        )


@app.get("/api/v1/models/response/{request_id}", response_model=ModelResponse)
async def get_model_response(
    request_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a model response by request ID.
    
    This endpoint returns a model response by its request ID.
    """
    logger.info(f"Getting model response for request {request_id}", props={"user": current_user.username})
    
    try:
        # Get model response
        model_response = await model_orchestrator.get_model_response(request_id)
        
        if not model_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Model response for request {request_id} not found"
            )
        
        return ModelResponse(
            response_id=model_response.response_id,
            request_id=model_response.request_id,
            model_type=model_response.model_type,
            output_data=model_response.output_data,
            confidence=model_response.confidence,
            processing_time=model_response.processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting model response: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting model response: {str(e)}"
        )


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}


# Helper functions
async def process_model_request(model_request: AIModelRequest):
    """
    Process a model request.
    
    Args:
        model_request: Model request to process
    """
    logger.info(f"Processing model request {model_request.request_id}", props={
        "model_type": model_request.model_type
    })
    
    try:
        # Publish model requested event
        event = Event(
            event_id=str(uuid.uuid4()),
            event_type=EventType.AI_MODEL_REQUESTED,
            producer="ai-orchestrator-service",
            payload=AIModelRequestedPayload(
                request_id=model_request.request_id,
                model_type=model_request.model_type,
                input_data=model_request.input_data,
                context=model_request.context,
                metadata={}
            ).dict()
        )
        
        await event_producer.produce_event(event)
        
        # Process model request
        model_response = await model_orchestrator.process_model_request(model_request)
        
        # Publish model responded event
        event = Event(
            event_id=str(uuid.uuid4()),
            event_type=EventType.AI_MODEL_RESPONDED,
            producer="ai-orchestrator-service",
            payload=AIModelRespondedPayload(
                response_id=model_response.response_id,
                request_id=model_response.request_id,
                model_type=model_response.model_type,
                output_data=model_response.output_data,
                confidence=model_response.confidence,
                processing_time=model_response.processing_time,
                metadata={}
            ).dict()
        )
        
        await event_producer.produce_event(event)
        
        logger.info(f"Processed model request {model_request.request_id}", props={
            "model_type": model_request.model_type,
            "response_id": model_response.response_id,
            "confidence": model_response.confidence,
            "processing_time": model_response.processing_time
        })
        
    except Exception as e:
        logger.error(f"Error processing model request: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8006)
