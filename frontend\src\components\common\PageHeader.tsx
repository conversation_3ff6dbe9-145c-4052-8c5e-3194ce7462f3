import React from 'react';
import { Box, Typography, Breadcrumbs, Link } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { Home as HomeIcon, NavigateNext as NavigateNextIcon } from '@mui/icons-material';

interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: React.ReactNode;
}

interface PageHeaderProps {
  title: string;
  description?: string;
  breadcrumbs?: BreadcrumbItem[];
  action?: React.ReactNode;
}

/**
 * PageHeader Component
 *
 * A consistent page header with title, description, breadcrumbs, and optional action button
 */
const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  breadcrumbs = [],
  action
}) => {
  // Default breadcrumbs always include Home
  const defaultBreadcrumbs: BreadcrumbItem[] = [
    { label: 'Home', path: '/dashboard', icon: <HomeIcon fontSize="inherit" /> },
  ];

  // Combine default with provided breadcrumbs
  const allBreadcrumbs = [...defaultBreadcrumbs, ...breadcrumbs];

  return (
    <Box className="fade-in" sx={{ mb: 4 }}>
      {/* Breadcrumbs */}
      {breadcrumbs && breadcrumbs.length > 0 && (
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          aria-label="breadcrumb"
          sx={{ mb: 2 }}
        >
          {allBreadcrumbs.map((crumb, index) => {
            const isLast = index === allBreadcrumbs.length - 1;

            return isLast ? (
              <Typography
                key={crumb.label}
                color="text.primary"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '0.875rem'
                }}
              >
                {crumb.icon && (
                  <Box component="span" sx={{ mr: 0.5, display: 'flex', alignItems: 'center' }}>
                    {crumb.icon}
                  </Box>
                )}
                {crumb.label}
              </Typography>
            ) : (
              <Link
                key={crumb.label}
                component={RouterLink}
                to={crumb.path || '#'}
                color="inherit"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '0.875rem'
                }}
              >
                {crumb.icon && (
                  <Box component="span" sx={{ mr: 0.5, display: 'flex', alignItems: 'center' }}>
                    {crumb.icon}
                  </Box>
                )}
                {crumb.label}
              </Link>
            );
          })}
        </Breadcrumbs>
      )}

      {/* Header with title and optional action */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: description ? 1 : 3
      }}>
        <Typography variant="h4" fontWeight="medium">
          {title}
        </Typography>

        {action && (
          <Box>
            {action}
          </Box>
        )}
      </Box>

      {/* Optional description */}
      {description && (
        <Typography variant="body1" color="text.secondary" paragraph sx={{ mb: 3 }}>
          {description}
        </Typography>
      )}
    </Box>
  );
};

export default PageHeader;
