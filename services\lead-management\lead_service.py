from typing import List, Dict, Any, Optional
import uuid
from datetime import datetime
import asyncio
import os
import json
from sqlalchemy import create_engine, Column, String, Boolean, DateTime, JSON, Table, MetaData, select, insert, update, delete, and_
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.interfaces import Lead

# Initialize logger
logger = get_service_logger("lead-management", "lead-service")

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@postgres:5432/leadgen")

# Create async engine
engine = create_async_engine(DATABASE_URL)
async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

# Define metadata
metadata = MetaData()

# Define leads table
leads_table = Table(
    "leads",
    metadata,
    Column("lead_id", String, primary_key=True),
    Column("first_name", String, nullable=True),
    Column("last_name", String, nullable=True),
    Column("email", String, nullable=True),
    Column("phone", String, nullable=True),
    Column("company", String, nullable=True),
    Column("job_title", String, nullable=True),
    Column("address", String, nullable=True),
    Column("city", String, nullable=True),
    Column("state", String, nullable=True),
    Column("zip_code", String, nullable=True),
    Column("country", String, nullable=True),
    Column("source", String, nullable=True),
    Column("created_at", DateTime, default=datetime.utcnow),
    Column("updated_at", DateTime, default=datetime.utcnow, onupdate=datetime.utcnow),
    Column("tags", JSON, default=list),
    Column("custom_fields", JSON, default=dict),
    Column("consent", Boolean, default=False),
    Column("consent_timestamp", DateTime, nullable=True),
    Column("notes", String, nullable=True)
)


class LeadService:
    """
    Service for managing leads.
    """
    
    def __init__(self):
        """Initialize the lead service."""
        self.logger = logger
    
    async def create_tables(self):
        """Create database tables if they don't exist."""
        async with engine.begin() as conn:
            await conn.run_sync(metadata.create_all)
    
    async def create_lead(self, lead_data: Dict[str, Any]) -> str:
        """
        Create a new lead.
        
        Args:
            lead_data: Lead data
            
        Returns:
            Lead ID
        """
        self.logger.info(f"Creating lead")
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Generate a unique ID for the lead
        lead_id = str(uuid.uuid4())
        
        # Set timestamps
        now = datetime.utcnow()
        
        # Prepare lead data
        lead_dict = {
            "lead_id": lead_id,
            "created_at": now,
            "updated_at": now,
            **lead_data
        }
        
        # Insert lead into database
        async with async_session() as session:
            async with session.begin():
                await session.execute(
                    insert(leads_table).values(**lead_dict)
                )
        
        self.logger.info(f"Created lead {lead_id}")
        
        return lead_id
    
    async def get_leads(
        self, 
        skip: int = 0, 
        limit: int = 100,
        source: Optional[str] = None,
        tag: Optional[str] = None
    ) -> List[Lead]:
        """
        Get a list of leads.
        
        Args:
            skip: Number of leads to skip
            limit: Maximum number of leads to return
            source: Filter by source
            tag: Filter by tag
            
        Returns:
            List of leads
        """
        self.logger.info(f"Getting leads", props={
            "skip": skip,
            "limit": limit,
            "source": source,
            "tag": tag
        })
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Build query
        query = select(leads_table)
        
        # Apply filters
        if source:
            query = query.where(leads_table.c.source == source)
        
        # Apply pagination
        query = query.offset(skip).limit(limit)
        
        # Execute query
        async with async_session() as session:
            result = await session.execute(query)
            rows = result.fetchall()
        
        # Convert rows to Lead objects
        leads = []
        for row in rows:
            lead_dict = {column.name: getattr(row, column.name) for column in leads_table.columns}
            
            # Filter by tag if specified
            if tag and tag not in lead_dict.get("tags", []):
                continue
            
            # Convert to Lead object
            lead = Lead(**lead_dict)
            leads.append(lead)
        
        self.logger.info(f"Got {len(leads)} leads")
        
        return leads
    
    async def get_lead(self, lead_id: str) -> Optional[Lead]:
        """
        Get a lead by ID.
        
        Args:
            lead_id: Lead ID
            
        Returns:
            Lead or None if not found
        """
        self.logger.info(f"Getting lead {lead_id}")
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Build query
        query = select(leads_table).where(leads_table.c.lead_id == lead_id)
        
        # Execute query
        async with async_session() as session:
            result = await session.execute(query)
            row = result.fetchone()
        
        if not row:
            self.logger.warning(f"Lead {lead_id} not found")
            return None
        
        # Convert row to Lead object
        lead_dict = {column.name: getattr(row, column.name) for column in leads_table.columns}
        lead = Lead(**lead_dict)
        
        self.logger.info(f"Got lead {lead_id}")
        
        return lead
    
    async def update_lead(self, lead_id: str, lead_data: Dict[str, Any]) -> bool:
        """
        Update a lead.
        
        Args:
            lead_id: Lead ID
            lead_data: Lead data to update
            
        Returns:
            True if the lead was updated, False if not found
        """
        self.logger.info(f"Updating lead {lead_id}")
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Set updated timestamp
        lead_data["updated_at"] = datetime.utcnow()
        
        # Update lead in database
        async with async_session() as session:
            async with session.begin():
                result = await session.execute(
                    update(leads_table)
                    .where(leads_table.c.lead_id == lead_id)
                    .values(**lead_data)
                )
        
        # Check if lead was updated
        if result.rowcount == 0:
            self.logger.warning(f"Lead {lead_id} not found")
            return False
        
        self.logger.info(f"Updated lead {lead_id}")
        
        return True
    
    async def delete_lead(self, lead_id: str) -> bool:
        """
        Delete a lead.
        
        Args:
            lead_id: Lead ID
            
        Returns:
            True if the lead was deleted, False if not found
        """
        self.logger.info(f"Deleting lead {lead_id}")
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Delete lead from database
        async with async_session() as session:
            async with session.begin():
                result = await session.execute(
                    delete(leads_table)
                    .where(leads_table.c.lead_id == lead_id)
                )
        
        # Check if lead was deleted
        if result.rowcount == 0:
            self.logger.warning(f"Lead {lead_id} not found")
            return False
        
        self.logger.info(f"Deleted lead {lead_id}")
        
        return True
