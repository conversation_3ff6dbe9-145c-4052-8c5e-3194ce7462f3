import os
import uuid
import datetime
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, BackgroundTasks, HTTPException, Depends, status
from pydantic import BaseModel, HttpUrl, Field

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.auth import get_current_active_user, User
from libs.interfaces import (
    FormMetadata,
    FormStatus,
    Event,
    EventType,
    FormDiscoveredPayload,
    FormSubmittedPayload
)

# Import service-specific modules
try:
    from .form_agent import FormAgent
    from .event_producer import EventProducer
except ImportError:
    # For direct script execution
    from form_agent import FormAgent
    from event_producer import EventProducer

# Initialize FastAPI app
app = FastAPI(
    title="Form Discovery & Submission Service",
    description="Service for discovering and submitting contact forms on websites",
    version="0.2.0"
)

# Initialize logger
logger = get_service_logger("form-discovery")

# Initialize event producer
event_producer = EventProducer(
    bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092"),
    topic=os.getenv("FORM_EVENTS_TOPIC", "form.events")
)

# Initialize form agent
form_agent = FormAgent(use_ai_reasoning=True)

# Request/Response models
class WebsiteRequest(BaseModel):
    url: HttpUrl
    company_name: Optional[str] = None
    industry: Optional[str] = None
    priority: int = Field(default=1, ge=1, le=10)
    max_pages: int = Field(default=10, ge=1, le=50)
    max_depth: int = Field(default=3, ge=1, le=5)

class DiscoveryResponse(BaseModel):
    job_id: str
    status: str
    message: str

class DiscoveryJobStatus(BaseModel):
    job_id: str
    status: str
    forms_discovered: int = 0
    completed: bool = False
    error: Optional[str] = None

class SubmitFormRequest(BaseModel):
    url: HttpUrl
    form_id: Optional[str] = None
    lead_data: Dict[str, str]
    timeout: int = Field(default=60, ge=10, le=300)
    use_ai_reasoning: bool = True

class SubmitFormResponse(BaseModel):
    job_id: str
    message: str
    status: str

class FormSubmissionResult(BaseModel):
    job_id: str
    url: str
    form_id: Optional[str] = None
    fields_filled: int = 0
    submitted: bool = False
    status: str
    error: Optional[str] = None
    processing_time: Optional[float] = None
    submission_timestamp: Optional[datetime.datetime] = None
    metadata: Dict[str, Any] = {}

# Background task for form discovery
async def discover_forms_task(website: WebsiteRequest, job_id: str):
    """Background task to discover forms on a website."""
    logger.info(f"Starting form discovery for {website.url}", props={"job_id": job_id})

    try:
        # Use the form agent to discover forms
        start_time = datetime.datetime.now()
        result = await form_agent.discover_and_submit_form(
            url=str(website.url),
            lead_data={},  # Empty lead data for discovery only
            job_id=job_id
        )
        processing_time = (datetime.datetime.now() - start_time).total_seconds()

        # Store the result
        await form_agent.store_result(job_id, {
            "job_id": job_id,
            "url": str(website.url),
            "discovered_forms": result.get("discovered_forms", []),
            "status": "completed" if result.get("success", False) else "failed",
            "error": result.get("error"),
            "processing_time": processing_time,
            "timestamp": datetime.datetime.now().isoformat()
        })

        # Publish events for each discovered form
        for form in result.get("discovered_forms", []):
            # Create a unique ID for the form if not already present
            form_id = form.get("form_id", str(uuid.uuid4()))

            # Create and publish event
            event = Event(
                event_id=str(uuid.uuid4()),
                event_type=EventType.FORM_DISCOVERED,
                producer="form-discovery-service",
                payload=FormDiscoveredPayload(
                    form_id=form_id,
                    url=str(form.get("url", website.url)),
                    discovered_by="form-discovery-service",
                    screenshot_path=form.get("screenshot_path"),
                    html_content=form.get("html_content"),
                    metadata={
                        "company_name": website.company_name,
                        "industry": website.industry,
                        "priority": website.priority
                    }
                ).dict()
            )

            await event_producer.produce_event(event)

            logger.info(f"Published form discovery event", props={
                "job_id": job_id,
                "form_id": form_id,
                "url": str(form.get("url", website.url))
            })

        logger.info(f"Form discovery completed for {website.url}", props={
            "job_id": job_id,
            "forms_discovered": len(result.get("discovered_forms", []))
        })

    except Exception as e:
        logger.error(f"Error during form discovery: {str(e)}", props={"job_id": job_id})
        # Store error result
        await form_agent.store_result(job_id, {
            "job_id": job_id,
            "url": str(website.url),
            "discovered_forms": [],
            "status": "failed",
            "error": str(e),
            "processing_time": 0,
            "timestamp": datetime.datetime.now().isoformat()
        })

# Background task for form submission
async def submit_form_task(request: SubmitFormRequest, job_id: str):
    """Background task to submit a form."""
    logger.info(f"Starting form submission for {request.url}", props={
        "job_id": job_id,
        "form_id": request.form_id
    })

    try:
        # Use the form agent to submit the form
        start_time = datetime.datetime.now()
        result = await form_agent.discover_and_submit_form(
            url=str(request.url),
            lead_data=request.lead_data,
            form_id=request.form_id,
            job_id=job_id
        )
        processing_time = (datetime.datetime.now() - start_time).total_seconds()

        # Store the result
        submission_result = FormSubmissionResult(
            job_id=job_id,
            url=str(request.url),
            form_id=result.get("form_id"),
            fields_filled=result.get("fields_filled", 0),
            submitted=result.get("success", False),
            status="completed" if result.get("success", False) else "failed",
            error=result.get("error"),
            processing_time=processing_time,
            submission_timestamp=datetime.datetime.now(),
            metadata=result.get("metadata", {})
        )

        await form_agent.store_result(job_id, submission_result.dict())

        # Publish form submitted event if successful
        if result.get("success", False):
            event = Event(
                event_id=str(uuid.uuid4()),
                event_type=EventType.FORM_SUBMITTED,
                producer="form-discovery-service",
                payload=FormSubmittedPayload(
                    form_id=result.get("form_id", "unknown"),
                    submission_id=job_id,
                    lead_id=job_id,  # Using job_id as lead_id for simplicity
                    url=str(request.url),
                    submitted_data=request.lead_data,
                    success=True,
                    metadata=result.get("metadata", {})
                ).dict()
            )

            await event_producer.produce_event(event)

            logger.info(f"Published form submission event", props={
                "job_id": job_id,
                "form_id": result.get("form_id"),
                "url": str(request.url)
            })

        logger.info(f"Form submission completed for {request.url}", props={
            "job_id": job_id,
            "success": result.get("success", False),
            "fields_filled": result.get("fields_filled", 0)
        })

    except Exception as e:
        logger.error(f"Error during form submission: {str(e)}", props={"job_id": job_id})
        # Store error result
        submission_result = FormSubmissionResult(
            job_id=job_id,
            url=str(request.url),
            form_id=request.form_id,
            fields_filled=0,
            submitted=False,
            status="failed",
            error=str(e),
            processing_time=0,
            submission_timestamp=datetime.datetime.now(),
            metadata={}
        )

        await form_agent.store_result(job_id, submission_result.dict())

# API endpoints
@app.post("/api/v1/discover", response_model=DiscoveryResponse)
async def discover_forms(
    website: WebsiteRequest,
    background_tasks: BackgroundTasks,
    current_user: User = None  # Make authentication optional for testing
):
    """
    Discover contact forms on a website.

    This endpoint initiates a background task to crawl the website and discover contact forms.
    """
    job_id = str(uuid.uuid4())

    logger.info(f"Form discovery requested for {website.url}", props={
        "job_id": job_id,
        "user": current_user.username if current_user else "anonymous"
    })

    background_tasks.add_task(discover_forms_task, website, job_id)

    return DiscoveryResponse(
        job_id=job_id,
        status="processing",
        message=f"Form discovery started for {website.url}"
    )

@app.get("/api/v1/discover/{job_id}", response_model=DiscoveryJobStatus)
async def get_discovery_status(
    job_id: str,
    current_user: User = None  # Make authentication optional for testing
):
    """
    Get the status of a form discovery job.

    This endpoint returns the current status of a form discovery job.
    """
    logger.info(f"Status requested for job {job_id}", props={"user": current_user.username if current_user else "anonymous"})

    # Get the result from the form agent
    result = await form_agent.get_result(job_id)

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Job {job_id} not found"
        )

    return DiscoveryJobStatus(
        job_id=job_id,
        status=result.get("status", "unknown"),
        forms_discovered=len(result.get("discovered_forms", [])),
        completed=result.get("status") == "completed",
        error=result.get("error")
    )

@app.post("/api/v1/submit", response_model=SubmitFormResponse)
async def submit_form(
    request: SubmitFormRequest,
    background_tasks: BackgroundTasks,
    current_user: User = None  # Make authentication optional for testing
):
    """
    Submit a lead form.

    This endpoint submits lead data to a form on the specified URL.
    """
    job_id = str(uuid.uuid4())

    logger.info(f"Form submission requested for {request.url}", props={
        "job_id": job_id,
        "user": current_user.username if current_user else "anonymous",
        "form_id": request.form_id
    })

    background_tasks.add_task(submit_form_task, request, job_id)

    return SubmitFormResponse(
        job_id=job_id,
        status="processing",
        message=f"Form submission started for {request.url}"
    )

@app.get("/api/v1/submit/{job_id}", response_model=FormSubmissionResult)
async def get_submission_status(
    job_id: str,
    current_user: User = None  # Make authentication optional for testing
):
    """
    Get the status of a form submission job.

    This endpoint returns the current status of a form submission job.
    """
    logger.info(f"Status requested for submission job {job_id}", props={"user": current_user.username if current_user else "anonymous"})

    # Get the result from the form agent
    result = await form_agent.get_result(job_id)

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Job {job_id} not found"
        )

    # Convert to FormSubmissionResult if needed
    if isinstance(result, dict) and not isinstance(result, FormSubmissionResult):
        return FormSubmissionResult(**result)

    return result

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
