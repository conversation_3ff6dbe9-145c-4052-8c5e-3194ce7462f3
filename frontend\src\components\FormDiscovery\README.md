# Form Discovery & Submission Component

This component provides a user interface for discovering and submitting lead forms on websites. It integrates with the Form Discovery & Submission Service to provide a seamless experience for users.

## Features

- **Form Discovery**: Discover lead forms on websites
- **Form Analysis**: Analyze form fields and determine how to fill them out
- **Form Submission**: Submit lead data to forms with AI-powered reasoning
- **Real-time Status Updates**: Track the status of form discovery and submission jobs
- **Error Handling**: Robust error handling and user feedback

## Usage

```jsx
import FormDiscoveryPanel from './components/FormDiscovery';

const MyPage = () => {
  return (
    <div>
      <h1>Form Discovery & Submission</h1>
      <FormDiscoveryPanel />
    </div>
  );
};
```

## API Integration

The component uses the `formDiscoveryService` to interact with the Form Discovery & Submission Service. The service provides the following methods:

- `discoverForms(url, options)`: Discover forms on a website
- `getDiscoveryStatus(jobId)`: Get the status of a form discovery job
- `submitForm(url, leadData, formId, options)`: Submit a form
- `getSubmissionStatus(jobId)`: Get the status of a form submission job
- `discoverAndSubmitForm(url, leadData, options)`: Discover and submit a form in one operation

## Configuration

The component can be configured using environment variables:

- `REACT_APP_FORM_DISCOVERY_API_URL`: URL of the Form Discovery & Submission Service
- `REACT_APP_ENABLE_AI_REASONING`: Whether to use AI reasoning for form filling
- `REACT_APP_ENABLE_MOCK_DATA`: Whether to use mock data for testing

## Dependencies

- React
- Material-UI
- Axios

## Backend Integration

This component integrates with the Form Discovery & Submission Service, which provides the following endpoints:

- `POST /api/v1/discover`: Discover forms on a website
- `GET /api/v1/discover/{job_id}`: Get the status of a form discovery job
- `POST /api/v1/submit`: Submit a form
- `GET /api/v1/submit/{job_id}`: Get the status of a form submission job

## Example

```jsx
// Example of using the FormDiscoveryPanel component
import React from 'react';
import FormDiscoveryPanel from './components/FormDiscovery';

const FormDiscoveryPage = () => {
  return (
    <div>
      <h1>Form Discovery & Submission</h1>
      <p>Discover and submit lead forms on websites</p>
      <FormDiscoveryPanel />
    </div>
  );
};

export default FormDiscoveryPage;
```
