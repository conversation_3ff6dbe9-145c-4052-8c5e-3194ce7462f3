from typing import List, Dict, Any, Optional
import uuid
from datetime import datetime
import asyncio
import os
import json
from sqlalchemy import create_engine, Column, String, Boolean, DateTime, JSON, Table, MetaData, select, insert, update, delete, and_
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.interfaces import Response, ResponseChannel

# Initialize logger
logger = get_service_logger("response-ingestion", "response-service")

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@postgres:5432/leadgen")

# Create async engine
engine = create_async_engine(DATABASE_URL)
async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

# Define metadata
metadata = MetaData()

# Define responses table
responses_table = Table(
    "responses",
    metadata,
    Column("response_id", String, primary_key=True),
    Column("lead_id", String, nullable=False),
    Column("submission_id", String, nullable=True),
    Column("channel", String, nullable=False),
    Column("content", String, nullable=False),
    Column("received_at", DateTime, default=datetime.utcnow),
    Column("sender_info", JSON, default=dict),
    Column("status", String, nullable=False),
    Column("sentiment_score", String, nullable=True),
    Column("quality_score", String, nullable=True),
    Column("metadata", JSON, default=dict)
)


class ResponseService:
    """
    Service for managing responses.
    """
    
    def __init__(self):
        """Initialize the response service."""
        self.logger = logger
    
    async def create_tables(self):
        """Create database tables if they don't exist."""
        async with engine.begin() as conn:
            await conn.run_sync(metadata.create_all)
    
    async def create_response(self, response_data: Dict[str, Any]) -> str:
        """
        Create a new response.
        
        Args:
            response_data: Response data
            
        Returns:
            Response ID
        """
        self.logger.info(f"Creating response for lead {response_data.get('lead_id')}")
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Insert response into database
        async with async_session() as session:
            async with session.begin():
                await session.execute(
                    insert(responses_table).values(**response_data)
                )
        
        self.logger.info(f"Created response {response_data.get('response_id')}")
        
        return response_data.get('response_id')
    
    async def get_responses(
        self, 
        lead_id: Optional[str] = None,
        channel: Optional[ResponseChannel] = None,
        skip: int = 0, 
        limit: int = 100
    ) -> List[Response]:
        """
        Get a list of responses.
        
        Args:
            lead_id: Filter by lead ID
            channel: Filter by channel
            skip: Number of responses to skip
            limit: Maximum number of responses to return
            
        Returns:
            List of responses
        """
        self.logger.info(f"Getting responses", props={
            "lead_id": lead_id,
            "channel": channel,
            "skip": skip,
            "limit": limit
        })
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Build query
        query = select(responses_table)
        
        # Apply filters
        if lead_id:
            query = query.where(responses_table.c.lead_id == lead_id)
        
        if channel:
            query = query.where(responses_table.c.channel == channel)
        
        # Apply pagination
        query = query.offset(skip).limit(limit)
        
        # Execute query
        async with async_session() as session:
            result = await session.execute(query)
            rows = result.fetchall()
        
        # Convert rows to Response objects
        responses = []
        for row in rows:
            response_dict = {column.name: getattr(row, column.name) for column in responses_table.columns}
            response = Response(**response_dict)
            responses.append(response)
        
        self.logger.info(f"Got {len(responses)} responses")
        
        return responses
    
    async def get_response(self, response_id: str) -> Optional[Response]:
        """
        Get a response by ID.
        
        Args:
            response_id: Response ID
            
        Returns:
            Response or None if not found
        """
        self.logger.info(f"Getting response {response_id}")
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Build query
        query = select(responses_table).where(responses_table.c.response_id == response_id)
        
        # Execute query
        async with async_session() as session:
            result = await session.execute(query)
            row = result.fetchone()
        
        if not row:
            self.logger.warning(f"Response {response_id} not found")
            return None
        
        # Convert row to Response object
        response_dict = {column.name: getattr(row, column.name) for column in responses_table.columns}
        response = Response(**response_dict)
        
        self.logger.info(f"Got response {response_id}")
        
        return response
    
    async def update_response(self, response_id: str, response_data: Dict[str, Any]) -> bool:
        """
        Update a response.
        
        Args:
            response_id: Response ID
            response_data: Response data to update
            
        Returns:
            True if the response was updated, False if not found
        """
        self.logger.info(f"Updating response {response_id}")
        
        # Create tables if they don't exist
        await self.create_tables()
        
        # Update response in database
        async with async_session() as session:
            async with session.begin():
                result = await session.execute(
                    update(responses_table)
                    .where(responses_table.c.response_id == response_id)
                    .values(**response_data)
                )
        
        # Check if response was updated
        if result.rowcount == 0:
            self.logger.warning(f"Response {response_id} not found")
            return False
        
        self.logger.info(f"Updated response {response_id}")
        
        return True
