#!/usr/bin/env python
"""
Simple test script for the Form Discovery & Submission Service.

This script tests the basic functionality without using LangGraph.
"""
import asyncio
import json
import sys
import os
import uuid
from typing import Dict, Any, List, Optional
import datetime

class MockFormAgent:
    """
    Mock implementation of the Form Agent for testing.
    """
    
    def __init__(self):
        """Initialize the mock form agent."""
        self.results = {}
    
    async def discover_forms(self, url: str) -> List[Dict[str, Any]]:
        """
        Discover forms on a website.
        
        Args:
            url: URL to discover forms on
            
        Returns:
            List of discovered forms
        """
        print(f"Discovering forms on {url}")
        
        # Generate a deterministic form ID based on the URL for testing
        form_id = f"form_{hash(url) % 10000}"
        
        # Simulate discovered forms
        discovered_forms = [
            {
                "form_id": form_id,
                "url": url,
                "title": "Contact Form",
                "description": "Send us a message",
                "fields": [
                    {"name": "name", "field_type": "text", "required": True},
                    {"name": "email", "field_type": "email", "required": True},
                    {"name": "phone", "field_type": "tel", "required": False},
                    {"name": "message", "field_type": "textarea", "required": True}
                ],
                "multi_step": False,
                "has_captcha": False,
                "submission_endpoint": "/contact",
                "method": "POST"
            }
        ]
        
        return discovered_forms
    
    async def submit_form(
        self,
        url: str,
        lead_data: Dict[str, str],
        form_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Submit a form.
        
        Args:
            url: URL to submit form on
            lead_data: Lead data to submit
            form_id: Form ID to submit (optional)
            
        Returns:
            Submission result
        """
        print(f"Submitting form on {url}")
        
        # Use the provided form ID or generate a deterministic one
        if not form_id:
            form_id = f"form_{hash(url) % 10000}"
        
        # Count fields filled based on lead data
        fields_filled = len(lead_data) if lead_data else 0
        
        # Always succeed in test mode
        submitted = True
        
        # Create submission details
        submission_details = {
            "form_id": form_id,
            "url": url,
            "fields_filled": fields_filled,
            "submitted": submitted,
            "timestamp": datetime.datetime.now().isoformat(),
            "metadata": {
                "confidence_score": 0.85,
                "critical_fields": list(lead_data.keys())[:2] if lead_data else [],
                "optional_fields": list(lead_data.keys())[2:] if lead_data and len(lead_data) > 2 else []
            }
        }
        
        return {
            "success": True,
            "form_id": form_id,
            "fields_filled": fields_filled,
            "metadata": submission_details.get("metadata", {})
        }
    
    async def discover_and_submit_form(
        self,
        url: str,
        lead_data: Dict[str, str],
        form_id: Optional[str] = None,
        job_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Discover and submit a form.
        
        Args:
            url: URL to discover and submit form on
            lead_data: Lead data to submit
            form_id: Form ID to submit (optional)
            job_id: Job ID for tracking (optional)
            
        Returns:
            Result of the form discovery and submission
        """
        print(f"Discovering and submitting form on {url}")
        
        # Generate a job ID if not provided
        job_id = job_id or str(uuid.uuid4())
        
        try:
            # Discover forms
            discovered_forms = await self.discover_forms(url)
            
            if not discovered_forms:
                return {
                    "success": False,
                    "error": "No forms discovered",
                    "job_id": job_id,
                    "discovered_forms": []
                }
            
            # Select form to submit
            selected_form = None
            if form_id:
                # Find form with matching ID
                for form in discovered_forms:
                    if form.get("form_id") == form_id:
                        selected_form = form
                        break
                
                if not selected_form:
                    return {
                        "success": False,
                        "error": f"Form with ID {form_id} not found",
                        "job_id": job_id,
                        "discovered_forms": discovered_forms
                    }
            else:
                # Use the first form
                selected_form = discovered_forms[0]
            
            # Submit form
            if lead_data:
                submission_result = await self.submit_form(
                    url=url,
                    lead_data=lead_data,
                    form_id=selected_form.get("form_id")
                )
                
                return {
                    "success": submission_result.get("success", False),
                    "form_id": submission_result.get("form_id"),
                    "fields_filled": submission_result.get("fields_filled", 0),
                    "job_id": job_id,
                    "metadata": submission_result.get("metadata", {}),
                    "discovered_forms": discovered_forms
                }
            else:
                # Discovery only
                return {
                    "success": True,
                    "job_id": job_id,
                    "discovered_forms": discovered_forms
                }
            
        except Exception as e:
            print(f"Error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "job_id": job_id,
                "discovered_forms": []
            }
    
    async def store_result(self, job_id: str, result: Dict[str, Any]) -> None:
        """
        Store a result.
        
        Args:
            job_id: Job ID
            result: Result to store
        """
        self.results[job_id] = result
    
    async def get_result(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a result.
        
        Args:
            job_id: Job ID
            
        Returns:
            Result or None if not found
        """
        return self.results.get(job_id)

async def test_discover_and_submit():
    """Test form discovery and submission."""
    # Initialize the mock form agent
    agent = MockFormAgent()
    
    # Test URLs
    urls = [
        "http://www.example.com/contact"
    ]
    
    # Test lead data
    lead_data = {
        "name": "John Smith",
        "email": "<EMAIL>",
        "phone": "************",
        "message": "I'm interested in learning more about your services and would like to schedule a free estimate."
    }
    
    # Test each URL
    for url in urls:
        print(f"\n=== Testing {url} ===")
        
        # Discover and submit form
        result = await agent.discover_and_submit_form(
            url=url,
            lead_data=lead_data,
            job_id=f"test_{url.replace('http://', '').replace('https://', '').replace('/', '_')}"
        )
        
        # Print result
        print(f"Success: {result.get('success', False)}")
        print(f"Form ID: {result.get('form_id')}")
        print(f"Fields filled: {result.get('fields_filled', 0)}")
        
        if result.get('error'):
            print(f"Error: {result.get('error')}")
        
        # Print metadata
        if result.get('metadata'):
            print("Metadata:")
            for key, value in result.get('metadata', {}).items():
                print(f"  {key}: {value}")
    
    print("\nTests completed.")

async def test_discovery_only():
    """Test form discovery only."""
    # Initialize the mock form agent
    agent = MockFormAgent()
    
    # Test URLs
    urls = [
        "http://www.example.com/contact"
    ]
    
    # Test each URL
    for url in urls:
        print(f"\n=== Testing discovery for {url} ===")
        
        # Discover forms
        result = await agent.discover_and_submit_form(
            url=url,
            lead_data={},  # Empty lead data for discovery only
            job_id=f"discovery_{url.replace('http://', '').replace('https://', '').replace('/', '_')}"
        )
        
        # Print result
        print(f"Success: {result.get('success', False)}")
        print(f"Forms discovered: {len(result.get('discovered_forms', []))}")
        
        if result.get('error'):
            print(f"Error: {result.get('error')}")
        
        # Print discovered forms
        for i, form in enumerate(result.get('discovered_forms', [])):
            print(f"\nForm {i+1}:")
            print(f"  Form ID: {form.get('form_id')}")
            print(f"  URL: {form.get('url')}")
            print(f"  Title: {form.get('title')}")
            print(f"  Fields: {len(form.get('fields', []))}")
    
    print("\nDiscovery tests completed.")

async def main():
    """Run all tests."""
    print("=== Testing Form Agent ===")
    
    # Test discovery only
    await test_discovery_only()
    
    # Test discovery and submission
    await test_discover_and_submit()

if __name__ == "__main__":
    # Run the tests
    asyncio.run(main())
