FROM python:3.9-slim

WORKDIR /app

# Copy requirements files
COPY services/requirements.txt /app/requirements.txt
COPY services/conversation-management/requirements.txt /app/service-requirements.txt

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r service-requirements.txt

# Copy shared libraries
COPY libs /app/libs

# Copy service code
COPY services/conversation-management /app/services/conversation-management

# Set environment variables
ENV PYTHONPATH=/app
ENV SERVICE_NAME=conversation-management

# Expose port
EXPOSE 8005

# Run the service
CMD ["python", "services/conversation-management/main.py"]
