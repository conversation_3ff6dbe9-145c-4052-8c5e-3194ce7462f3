from typing import Dict, Any, List, Optional
import re
import json
import asyncio
from bs4 import BeautifulSoup

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.interfaces import FieldType

# Import service-specific modules
from .field_classifier import FieldClassifier
from .validation_detector import ValidationDetector

# Initialize logger
logger = get_service_logger("form-analysis", "engine")


class FormAnalysisEngine:
    """
    Engine for analyzing form structure and fields.
    """
    
    def __init__(self):
        """Initialize the form analysis engine."""
        self.field_classifier = FieldClassifier()
        self.validation_detector = ValidationDetector()
        self.logger = logger
    
    async def analyze_form(
        self, 
        form_id: str, 
        url: str, 
        html_content: str,
        screenshot_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze a form structure and fields.
        
        Args:
            form_id: ID of the form to analyze
            url: URL of the form
            html_content: HTML content of the page containing the form
            screenshot_path: Path to the screenshot of the form
            
        Returns:
            Analysis result containing form metadata
        """
        self.logger.info(f"Analyzing form {form_id} at {url}")
        
        try:
            # Parse the HTML
            soup = BeautifulSoup(html_content, "html.parser")
            
            # Find the form
            form_element = soup.find("form")
            
            if not form_element:
                self.logger.warning(f"No form found at {url}")
                return {
                    "fields": {},
                    "has_captcha": False,
                    "multi_step": False,
                    "submission_endpoint": None,
                    "method": "POST",
                    "success_indicators": None,
                    "error_indicators": None,
                    "metadata": {}
                }
            
            # Extract form attributes
            form_action = form_element.get("action", "")
            form_method = form_element.get("method", "POST").upper()
            form_id_attr = form_element.get("id", "")
            form_class = form_element.get("class", [])
            if isinstance(form_class, list):
                form_class = " ".join(form_class)
            
            # Find all input, select, and textarea elements
            input_elements = form_element.find_all("input")
            select_elements = form_element.find_all("select")
            textarea_elements = form_element.find_all("textarea")
            
            # Analyze fields
            fields = {}
            
            # Process input elements
            for input_elem in input_elements:
                field_data = self._analyze_input_field(input_elem)
                if field_data:
                    field_name = field_data["name"] or f"field_{len(fields)}"
                    fields[field_name] = field_data
            
            # Process select elements
            for select_elem in select_elements:
                field_data = self._analyze_select_field(select_elem)
                if field_data:
                    field_name = field_data["name"] or f"field_{len(fields)}"
                    fields[field_name] = field_data
            
            # Process textarea elements
            for textarea_elem in textarea_elements:
                field_data = self._analyze_textarea_field(textarea_elem)
                if field_data:
                    field_name = field_data["name"] or f"field_{len(fields)}"
                    fields[field_name] = field_data
            
            # Detect if the form has CAPTCHA
            has_captcha = self._detect_captcha(form_element, html_content)
            
            # Detect if the form is multi-step
            multi_step = self._detect_multi_step(form_element, html_content)
            
            # Detect success and error indicators
            success_indicators = self._detect_success_indicators(html_content)
            error_indicators = self._detect_error_indicators(html_content)
            
            # Detect validation patterns
            validation_patterns = await self.validation_detector.detect_validation(
                html_content, fields
            )
            
            # Update fields with validation patterns
            for field_name, pattern in validation_patterns.items():
                if field_name in fields:
                    fields[field_name]["validation_pattern"] = pattern
            
            # Prepare the result
            result = {
                "fields": fields,
                "has_captcha": has_captcha,
                "multi_step": multi_step,
                "submission_endpoint": form_action,
                "method": form_method,
                "success_indicators": success_indicators,
                "error_indicators": error_indicators,
                "metadata": {
                    "form_id_attr": form_id_attr,
                    "form_class": form_class
                }
            }
            
            self.logger.info(f"Form analysis completed for {form_id}", props={
                "field_count": len(fields),
                "has_captcha": has_captcha,
                "multi_step": multi_step
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing form: {str(e)}")
            raise
    
    def _analyze_input_field(self, input_elem) -> Optional[Dict[str, Any]]:
        """
        Analyze an input field.
        
        Args:
            input_elem: The input element to analyze
            
        Returns:
            Field data or None if the field should be ignored
        """
        field_type = input_elem.get("type", "text").lower()
        
        # Ignore hidden and submit fields
        if field_type in ["hidden", "submit", "button", "reset", "image"]:
            return None
        
        name = input_elem.get("name", "")
        id_attr = input_elem.get("id", "")
        required = input_elem.has_attr("required")
        placeholder = input_elem.get("placeholder", "")
        value = input_elem.get("value", "")
        
        # Find the label
        label = self._find_label(id_attr)
        
        # Classify the field
        field_type_enum = self.field_classifier.classify_field(
            name=name,
            id_attr=id_attr,
            html_type=field_type,
            label=label,
            placeholder=placeholder
        )
        
        return {
            "name": name,
            "id": id_attr,
            "label": label,
            "field_type": field_type_enum,
            "html_type": field_type,
            "required": required,
            "placeholder": placeholder,
            "default_value": value,
            "options": None,
            "validation_pattern": input_elem.get("pattern", None)
        }
    
    def _analyze_select_field(self, select_elem) -> Dict[str, Any]:
        """
        Analyze a select field.
        
        Args:
            select_elem: The select element to analyze
            
        Returns:
            Field data
        """
        name = select_elem.get("name", "")
        id_attr = select_elem.get("id", "")
        required = select_elem.has_attr("required")
        
        # Find the label
        label = self._find_label(id_attr)
        
        # Get options
        options = []
        for option in select_elem.find_all("option"):
            option_value = option.get("value", "")
            option_text = option.text.strip()
            if option_value or option_text:
                options.append({"value": option_value, "text": option_text})
        
        # Classify the field
        field_type_enum = self.field_classifier.classify_field(
            name=name,
            id_attr=id_attr,
            html_type="select",
            label=label,
            options=options
        )
        
        return {
            "name": name,
            "id": id_attr,
            "label": label,
            "field_type": field_type_enum,
            "html_type": "select",
            "required": required,
            "placeholder": None,
            "default_value": None,
            "options": options,
            "validation_pattern": None
        }
    
    def _analyze_textarea_field(self, textarea_elem) -> Dict[str, Any]:
        """
        Analyze a textarea field.
        
        Args:
            textarea_elem: The textarea element to analyze
            
        Returns:
            Field data
        """
        name = textarea_elem.get("name", "")
        id_attr = textarea_elem.get("id", "")
        required = textarea_elem.has_attr("required")
        placeholder = textarea_elem.get("placeholder", "")
        value = textarea_elem.text.strip()
        
        # Find the label
        label = self._find_label(id_attr)
        
        # Classify the field
        field_type_enum = self.field_classifier.classify_field(
            name=name,
            id_attr=id_attr,
            html_type="textarea",
            label=label,
            placeholder=placeholder
        )
        
        return {
            "name": name,
            "id": id_attr,
            "label": label,
            "field_type": field_type_enum,
            "html_type": "textarea",
            "required": required,
            "placeholder": placeholder,
            "default_value": value,
            "options": None,
            "validation_pattern": None
        }
    
    def _find_label(self, field_id: str) -> Optional[str]:
        """
        Find the label for a field.
        
        Args:
            field_id: ID of the field
            
        Returns:
            Label text or None if not found
        """
        # In a real implementation, this would search the HTML for a label
        # This is a simplified example
        return f"Label for {field_id}" if field_id else None
    
    def _detect_captcha(self, form_element, html_content: str) -> bool:
        """
        Detect if the form has a CAPTCHA.
        
        Args:
            form_element: The form element
            html_content: HTML content of the page
            
        Returns:
            True if the form has a CAPTCHA, False otherwise
        """
        # Look for common CAPTCHA implementations
        html_lower = html_content.lower()
        captcha_indicators = [
            "captcha",
            "recaptcha",
            "g-recaptcha",
            "hcaptcha",
            "cf-turnstile"
        ]
        
        return any(indicator in html_lower for indicator in captcha_indicators)
    
    def _detect_multi_step(self, form_element, html_content: str) -> bool:
        """
        Detect if the form is multi-step.
        
        Args:
            form_element: The form element
            html_content: HTML content of the page
            
        Returns:
            True if the form is multi-step, False otherwise
        """
        # Look for indicators of multi-step forms
        html_lower = html_content.lower()
        multi_step_indicators = [
            "step",
            "wizard",
            "multi-step",
            "multistep",
            "next step",
            "previous step",
            "next page",
            "prev page"
        ]
        
        return any(indicator in html_lower for indicator in multi_step_indicators)
    
    def _detect_success_indicators(self, html_content: str) -> Dict[str, Any]:
        """
        Detect success indicators for form submission.
        
        Args:
            html_content: HTML content of the page
            
        Returns:
            Success indicators
        """
        # In a real implementation, this would analyze the page for success indicators
        # This is a simplified example
        return {
            "messages": ["Thank you", "Success", "Message sent"],
            "selectors": [".success", ".thank-you", ".confirmation"]
        }
    
    def _detect_error_indicators(self, html_content: str) -> Dict[str, Any]:
        """
        Detect error indicators for form submission.
        
        Args:
            html_content: HTML content of the page
            
        Returns:
            Error indicators
        """
        # In a real implementation, this would analyze the page for error indicators
        # This is a simplified example
        return {
            "messages": ["Error", "Failed", "Please try again"],
            "selectors": [".error", ".alert-danger", ".validation-error"]
        }
