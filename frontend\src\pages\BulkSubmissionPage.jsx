import React from 'react';
import {
  Box,
  Container,
  Typography,
  Breadcrumbs,
  Link,
  Paper,
} from '@mui/material';
import {
  Home as HomeIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import BulkSubmissionPanel from '../components/BulkSubmission';

/**
 * Bulk Submission Page
 * 
 * This page allows users to submit forms in bulk for imported leads.
 */
const BulkSubmissionPage = () => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 3 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            underline="hover"
            color="inherit"
            href="/"
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </Link>
          <Typography
            sx={{ display: 'flex', alignItems: 'center' }}
            color="text.primary"
          >
            <SendIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Bulk Form Submission
          </Typography>
        </Breadcrumbs>
        
        <BulkSubmissionPanel />
      </Box>
    </Container>
  );
};

export default BulkSubmissionPage;
