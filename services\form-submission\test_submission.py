#!/usr/bin/env python
"""
Test script for the Form Submission Service.
"""
import asyncio
import json
from form_submission import FormSubmissionEngine

async def test_form_submission():
    """Test form submission."""
    # Initialize form submission engine
    engine = FormSubmissionEngine()
    
    # Test data
    url = "http://www.example.com/contact"
    lead_data = {
        "name": "<PERSON>",
        "email": "johns<PERSON>@example.com",
        "phone": "************",
        "message": "I'm interested in learning more about your services."
    }
    
    # Submit form
    result = await engine.submit_form(
        url=url,
        lead_data=lead_data,
        use_ai_reasoning=True
    )
    
    # Print result
    print(f"Form submission result: {json.dumps(result, indent=2)}")
    
    # Return result
    return result

if __name__ == "__main__":
    # Run test
    asyncio.run(test_form_submission())
