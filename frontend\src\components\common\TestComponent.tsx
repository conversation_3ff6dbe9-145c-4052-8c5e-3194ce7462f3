import React from 'react';
import { Box, Typography, Button, Grid, Paper } from '@mui/material';
import PageHeader from './PageHeader';
import { Dashboard as DashboardIcon } from '@mui/icons-material';

/**
 * TestComponent
 * 
 * A component to test our UI styling and components
 */
const TestComponent: React.FC = () => {
  return (
    <Box>
      <PageHeader
        title="Test Component"
        description="This is a test component to verify our UI styling"
        breadcrumbs={[
          { label: 'Test', icon: <DashboardIcon fontSize="inherit" /> }
        ]}
        action={
          <Button variant="contained" color="primary">
            Test Action
          </Button>
        }
      />

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Card Title
            </Typography>
            <Typography variant="body2" paragraph>
              This is a test card to verify our styling is working correctly.
              It should have proper spacing, typography, and shadows.
            </Typography>
            <Button variant="contained">Primary Button</Button>
            <Button variant="outlined" sx={{ ml: 2 }}>Secondary Button</Button>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Animation Test
            </Typography>
            <Box className="slide-in" sx={{ p: 2, bgcolor: 'primary.light', borderRadius: 2, mb: 2 }}>
              <Typography variant="body2" color="primary.contrastText">
                This box should slide in from the right
              </Typography>
            </Box>
            <Box className="fade-in" sx={{ p: 2, bgcolor: 'secondary.light', borderRadius: 2, mb: 2 }}>
              <Typography variant="body2" color="secondary.contrastText">
                This box should fade in
              </Typography>
            </Box>
            <Box className="slide-up" sx={{ p: 2, bgcolor: 'success.light', borderRadius: 2 }}>
              <Typography variant="body2" color="success.contrastText">
                This box should slide up from the bottom
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TestComponent;
