"""
Outreach Agent Service

Generates personalized cold emails and LinkedIn messages based on audit reports.
"""

import os
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import openai
from supabase import create_client, Client
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Outreach Agent Service",
    description="Generates personalized cold outreach messages based on audit reports",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

# Initialize OpenAI client
openai.api_key = os.getenv("OPENAI_API_KEY")

# Pydantic models
class OutreachRequest(BaseModel):
    lead_id: str = Field(..., description="Lead ID to generate outreach for")
    audit_report_id: str = Field(..., description="Audit report ID to base outreach on")
    message_types: List[str] = Field(default=["email", "linkedin"], description="Types of messages to generate")
    tone: str = Field(default="professional", description="Tone of the message: professional, friendly, direct")
    include_audit_link: bool = Field(default=True, description="Whether to include link to audit report")
    generate_sequences: bool = Field(default=True, description="Whether to generate full sequences (5 emails, 3 LinkedIn)")
    sequence_spacing_days: List[int] = Field(default=[0, 3, 7, 14, 21], description="Days between sequence messages")

class OutreachResponse(BaseModel):
    success: bool
    message: str
    generated_messages: List[Dict[str, Any]]
    lead_id: str
    audit_report_id: str

class MessageGenerationRequest(BaseModel):
    lead_data: Dict[str, Any]
    audit_data: Dict[str, Any]
    message_type: str  # email or linkedin
    tone: str = "professional"
    include_audit_link: bool = True

class OutreachMessage(BaseModel):
    id: str
    lead_id: str
    audit_report_id: str
    message_type: str
    status: str
    subject_line: Optional[str]
    message_body: str
    personalization_data: Dict[str, Any]
    created_at: datetime

# Outreach generation service
class OutreachGenerator:
    def __init__(self):
        self.model = "gpt-4"

    async def generate_email_sequence(self, lead_data: Dict, audit_data: Dict, tone: str = "professional", include_audit_link: bool = True, sequence_days: List[int] = [0, 3, 7, 14, 21]) -> List[Dict[str, str]]:
        """Generate a 5-email sequence based on audit results."""

        # Define email sequence strategy based on audit performance
        grade = audit_data.get("grade", "C")
        responded = audit_data.get("responded", False)
        facebook_pixel = audit_data.get("facebook_pixel_detected", False)

        # Create sequence templates based on performance
        sequence_templates = self._get_email_sequence_templates(grade, responded, facebook_pixel)

        emails = []
        for i, template in enumerate(sequence_templates):
            email_data = await self._generate_single_email(
                lead_data, audit_data, template, tone, include_audit_link, i + 1
            )
            email_data["sequence_position"] = i + 1
            email_data["send_delay_days"] = sequence_days[i] if i < len(sequence_days) else sequence_days[-1] + (i - len(sequence_days) + 1) * 7
            emails.append(email_data)

        return emails

    async def generate_linkedin_sequence(self, lead_data: Dict, audit_data: Dict, tone: str = "professional", include_audit_link: bool = True) -> List[Dict[str, str]]:
        """Generate a 3-LinkedIn message sequence based on audit results."""

        # Define LinkedIn sequence strategy
        grade = audit_data.get("grade", "C")
        responded = audit_data.get("responded", False)

        sequence_templates = self._get_linkedin_sequence_templates(grade, responded)

        messages = []
        for i, template in enumerate(sequence_templates):
            message_data = await self._generate_single_linkedin_message(
                lead_data, audit_data, template, tone, include_audit_link, i + 1
            )
            message_data["sequence_position"] = i + 1
            message_data["send_delay_days"] = [0, 5, 12][i]  # LinkedIn spacing: immediate, 5 days, 12 days
            messages.append(message_data)

        return messages

    async def generate_email(self, lead_data: Dict, audit_data: Dict, tone: str = "professional", include_audit_link: bool = True) -> Dict[str, str]:
        """Generate a personalized cold email based on audit results."""

        # Extract key data points
        company_name = lead_data.get("company", "your company")
        first_name = lead_data.get("first_name", "")
        website = lead_data.get("website", "")

        # Audit insights
        grade = audit_data.get("grade", "C")
        response_time = audit_data.get("response_time_human", "slow")
        responded = audit_data.get("responded", False)
        conversion_rate = audit_data.get("conversion_rate_current", 50)
        facebook_pixel = audit_data.get("facebook_pixel_detected", False)

        # Create personalization context
        context = {
            "company_name": company_name,
            "first_name": first_name,
            "website": website,
            "grade": grade,
            "response_time": response_time,
            "responded": responded,
            "conversion_rate": conversion_rate,
            "facebook_pixel": facebook_pixel,
            "tone": tone
        }

        # Generate email using OpenAI
        prompt = self._create_email_prompt(context, include_audit_link)

        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert B2B sales copywriter specializing in lead generation and conversion optimization. Write compelling, personalized cold emails that get responses."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=800
            )

            content = response.choices[0].message.content.strip()

            # Parse the response to extract subject and body
            lines = content.split('\n')
            subject_line = ""
            message_body = ""

            for i, line in enumerate(lines):
                if line.startswith("Subject:"):
                    subject_line = line.replace("Subject:", "").strip()
                elif line.startswith("Body:") or (subject_line and i > 0):
                    message_body = '\n'.join(lines[i:]).replace("Body:", "").strip()
                    break

            if not subject_line:
                subject_line = f"Quick question about {company_name}'s lead response"

            if not message_body:
                message_body = content

            return {
                "subject_line": subject_line,
                "message_body": message_body,
                "personalization_data": context
            }

        except Exception as e:
            logger.error(f"Error generating email: {e}")
            raise HTTPException(status_code=500, detail=f"Error generating email: {str(e)}")

    async def generate_linkedin_message(self, lead_data: Dict, audit_data: Dict, tone: str = "professional", include_audit_link: bool = True) -> Dict[str, str]:
        """Generate a personalized LinkedIn message based on audit results."""

        # Extract key data points
        company_name = lead_data.get("company", "your company")
        first_name = lead_data.get("first_name", "")
        title = lead_data.get("title", "")

        # Audit insights
        grade = audit_data.get("grade", "C")
        response_time = audit_data.get("response_time_human", "slow")
        conversion_rate = audit_data.get("conversion_rate_current", 50)

        # Create personalization context
        context = {
            "company_name": company_name,
            "first_name": first_name,
            "title": title,
            "grade": grade,
            "response_time": response_time,
            "conversion_rate": conversion_rate,
            "tone": tone
        }

        # Generate LinkedIn message using OpenAI
        prompt = self._create_linkedin_prompt(context, include_audit_link)

        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert LinkedIn outreach specialist. Write concise, professional LinkedIn messages that start conversations and get responses. Keep messages under 300 characters when possible."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=400
            )

            message_body = response.choices[0].message.content.strip()

            return {
                "message_body": message_body,
                "personalization_data": context
            }

        except Exception as e:
            logger.error(f"Error generating LinkedIn message: {e}")
            raise HTTPException(status_code=500, detail=f"Error generating LinkedIn message: {str(e)}")

    def _create_email_prompt(self, context: Dict, include_audit_link: bool) -> str:
        """Create the prompt for email generation."""

        audit_insight = self._get_audit_insight(context)

        prompt = f"""
Write a personalized cold email for lead generation. Here's the context:

Company: {context['company_name']}
Contact: {context['first_name']}
Website: {context.get('website', 'N/A')}
Tone: {context['tone']}

Audit Results:
- Response Grade: {context['grade']}
- Response Time: {context['response_time']}
- Current Conversion Rate: {context['conversion_rate']}%
- Facebook Pixel: {'Detected' if context['facebook_pixel'] else 'Not Detected'}

Key Insight: {audit_insight}

Requirements:
1. Write a compelling subject line that mentions their specific issue
2. Keep the email under 150 words
3. Be specific about their audit results without being too technical
4. Include a clear call-to-action
5. Sound helpful, not salesy
6. Use their company name and first name naturally
{"7. Mention that we have a detailed audit report available" if include_audit_link else ""}

Format:
Subject: [subject line]

Body:
[email body]
"""
        return prompt

    def _create_linkedin_prompt(self, context: Dict, include_audit_link: bool) -> str:
        """Create the prompt for LinkedIn message generation."""

        audit_insight = self._get_audit_insight(context)

        prompt = f"""
Write a personalized LinkedIn connection message or direct message. Here's the context:

Company: {context['company_name']}
Contact: {context['first_name']}
Title: {context.get('title', 'N/A')}
Tone: {context['tone']}

Audit Results:
- Response Grade: {context['grade']}
- Response Time: {context['response_time']}
- Current Conversion Rate: {context['conversion_rate']}%

Key Insight: {audit_insight}

Requirements:
1. Keep it under 300 characters (LinkedIn limit)
2. Be conversational and professional
3. Mention their specific performance issue briefly
4. Include a soft call-to-action
5. Don't be pushy or salesy
{"6. Mention we have insights to share" if include_audit_link else ""}

Write just the message body, no subject line needed.
"""
        return prompt

    def _get_audit_insight(self, context: Dict) -> str:
        """Generate a key insight based on audit data."""

        grade = context['grade']
        conversion_rate = context['conversion_rate']
        facebook_pixel = context['facebook_pixel']

        if grade in ['F']:
            return "They're not responding to leads at all, missing 100% of opportunities"
        elif grade in ['D', 'C']:
            return f"Their slow response time is costing them {85 - conversion_rate}% of potential conversions"
        elif not facebook_pixel:
            return "They're missing Facebook Pixel tracking, losing retargeting opportunities"
        else:
            return "They have good response times but could optimize further"

    def _get_email_sequence_templates(self, grade: str, responded: bool, facebook_pixel: bool) -> List[Dict[str, str]]:
        """Get email sequence templates based on audit performance."""

        if not responded:
            # No response sequence - more urgent
            return [
                {
                    "type": "audit_reveal",
                    "focus": "no_response_shock",
                    "cta": "schedule_emergency_call"
                },
                {
                    "type": "competitor_comparison",
                    "focus": "losing_to_competitors",
                    "cta": "quick_consultation"
                },
                {
                    "type": "case_study",
                    "focus": "transformation_story",
                    "cta": "see_results"
                },
                {
                    "type": "social_proof",
                    "focus": "industry_leaders",
                    "cta": "join_winners"
                },
                {
                    "type": "final_offer",
                    "focus": "last_chance",
                    "cta": "claim_spot"
                }
            ]
        elif grade in ['D', 'C']:
            # Poor performance sequence - improvement focused
            return [
                {
                    "type": "audit_reveal",
                    "focus": "slow_response_cost",
                    "cta": "see_full_report"
                },
                {
                    "type": "value_demonstration",
                    "focus": "revenue_opportunity",
                    "cta": "calculate_roi"
                },
                {
                    "type": "educational_content",
                    "focus": "how_to_improve",
                    "cta": "download_guide"
                },
                {
                    "type": "success_story",
                    "focus": "similar_company",
                    "cta": "see_case_study"
                },
                {
                    "type": "consultation_offer",
                    "focus": "free_optimization",
                    "cta": "book_session"
                }
            ]
        else:
            # Good performance sequence - optimization focused
            return [
                {
                    "type": "compliment_audit",
                    "focus": "good_performance",
                    "cta": "see_opportunities"
                },
                {
                    "type": "advanced_optimization",
                    "focus": "next_level_tips",
                    "cta": "learn_advanced"
                },
                {
                    "type": "industry_benchmark",
                    "focus": "competitive_edge",
                    "cta": "compare_market"
                },
                {
                    "type": "technology_upgrade",
                    "focus": "modern_tools",
                    "cta": "explore_tech"
                },
                {
                    "type": "partnership_offer",
                    "focus": "mutual_benefit",
                    "cta": "discuss_partnership"
                }
            ]

    def _get_linkedin_sequence_templates(self, grade: str, responded: bool) -> List[Dict[str, str]]:
        """Get LinkedIn sequence templates based on audit performance."""

        if not responded:
            return [
                {
                    "type": "connection_request",
                    "focus": "audit_mention",
                    "cta": "connect"
                },
                {
                    "type": "value_share",
                    "focus": "quick_insight",
                    "cta": "see_report"
                },
                {
                    "type": "final_reach",
                    "focus": "last_attempt",
                    "cta": "quick_chat"
                }
            ]
        elif grade in ['D', 'C']:
            return [
                {
                    "type": "connection_request",
                    "focus": "improvement_opportunity",
                    "cta": "connect"
                },
                {
                    "type": "insight_share",
                    "focus": "specific_findings",
                    "cta": "discuss_results"
                },
                {
                    "type": "consultation_offer",
                    "focus": "free_advice",
                    "cta": "schedule_call"
                }
            ]
        else:
            return [
                {
                    "type": "connection_request",
                    "focus": "compliment_performance",
                    "cta": "connect"
                },
                {
                    "type": "advanced_tip",
                    "focus": "optimization_idea",
                    "cta": "share_insight"
                },
                {
                    "type": "collaboration",
                    "focus": "mutual_benefit",
                    "cta": "explore_partnership"
                }
            ]

    async def _generate_single_email(self, lead_data: Dict, audit_data: Dict, template: Dict, tone: str, include_audit_link: bool, sequence_position: int) -> Dict[str, str]:
        """Generate a single email based on template and sequence position."""

        # Extract key data points
        company_name = lead_data.get("company", "your company")
        first_name = lead_data.get("first_name", "")
        website = lead_data.get("website", "")

        # Audit insights
        grade = audit_data.get("grade", "C")
        response_time = audit_data.get("response_time_human", "slow")
        responded = audit_data.get("responded", False)
        conversion_rate = audit_data.get("conversion_rate_current", 50)
        facebook_pixel = audit_data.get("facebook_pixel_detected", False)

        # Create personalization context
        context = {
            "company_name": company_name,
            "first_name": first_name,
            "website": website,
            "grade": grade,
            "response_time": response_time,
            "responded": responded,
            "conversion_rate": conversion_rate,
            "facebook_pixel": facebook_pixel,
            "tone": tone,
            "template": template,
            "sequence_position": sequence_position
        }

        # Generate email using OpenAI
        prompt = self._create_sequence_email_prompt(context, include_audit_link)

        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": f"You are an expert B2B sales copywriter specializing in lead generation sequences. Write email #{sequence_position} in a 5-email sequence. Each email should build on the previous ones and move the prospect closer to a consultation."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=800
            )

            content = response.choices[0].message.content.strip()

            # Parse the response to extract subject and body
            lines = content.split('\n')
            subject_line = ""
            message_body = ""

            for i, line in enumerate(lines):
                if line.startswith("Subject:"):
                    subject_line = line.replace("Subject:", "").strip()
                elif line.startswith("Body:") or (subject_line and i > 0):
                    message_body = '\n'.join(lines[i:]).replace("Body:", "").strip()
                    break

            if not subject_line:
                subject_line = f"Email #{sequence_position}: {company_name} lead response insights"

            if not message_body:
                message_body = content

            return {
                "subject_line": subject_line,
                "message_body": message_body,
                "personalization_data": context,
                "template_type": template["type"],
                "template_focus": template["focus"]
            }

        except Exception as e:
            logger.error(f"Error generating sequence email: {e}")
            raise HTTPException(status_code=500, detail=f"Error generating sequence email: {str(e)}")

    async def _generate_single_linkedin_message(self, lead_data: Dict, audit_data: Dict, template: Dict, tone: str, include_audit_link: bool, sequence_position: int) -> Dict[str, str]:
        """Generate a single LinkedIn message based on template and sequence position."""

        # Extract key data points
        company_name = lead_data.get("company", "your company")
        first_name = lead_data.get("first_name", "")
        title = lead_data.get("title", "")

        # Audit insights
        grade = audit_data.get("grade", "C")
        response_time = audit_data.get("response_time_human", "slow")
        conversion_rate = audit_data.get("conversion_rate_current", 50)

        # Create personalization context
        context = {
            "company_name": company_name,
            "first_name": first_name,
            "title": title,
            "grade": grade,
            "response_time": response_time,
            "conversion_rate": conversion_rate,
            "tone": tone,
            "template": template,
            "sequence_position": sequence_position
        }

        # Generate LinkedIn message using OpenAI
        prompt = self._create_sequence_linkedin_prompt(context, include_audit_link)

        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": f"You are an expert LinkedIn outreach specialist. Write LinkedIn message #{sequence_position} in a 3-message sequence. Keep messages under 300 characters and build rapport progressively."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=400
            )

            message_body = response.choices[0].message.content.strip()

            return {
                "message_body": message_body,
                "personalization_data": context,
                "template_type": template["type"],
                "template_focus": template["focus"]
            }

        except Exception as e:
            logger.error(f"Error generating sequence LinkedIn message: {e}")
            raise HTTPException(status_code=500, detail=f"Error generating sequence LinkedIn message: {str(e)}")

    def _create_sequence_email_prompt(self, context: Dict, include_audit_link: bool) -> str:
        """Create the prompt for sequence email generation."""

        template = context['template']
        sequence_position = context['sequence_position']

        # Get sequence-specific guidance
        sequence_guidance = {
            1: "This is the first email - introduce the audit findings and create curiosity",
            2: "This is the second follow-up - provide more value and address potential objections",
            3: "This is the third follow-up - share educational content or case studies",
            4: "This is the fourth follow-up - create urgency and social proof",
            5: "This is the final follow-up - last chance offer with clear next steps"
        }

        audit_insight = self._get_audit_insight(context)

        prompt = f"""
Write email #{sequence_position} in a 5-email sequence for lead generation. Here's the context:

Company: {context['company_name']}
Contact: {context['first_name']}
Website: {context.get('website', 'N/A')}
Tone: {context['tone']}

Audit Results:
- Response Grade: {context['grade']}
- Response Time: {context['response_time']}
- Current Conversion Rate: {context['conversion_rate']}%
- Facebook Pixel: {'Detected' if context['facebook_pixel'] else 'Not Detected'}

Email Template: {template['type']}
Focus: {template['focus']}
CTA: {template['cta']}

Sequence Guidance: {sequence_guidance.get(sequence_position, 'Continue building the relationship')}

Key Insight: {audit_insight}

Requirements:
1. Write a compelling subject line that fits this sequence position
2. Keep the email under 150 words
3. Reference the audit findings appropriately for this email in the sequence
4. Build on previous emails (assume they've seen email #{sequence_position-1} if applicable)
5. Include a clear call-to-action that matches the template CTA
6. Sound helpful and consultative, not pushy
7. Use their company name and first name naturally
{"8. Reference the detailed audit report available" if include_audit_link else ""}

Format:
Subject: [subject line]

Body:
[email body]
"""
        return prompt

    def _create_sequence_linkedin_prompt(self, context: Dict, include_audit_link: bool) -> str:
        """Create the prompt for sequence LinkedIn message generation."""

        template = context['template']
        sequence_position = context['sequence_position']

        # Get sequence-specific guidance
        sequence_guidance = {
            1: "This is the connection request or first message - be brief and intriguing",
            2: "This is the follow-up message - provide specific value or insight",
            3: "This is the final message - soft close with clear next step"
        }

        audit_insight = self._get_audit_insight(context)

        prompt = f"""
Write LinkedIn message #{sequence_position} in a 3-message sequence. Here's the context:

Company: {context['company_name']}
Contact: {context['first_name']}
Title: {context.get('title', 'N/A')}
Tone: {context['tone']}

Audit Results:
- Response Grade: {context['grade']}
- Response Time: {context['response_time']}
- Current Conversion Rate: {context['conversion_rate']}%

Message Template: {template['type']}
Focus: {template['focus']}
CTA: {template['cta']}

Sequence Guidance: {sequence_guidance.get(sequence_position, 'Continue the conversation')}

Key Insight: {audit_insight}

Requirements:
1. Keep it under 300 characters (LinkedIn limit)
2. Be conversational and professional
3. Reference their specific audit findings briefly
4. Build on the previous message if this isn't message #1
5. Include a soft call-to-action
6. Don't be pushy or salesy
{"7. Mention insights available to share" if include_audit_link else ""}

Write just the message body, no subject line needed.
"""
        return prompt

# Initialize the generator
outreach_generator = OutreachGenerator()

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "outreach-agent"}

@app.post("/generate-outreach", response_model=OutreachResponse)
async def generate_outreach(request: OutreachRequest):
    """Generate outreach messages for a lead based on their audit report."""

    try:
        # Fetch lead data
        lead_response = supabase.table("leads").select("*").eq("id", request.lead_id).execute()
        if not lead_response.data:
            raise HTTPException(status_code=404, detail="Lead not found")

        lead_data = lead_response.data[0]

        # Fetch audit report data
        audit_response = supabase.table("audit_reports").select("*").eq("id", request.audit_report_id).execute()
        if not audit_response.data:
            raise HTTPException(status_code=404, detail="Audit report not found")

        audit_data = audit_response.data[0]

        generated_messages = []

        # Generate requested message types
        for message_type in request.message_types:
            if message_type == "email":
                if request.generate_sequences:
                    # Generate 5-email sequence
                    email_sequence = await outreach_generator.generate_email_sequence(
                        lead_data, audit_data, request.tone, request.include_audit_link, request.sequence_spacing_days
                    )

                    # Save each email in the sequence
                    for email_data in email_sequence:
                        message_id = str(uuid.uuid4())
                        message_record = {
                            "id": message_id,
                            "lead_id": request.lead_id,
                            "audit_report_id": request.audit_report_id,
                            "message_type": "email",
                            "status": "draft",
                            "subject_line": email_data["subject_line"],
                            "message_body": email_data["message_body"],
                            "personalization_data": email_data["personalization_data"],
                            "to_email": lead_data.get("email"),
                            "ai_model_used": outreach_generator.model,
                            "sequence_position": email_data["sequence_position"],
                            "send_delay_days": email_data["send_delay_days"],
                            "template_type": email_data["template_type"],
                            "template_focus": email_data["template_focus"],
                            "created_at": datetime.utcnow().isoformat()
                        }

                        supabase.table("outreach_messages").insert(message_record).execute()
                        generated_messages.append(message_record)
                else:
                    # Generate single email
                    email_data = await outreach_generator.generate_email(
                        lead_data, audit_data, request.tone, request.include_audit_link
                    )

                    message_id = str(uuid.uuid4())
                    message_record = {
                        "id": message_id,
                        "lead_id": request.lead_id,
                        "audit_report_id": request.audit_report_id,
                        "message_type": "email",
                        "status": "draft",
                        "subject_line": email_data["subject_line"],
                        "message_body": email_data["message_body"],
                        "personalization_data": email_data["personalization_data"],
                        "to_email": lead_data.get("email"),
                        "ai_model_used": outreach_generator.model,
                        "sequence_position": 1,
                        "send_delay_days": 0,
                        "created_at": datetime.utcnow().isoformat()
                    }

                    supabase.table("outreach_messages").insert(message_record).execute()
                    generated_messages.append(message_record)

            elif message_type == "linkedin":
                if request.generate_sequences:
                    # Generate 3-LinkedIn sequence
                    linkedin_sequence = await outreach_generator.generate_linkedin_sequence(
                        lead_data, audit_data, request.tone, request.include_audit_link
                    )

                    # Save each LinkedIn message in the sequence
                    for linkedin_data in linkedin_sequence:
                        message_id = str(uuid.uuid4())
                        message_record = {
                            "id": message_id,
                            "lead_id": request.lead_id,
                            "audit_report_id": request.audit_report_id,
                            "message_type": "linkedin",
                            "status": "draft",
                            "message_body": linkedin_data["message_body"],
                            "personalization_data": linkedin_data["personalization_data"],
                            "linkedin_profile_url": lead_data.get("linkedin"),
                            "ai_model_used": outreach_generator.model,
                            "sequence_position": linkedin_data["sequence_position"],
                            "send_delay_days": linkedin_data["send_delay_days"],
                            "template_type": linkedin_data["template_type"],
                            "template_focus": linkedin_data["template_focus"],
                            "created_at": datetime.utcnow().isoformat()
                        }

                        supabase.table("outreach_messages").insert(message_record).execute()
                        generated_messages.append(message_record)
                else:
                    # Generate single LinkedIn message
                    linkedin_data = await outreach_generator.generate_linkedin_message(
                        lead_data, audit_data, request.tone, request.include_audit_link
                    )

                    message_id = str(uuid.uuid4())
                    message_record = {
                        "id": message_id,
                        "lead_id": request.lead_id,
                        "audit_report_id": request.audit_report_id,
                        "message_type": "linkedin",
                        "status": "draft",
                        "message_body": linkedin_data["message_body"],
                        "personalization_data": linkedin_data["personalization_data"],
                        "linkedin_profile_url": lead_data.get("linkedin"),
                        "ai_model_used": outreach_generator.model,
                        "sequence_position": 1,
                        "send_delay_days": 0,
                        "created_at": datetime.utcnow().isoformat()
                    }

                    supabase.table("outreach_messages").insert(message_record).execute()
                    generated_messages.append(message_record)

        # Update lead state
        supabase.table("lead_states").upsert({
            "lead_id": request.lead_id,
            "outreach_generated": True,
            "outreach_generated_at": datetime.utcnow().isoformat(),
            "current_state": "outreach_generated"
        }).execute()

        return OutreachResponse(
            success=True,
            message=f"Generated {len(generated_messages)} outreach messages",
            generated_messages=generated_messages,
            lead_id=request.lead_id,
            audit_report_id=request.audit_report_id
        )

    except Exception as e:
        logger.error(f"Error generating outreach: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating outreach: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8007)
