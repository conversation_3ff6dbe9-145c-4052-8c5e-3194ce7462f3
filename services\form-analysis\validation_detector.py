from typing import Dict, Any, List, Optional
import re
import asyncio

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger

# Initialize logger
logger = get_service_logger("form-analysis", "validation-detector")


class ValidationDetector:
    """
    Detector for form field validation patterns.
    """
    
    def __init__(self):
        """Initialize the validation detector."""
        self.logger = logger
    
    async def detect_validation(
        self, 
        html_content: str,
        fields: Dict[str, Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        Detect validation patterns for form fields.
        
        Args:
            html_content: HTML content of the page
            fields: Form fields data
            
        Returns:
            Dictionary mapping field names to validation patterns
        """
        self.logger.info(f"Detecting validation patterns for {len(fields)} fields")
        
        validation_patterns = {}
        
        # Extract JavaScript from the HTML
        js_code = self._extract_javascript(html_content)
        
        # Look for validation patterns in the JavaScript
        for field_name, field_data in fields.items():
            pattern = self._find_validation_pattern(
                js_code, 
                field_name, 
                field_data.get("id", ""),
                field_data.get("field_type", "")
            )
            
            if pattern:
                validation_patterns[field_name] = pattern
                self.logger.info(f"Found validation pattern for field {field_name}: {pattern}")
        
        return validation_patterns
    
    def _extract_javascript(self, html_content: str) -> str:
        """
        Extract JavaScript code from HTML.
        
        Args:
            html_content: HTML content of the page
            
        Returns:
            Extracted JavaScript code
        """
        # Find all script tags
        script_pattern = re.compile(r'<script[^>]*>(.*?)</script>', re.DOTALL)
        scripts = script_pattern.findall(html_content)
        
        # Combine all scripts
        js_code = "\n".join(scripts)
        
        return js_code
    
    def _find_validation_pattern(
        self, 
        js_code: str, 
        field_name: str, 
        field_id: str,
        field_type: str
    ) -> Optional[str]:
        """
        Find validation pattern for a field in JavaScript code.
        
        Args:
            js_code: JavaScript code to search
            field_name: Name of the field
            field_id: ID of the field
            field_type: Type of the field
            
        Returns:
            Validation pattern or None if not found
        """
        # Look for common validation patterns
        
        # Check for regex patterns
        regex_patterns = [
            # Look for regex patterns associated with the field
            rf'/{field_name}.*?pattern.*?["\']([^"\']+)["\']',
            rf'/{field_id}.*?pattern.*?["\']([^"\']+)["\']',
            rf'/validate.*?{field_name}.*?["\']([^"\']+)["\']',
            rf'/validate.*?{field_id}.*?["\']([^"\']+)["\']',
            # Look for regex literals
            rf'/new RegExp.*?{field_name}.*?["\']([^"\']+)["\']',
            rf'/new RegExp.*?{field_id}.*?["\']([^"\']+)["\']'
        ]
        
        for pattern in regex_patterns:
            match = re.search(pattern, js_code, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(1)
        
        # If no specific pattern found, return common patterns based on field type
        if field_type == "email":
            return r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        elif field_type == "phone":
            return r"^\+?[0-9]{10,15}$"
        
        return None
