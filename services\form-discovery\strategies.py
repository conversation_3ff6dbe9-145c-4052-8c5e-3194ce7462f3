from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import logging
import re
import os
from urllib.parse import urljoin, urlparse

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger

# Initialize logger
logger = get_service_logger("form-discovery", "strategies")


class FormDiscoveryStrategy(ABC):
    """
    Abstract base class for form discovery strategies.
    """
    
    def __init__(self, name: str):
        self.name = name
        self.logger = get_service_logger("form-discovery", f"strategy.{name}")
        self.confidence_score = 0.0
    
    @abstractmethod
    async def execute(self, url: str, browser_pool) -> List[Dict[str, Any]]:
        """
        Execute the strategy to discover forms on a website.
        
        Args:
            url: The URL of the website to analyze
            browser_pool: A pool of browser instances to use
            
        Returns:
            A list of discovered forms with their metadata
        """
        pass
    
    def is_high_confidence(self) -> bool:
        """
        Check if the strategy has high confidence in its results.
        
        Returns:
            True if the confidence score is high, False otherwise
        """
        return self.confidence_score > 0.8


class DirectFormStrategy(FormDiscoveryStrategy):
    """
    Strategy that looks for forms directly on the current page.
    """
    
    def __init__(self):
        super().__init__("direct-form")
    
    async def execute(self, url: str, browser_pool) -> List[Dict[str, Any]]:
        """
        Execute the direct form discovery strategy.
        
        Args:
            url: The URL of the website to analyze
            browser_pool: A pool of browser instances to use
            
        Returns:
            A list of discovered forms with their metadata
        """
        self.logger.info(f"Executing direct form strategy on {url}")
        
        try:
            # Get a browser from the pool
            async with browser_pool.get() as browser:
                # Navigate to the URL
                await browser.goto(url)
                
                # Wait for the page to load
                await browser.wait_for_load_state("networkidle")
                
                # Take a screenshot
                screenshot_path = f"screenshots/{urlparse(url).netloc}_{os.path.basename(urlparse(url).path or 'index')}.png"
                os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
                await browser.screenshot(path=screenshot_path)
                
                # Get the HTML content
                html_content = await browser.content()
                
                # Find all forms on the page
                forms = await browser.eval_js("""
                    Array.from(document.querySelectorAll('form')).map(form => {
                        const fields = Array.from(form.querySelectorAll('input, select, textarea')).map(field => {
                            return {
                                name: field.name || '',
                                id: field.id || '',
                                type: field.type || 'text',
                                required: field.required || false,
                                placeholder: field.placeholder || '',
                                value: field.value || ''
                            };
                        });
                        
                        return {
                            action: form.action || '',
                            method: form.method || 'POST',
                            id: form.id || '',
                            className: form.className || '',
                            fields: fields
                        };
                    });
                """)
                
                # Process the forms
                result = []
                for form in forms:
                    # Check if it's likely a contact form
                    if self._is_contact_form(form):
                        # Get form metadata
                        form_data = {
                            "url": url,
                            "title": self._extract_form_title(form, html_content),
                            "description": self._extract_form_description(form, html_content),
                            "fields": self._process_fields(form["fields"]),
                            "multi_step": self._is_multi_step(form, html_content),
                            "has_captcha": self._has_captcha(form, html_content),
                            "submission_endpoint": form["action"],
                            "method": form["method"],
                            "screenshot_path": screenshot_path,
                            "html_content": html_content
                        }
                        
                        result.append(form_data)
                        
                        self.logger.info(f"Found contact form on {url}", props={
                            "form_action": form["action"],
                            "field_count": len(form["fields"])
                        })
                
                # Set confidence score based on results
                self.confidence_score = 0.9 if result else 0.2
                
                return result
                
        except Exception as e:
            self.logger.error(f"Error executing direct form strategy: {str(e)}")
            self.confidence_score = 0.0
            return []
    
    def _is_contact_form(self, form: Dict[str, Any]) -> bool:
        """
        Check if a form is likely a contact form.
        
        Args:
            form: The form data to check
            
        Returns:
            True if the form is likely a contact form, False otherwise
        """
        # Check form action or ID for contact-related keywords
        action = form.get("action", "").lower()
        form_id = form.get("id", "").lower()
        form_class = form.get("className", "").lower()
        
        contact_keywords = ["contact", "inquiry", "enquiry", "message", "feedback", "support", "get-in-touch"]
        
        # Check if any contact keywords are in the form action, ID, or class
        if any(keyword in action for keyword in contact_keywords) or \
           any(keyword in form_id for keyword in contact_keywords) or \
           any(keyword in form_class for keyword in contact_keywords):
            return True
        
        # Check if the form has typical contact form fields
        fields = form.get("fields", [])
        field_names = [field.get("name", "").lower() for field in fields]
        field_types = [field.get("type", "").lower() for field in fields]
        
        # Check for email field
        if "email" in field_names or "email" in field_types:
            return True
        
        # Check for name and message fields
        if ("name" in field_names or "first" in field_names) and "message" in field_names:
            return True
        
        return False
    
    def _extract_form_title(self, form: Dict[str, Any], html_content: str) -> Optional[str]:
        """Extract a title for the form from surrounding HTML."""
        # Implementation would use regex or DOM parsing to find a heading near the form
        return "Contact Form"  # Simplified implementation
    
    def _extract_form_description(self, form: Dict[str, Any], html_content: str) -> Optional[str]:
        """Extract a description for the form from surrounding HTML."""
        # Implementation would use regex or DOM parsing to find text near the form
        return "Send us a message"  # Simplified implementation
    
    def _process_fields(self, fields: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process form fields to extract metadata."""
        processed_fields = []
        for field in fields:
            processed_field = {
                "name": field.get("name", ""),
                "field_type": self._map_field_type(field),
                "required": field.get("required", False),
                "placeholder": field.get("placeholder", "")
            }
            processed_fields.append(processed_field)
        return processed_fields
    
    def _map_field_type(self, field: Dict[str, Any]) -> str:
        """Map HTML field types to our field type enum."""
        field_type = field.get("type", "text").lower()
        name = field.get("name", "").lower()
        
        # Map common field types
        if field_type == "email" or "email" in name:
            return "email"
        elif field_type == "tel" or "phone" in name or "tel" in name:
            return "phone"
        elif "name" in name or "first" in name or "last" in name:
            return "name"
        elif "company" in name or "organization" in name:
            return "company"
        elif field_type == "textarea" or "message" in name or "comment" in name:
            return "message"
        elif field_type == "checkbox":
            return "checkbox"
        elif field_type == "radio":
            return "radio"
        elif field_type == "select":
            return "select"
        elif field_type == "file":
            return "file"
        elif field_type == "submit":
            return "submit"
        else:
            return "text"
    
    def _is_multi_step(self, form: Dict[str, Any], html_content: str) -> bool:
        """Check if the form is likely multi-step."""
        # Look for indicators of multi-step forms
        return False  # Simplified implementation
    
    def _has_captcha(self, form: Dict[str, Any], html_content: str) -> bool:
        """Check if the form has a CAPTCHA."""
        # Look for common CAPTCHA implementations
        html_lower = html_content.lower()
        captcha_indicators = [
            "captcha",
            "recaptcha",
            "g-recaptcha",
            "hcaptcha",
            "cf-turnstile"
        ]
        
        return any(indicator in html_lower for indicator in captcha_indicators)


class ContactPageStrategy(FormDiscoveryStrategy):
    """
    Strategy that looks for contact pages and then analyzes them for forms.
    """
    
    def __init__(self):
        super().__init__("contact-page")
    
    async def execute(self, url: str, browser_pool) -> List[Dict[str, Any]]:
        """
        Execute the contact page discovery strategy.
        
        Args:
            url: The URL of the website to analyze
            browser_pool: A pool of browser instances to use
            
        Returns:
            A list of discovered forms with their metadata
        """
        self.logger.info(f"Executing contact page strategy on {url}")
        
        try:
            # Get a browser from the pool
            async with browser_pool.get() as browser:
                # Navigate to the URL
                await browser.goto(url)
                
                # Wait for the page to load
                await browser.wait_for_load_state("networkidle")
                
                # Find contact page links
                contact_links = await browser.eval_js("""
                    Array.from(document.querySelectorAll('a')).filter(link => {
                        const text = link.textContent.toLowerCase();
                        const href = link.href.toLowerCase();
                        return text.includes('contact') || 
                               text.includes('get in touch') || 
                               href.includes('contact') || 
                               href.includes('get-in-touch');
                    }).map(link => link.href);
                """)
                
                result = []
                
                # If we found contact links, visit each one and look for forms
                if contact_links:
                    self.logger.info(f"Found {len(contact_links)} potential contact page links")
                    
                    for contact_url in contact_links:
                        # Use the direct form strategy on the contact page
                        direct_strategy = DirectFormStrategy()
                        contact_forms = await direct_strategy.execute(contact_url, browser_pool)
                        
                        if contact_forms:
                            result.extend(contact_forms)
                            self.logger.info(f"Found {len(contact_forms)} forms on contact page {contact_url}")
                
                # Set confidence score based on results
                self.confidence_score = 0.85 if result else 0.3
                
                return result
                
        except Exception as e:
            self.logger.error(f"Error executing contact page strategy: {str(e)}")
            self.confidence_score = 0.0
            return []


class NavigationMenuStrategy(FormDiscoveryStrategy):
    """
    Strategy that analyzes navigation menus to find potential form pages.
    """
    
    def __init__(self):
        super().__init__("navigation-menu")
    
    async def execute(self, url: str, browser_pool) -> List[Dict[str, Any]]:
        """
        Execute the navigation menu strategy.
        
        Args:
            url: The URL of the website to analyze
            browser_pool: A pool of browser instances to use
            
        Returns:
            A list of discovered forms with their metadata
        """
        # Implementation would look for navigation menus and analyze their links
        # Simplified implementation for now
        self.logger.info(f"Executing navigation menu strategy on {url}")
        self.confidence_score = 0.5
        return []


class PopupDetectionStrategy(FormDiscoveryStrategy):
    """
    Strategy that waits for and analyzes popup forms.
    """
    
    def __init__(self):
        super().__init__("popup-detection")
    
    async def execute(self, url: str, browser_pool) -> List[Dict[str, Any]]:
        """
        Execute the popup detection strategy.
        
        Args:
            url: The URL of the website to analyze
            browser_pool: A pool of browser instances to use
            
        Returns:
            A list of discovered forms with their metadata
        """
        # Implementation would wait for popups and analyze them for forms
        # Simplified implementation for now
        self.logger.info(f"Executing popup detection strategy on {url}")
        self.confidence_score = 0.4
        return []


class SitemapExplorationStrategy(FormDiscoveryStrategy):
    """
    Strategy that explores the sitemap to find potential form pages.
    """
    
    def __init__(self):
        super().__init__("sitemap-exploration")
    
    async def execute(self, url: str, browser_pool) -> List[Dict[str, Any]]:
        """
        Execute the sitemap exploration strategy.
        
        Args:
            url: The URL of the website to analyze
            browser_pool: A pool of browser instances to use
            
        Returns:
            A list of discovered forms with their metadata
        """
        # Implementation would look for sitemaps and explore them for form pages
        # Simplified implementation for now
        self.logger.info(f"Executing sitemap exploration strategy on {url}")
        self.confidence_score = 0.6
        return []
