"""
Audit Logger Module

This module handles logging of response monitoring results to CSV and Airtable.
"""
import csv
import json
import os
from typing import Dict, Any, List, Optional, Union, Callable
import datetime
import requests
import time

class AuditLogger:
    """
    Audit Logger for response monitoring system.
    Creates structured logs in CSV format and optionally syncs to Airtable.
    """
    
    def __init__(self, 
                log_dir: str = "./logs",
                csv_filename: str = "response_audit_log.csv",
                enable_airtable: bool = False,
                airtable_api_key: Optional[str] = None,
                airtable_base_id: Optional[str] = None,
                airtable_table_name: Optional[str] = None):
        """
        Initialize the audit logger.
        
        Args:
            log_dir: Directory to store log files
            csv_filename: Name of the CSV log file
            enable_airtable: Whether to sync logs to Airtable
            airtable_api_key: Airtable API key (required if enable_airtable=True)
            airtable_base_id: Airtable base ID (required if enable_airtable=True)
            airtable_table_name: Airtable table name (required if enable_airtable=True)
        """
        self.log_dir = log_dir
        self.csv_filename = csv_filename
        self.enable_airtable = enable_airtable
        self.airtable_api_key = airtable_api_key
        self.airtable_base_id = airtable_base_id
        self.airtable_table_name = airtable_table_name
        
        # Create log directory if it doesn't exist
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Initialize CSV headers if file doesn't exist
        self.csv_path = os.path.join(log_dir, csv_filename)
        if not os.path.exists(self.csv_path):
            self._initialize_csv()
    
    def _initialize_csv(self):
        """Initialize the CSV file with headers."""
        headers = [
            "timestamp",
            "message_id",
            "source",
            "from",
            "to",
            "classification",
            "requires_human",
            "response_generated",
            "response_sent",
            "routed_to_human",
            "processing_time_ms",
            "entities",
            "notes"
        ]
        
        with open(self.csv_path, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(headers)
    
    def log_result(self, result: Dict[str, Any]) -> bool:
        """
        Log a processing result to CSV and optionally Airtable.
        
        Args:
            result: Processing result data
            
        Returns:
            bool: Success status
        """
        try:
            # Extract message data
            message = result.get("message", {})
            classification = result.get("classification", {})
            
            # Calculate processing time if available
            processing_time_ms = None
            start_time = result.get("processing_start")
            end_time = result.get("processing_completed")
            
            if start_time and end_time:
                try:
                    start_dt = datetime.fromisoformat(start_time)
                    end_dt = datetime.fromisoformat(end_time)
                    processing_time_ms = int((end_dt - start_dt).total_seconds() * 1000)
                except (ValueError, TypeError):
                    pass
            
            # Prepare log entry
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "message_id": message.get("id", ""),
                "source": message.get("source", "unknown"),
                "from": message.get("from", ""),
                "to": message.get("to", ""),
                "classification": classification.get("category", "UNKNOWN"),
                "requires_human": str(classification.get("requires_human", False)),
                "response_generated": str(result.get("response") is not None),
                "response_sent": str(result.get("response_sent", False)),
                "routed_to_human": str(result.get("routed_to_human", False)),
                "processing_time_ms": str(processing_time_ms) if processing_time_ms is not None else "",
                "entities": json.dumps(result.get("entities", {})),
                "notes": result.get("response_explanation", "")
            }
            
            # Write to CSV
            self._write_to_csv(log_entry)
            
            # Sync to Airtable if enabled
            if self.enable_airtable:
                self._sync_to_airtable(log_entry)
            
            return True
        except Exception as e:
            print(f"Error logging result: {e}")
            return False
    
    def _write_to_csv(self, log_entry: Dict[str, str]):
        """
        Write a log entry to the CSV file.
        
        Args:
            log_entry: Log entry data
        """
        # Get the headers from the existing CSV
        with open(self.csv_path, 'r', newline='') as csvfile:
            reader = csv.reader(csvfile)
            headers = next(reader)
        
        # Write the log entry to the CSV
        with open(self.csv_path, 'a', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writerow(log_entry)
    
    def _sync_to_airtable(self, log_entry: Dict[str, str]) -> bool:
        """
        Sync a log entry to Airtable.
        
        Args:
            log_entry: Log entry data
            
        Returns:
            bool: Success status
        """
        if not all([self.airtable_api_key, self.airtable_base_id, self.airtable_table_name]):
            print("Airtable sync failed: Missing Airtable configuration")
            return False
        
        try:
            # Prepare Airtable record
            airtable_record = {
                "fields": {
                    # Map CSV fields to Airtable fields
                    # This assumes the Airtable table has matching column names
                    "Timestamp": log_entry["timestamp"],
                    "Message ID": log_entry["message_id"],
                    "Source": log_entry["source"],
                    "From": log_entry["from"],
                    "To": log_entry["to"],
                    "Classification": log_entry["classification"],
                    "Requires Human": log_entry["requires_human"] == "True",
                    "Response Generated": log_entry["response_generated"] == "True",
                    "Response Sent": log_entry["response_sent"] == "True",
                    "Routed to Human": log_entry["routed_to_human"] == "True",
                    "Processing Time (ms)": int(log_entry["processing_time_ms"]) if log_entry["processing_time_ms"] else None,
                    "Entities": log_entry["entities"],
                    "Notes": log_entry["notes"]
                }
            }
            
            # Send to Airtable API
            url = f"https://api.airtable.com/v0/{self.airtable_base_id}/{self.airtable_table_name}"
            headers = {
                "Authorization": f"Bearer {self.airtable_api_key}",
                "Content-Type": "application/json"
            }
            
            response = requests.post(url, json=airtable_record, headers=headers)
            
            if response.status_code == 200:
                return True
            else:
                print(f"Airtable sync failed: {response.status_code} {response.text}")
                return False
                
        except Exception as e:
            print(f"Error syncing to Airtable: {e}")
            return False
    
    def get_log_summary(self, days: int = 7) -> Dict[str, Any]:
        """
        Get a summary of recent log entries.
        
        Args:
            days: Number of days to include in the summary
            
        Returns:
            Summary statistics
        """
        try:
            # Calculate the cutoff date
            cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)
            cutoff_iso = cutoff_date.isoformat()
            
            # Read the CSV file
            log_entries = []
            with open(self.csv_path, 'r', newline='') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    if row["timestamp"] >= cutoff_iso:
                        log_entries.append(row)
            
            # Calculate statistics
            total_entries = len(log_entries)
            
            source_counts = {}
            classification_counts = {}
            human_intervention_count = 0
            response_sent_count = 0
            
            for entry in log_entries:
                # Count by source
                source = entry["source"]
                source_counts[source] = source_counts.get(source, 0) + 1
                
                # Count by classification
                classification = entry["classification"]
                classification_counts[classification] = classification_counts.get(classification, 0) + 1
                
                # Count human interventions
                if entry["routed_to_human"] == "True":
                    human_intervention_count += 1
                
                # Count responses sent
                if entry["response_sent"] == "True":
                    response_sent_count += 1
            
            # Calculate average processing time
            processing_times = [int(entry["processing_time_ms"]) for entry in log_entries 
                              if entry["processing_time_ms"] and entry["processing_time_ms"].isdigit()]
            avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
            
            return {
                "period_days": days,
                "total_entries": total_entries,
                "source_distribution": source_counts,
                "classification_distribution": classification_counts,
                "human_intervention_rate": human_intervention_count / total_entries if total_entries > 0 else 0,
                "response_rate": response_sent_count / total_entries if total_entries > 0 else 0,
                "avg_processing_time_ms": avg_processing_time
            }
        except Exception as e:
            print(f"Error generating log summary: {e}")
            return {
                "error": str(e),
                "period_days": days
            }
    
    def export_logs(self, 
                   output_file: str, 
                   format: str = "csv",
                   filter_func: Optional[Callable[[Dict[str, str]], bool]] = None) -> bool:
        """
        Export logs to a file.
        
        Args:
            output_file: Output file path
            format: Output format ("csv" or "json")
            filter_func: Optional function to filter log entries
            
        Returns:
            bool: Success status
        """
        try:
            # Read the CSV file
            with open(self.csv_path, 'r', newline='') as csvfile:
                reader = csv.DictReader(csvfile)
                log_entries = list(reader)
            
            # Apply filter if provided
            if filter_func:
                log_entries = [entry for entry in log_entries if filter_func(entry)]
            
            # Write to output file
            if format.lower() == "csv":
                with open(output_file, 'w', newline='') as outfile:
                    if log_entries:
                        writer = csv.DictWriter(outfile, fieldnames=log_entries[0].keys())
                        writer.writeheader()
                        writer.writerows(log_entries)
            elif format.lower() == "json":
                with open(output_file, 'w') as outfile:
                    json.dump(log_entries, outfile, indent=2)
            else:
                print(f"Unsupported export format: {format}")
                return False
            
            return True
        except Exception as e:
            print(f"Error exporting logs: {e}")
            return False


import os
import datetime
import csv
from typing import Dict, Any, List

try:
    from supabase import create_client, Client
except ImportError:
    create_client = None  # Will print warning if not available

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

def audit_response_times():
    """
    For each lead, list all response channels and the time-to-response from form submission.
    Output: List of dicts with lead info, channel, response_time, and delay.
    """
    if not create_client:
        print("[WARN] supabase-py not installed. Please install with 'pip install supabase'.")
        return []
    if not SUPABASE_URL or not SUPABASE_SERVICE_ROLE_KEY:
        print("[ERROR] SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in environment.")
        return []
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)
    # Fetch all leads
    leads_resp = supabase.table("leads").select("id, first_name, last_name, email, created_at").execute()
    leads = leads_resp.data if hasattr(leads_resp, 'data') else leads_resp
    # Fetch all responses
    responses_resp = supabase.table("responses").select("id, lead_id, channel, received_at").execute()
    responses = responses_resp.data if hasattr(responses_resp, 'data') else responses_resp
    # Index responses by lead_id
    responses_by_lead = {}
    for resp in responses:
        responses_by_lead.setdefault(resp["lead_id"], []).append(resp)
    audit_rows: List[Dict[str, Any]] = []
    for lead in leads:
        lead_id = lead["id"]
        form_time = lead["created_at"]
        if not form_time:
            continue
        try:
            form_dt = datetime.datetime.fromisoformat(form_time.replace("Z", "+00:00"))
        except Exception:
            print(f"[WARN] Could not parse form_time for lead {lead_id}: {form_time}")
            continue
        lead_responses = responses_by_lead.get(lead_id, [])
        channels_seen = set()
        for resp in lead_responses:
            channel = resp.get("channel", "unknown")
            resp_time = resp.get("received_at")
            if not resp_time:
                continue
            try:
                resp_dt = datetime.datetime.fromisoformat(resp_time.replace("Z", "+00:00"))
            except Exception:
                print(f"[WARN] Could not parse response_time for response {resp.get('id')}: {resp_time}")
                continue
            delay = (resp_dt - form_dt).total_seconds()
            audit_rows.append({
                "lead_id": lead_id,
                "lead_email": lead.get("email"),
                "lead_name": f"{lead.get('first_name', '')} {lead.get('last_name', '')}",
                "form_time": form_time,
                "channel": channel,
                "response_time": resp_time,
                "delay_seconds": delay,
                "delay_human": str(datetime.timedelta(seconds=int(delay)))
            })
            channels_seen.add(channel)
        if not lead_responses:
            audit_rows.append({
                "lead_id": lead_id,
                "lead_email": lead.get("email"),
                "lead_name": f"{lead.get('first_name', '')} {lead.get('last_name', '')}",
                "form_time": form_time,
                "channel": "NO_RESPONSE",
                "response_time": None,
                "delay_seconds": None,
                "delay_human": None
            })
    # Write to CSV
    out_path = os.path.join("./logs", "audit_response_times.csv")
    os.makedirs(os.path.dirname(out_path), exist_ok=True)
    with open(out_path, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=audit_rows[0].keys())
        writer.writeheader()
        writer.writerows(audit_rows)
    print(f"[INFO] Audit report written to {out_path} ({len(audit_rows)} rows)")
    return audit_rows

# Example usage
if __name__ == "__main__":
    # Create logger
    logger = AuditLogger(
        log_dir="./logs",
        csv_filename="response_audit_log.csv",
        enable_airtable=False
        # Add Airtable credentials if needed:
        # airtable_api_key="YOUR_AIRTABLE_API_KEY",
        # airtable_base_id="YOUR_AIRTABLE_BASE_ID",
        # airtable_table_name="response_monitoring"
    )

    # Run audit
    audit_response_times()

    # Example log entry
    example_result = {
        "message": {
            "id": "msg_123456",
            "source": "sms",
            "from": "+12025550123",
            "to": "+12025550789",
            "text": "Yes, I'm interested in your service. Please call me back."
        },
        "classification": {
            "category": "RESPONSE",
            "requires_human": True
        },
        "entities": {
            "intent": "positive_response",
            "request": "call_back"
        },
        "response": None,
        "response_explanation": "This message requires human follow-up as it's a positive response requesting a call back.",
        "routed_to_human": True,
        "processing_start": "2023-08-15T14:23:45.123456",
        "processing_completed": "2023-08-15T14:23:46.234567"
    }
    
    # Log the result
    logger.log_result(example_result)
    
    # Get a summary of recent logs
    summary = logger.get_log_summary(days=7)
    print(json.dumps(summary, indent=2))
