"""
Audit Report Generator

This module generates audit reports for lead responses based on a scoring system.
"""
import os
import json
import csv
import datetime
from typing import Dict, Any, List, Optional, Union, Tuple
import statistics
import random
from collections import defaultdict

try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except (ImportError, TypeError) as e:
    print(f"[WARN] supabase-py not available: {e}")
    print("[INFO] Using mock data instead of Supabase.")
    create_client = None
    SUPABASE_AVAILABLE = False

class AuditReportGenerator:
    """
    Generates audit reports for lead responses based on a scoring system.
    """

    def __init__(self,
                 supabase_url: Optional[str] = None,
                 supabase_key: Optional[str] = None,
                 output_dir: str = "./reports"):
        """
        Initialize the audit report generator.

        Args:
            supabase_url: Supabase URL
            supabase_key: Supabase service role key
            output_dir: Directory for output reports
        """
        self.supabase_url = supabase_url or os.environ.get("SUPABASE_URL")
        self.supabase_key = supabase_key or os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
        self.output_dir = output_dir
        self.supabase = None

        # Initialize Supabase client
        self.use_mock_data = True  # Default to using mock data

        if SUPABASE_AVAILABLE and self.supabase_url and self.supabase_key:
            try:
                self.supabase = create_client(self.supabase_url, self.supabase_key)
                print(f"[INFO] Supabase client initialized: {self.supabase_url}")
                self.use_mock_data = False  # Use real data if Supabase is available
            except Exception as e:
                print(f"[ERROR] Failed to initialize Supabase client: {e}")
                print("[INFO] Using mock data instead.")
        else:
            print("[INFO] Using mock data for audit reports.")

        # Create output directory if it doesn't exist
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

    def calculate_time_score(self, delay_seconds: float) -> int:
        """
        Calculate the time score based on response delay.

        Args:
            delay_seconds: Delay in seconds

        Returns:
            Time score (0-100)
        """
        # Convert seconds to minutes for easier comparison
        delay_minutes = delay_seconds / 60

        # Calculate score based on response time
        if delay_minutes < 5:
            return 100  # Under 5 min: 100 points
        elif delay_minutes < 10:
            return 80   # 5-10 min: 80 points
        elif delay_minutes < 30:
            return 60   # 10-30 min: 60 points
        elif delay_minutes < 60:
            return 40   # 30-60 min: 40 points
        elif delay_minutes < 180:
            return 25   # 1-3 hours: 25 points
        elif delay_minutes < 1440:
            return 10   # 3-24 hours: 10 points
        else:
            return 5    # Over 24 hours: 5 points

    def calculate_channel_bonus(self, channel: str) -> int:
        """
        Calculate the channel bonus based on response channel.

        Args:
            channel: Response channel

        Returns:
            Channel bonus (0-25)
        """
        channel = channel.lower()

        if "phone" in channel or "call" in channel or "voice" in channel:
            return 25  # Phone Call: +25 points
        elif "sms" in channel or "text" in channel:
            return 15  # SMS/Text: +15 points
        elif "email" in channel:
            return 5   # Email: +5 points
        else:
            return 0   # Autoresponder or unknown: +0 points

    def get_grade(self, total_score: int) -> Tuple[str, str]:
        """
        Get the grade and feedback based on total score.

        Args:
            total_score: Total score (0-125)

        Returns:
            Tuple of (grade, feedback)
        """
        if total_score >= 115:
            return "A+", "🟢 Elite Operator — You dominate your market. Stay sharp."
        elif total_score >= 100:
            return "A", "🟢 Strong but vulnerable — One slip and someone better eats your lunch."
        elif total_score >= 80:
            return "B", "🟠 You're average. Leads are slipping past you daily. Fix it or fall."
        elif total_score >= 60:
            return "C", "🔴 You're slow. You lose to the first responder — always."
        elif total_score >= 30:
            return "D", "🔴 You're wasting ad spend. This is costing you thousands/month."
        else:
            return "F", "⚫ Broken. Your sales process is a crime scene. Leads die here."

    def get_punchline(self, total_score: int) -> str:
        """
        Get a punchline based on total score.

        Args:
            total_score: Total score (0-125)

        Returns:
            Punchline
        """
        if total_score < 30:
            return "This is the equivalent of paying $200 for a lead and then ghosting them."
        elif total_score < 60:
            return "Your response speed is costing you 6 out of every 10 deals."
        elif total_score < 80:
            return "If you were in a race with your competitors, you'd be in the parking lot while they're cashing checks."
        elif total_score < 100:
            return "You're not slow. You're invisible."
        else:
            return "You're in the top tier of responders. Keep it up!"

    def generate_audit_report(self,
                             lead_id: Optional[str] = None,
                             start_date: Optional[datetime.datetime] = None,
                             end_date: Optional[datetime.datetime] = None,
                             output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate an audit report for lead responses.

        Args:
            lead_id: Lead ID to filter by
            start_date: Start date for filtering
            end_date: End date for filtering
            output_file: Output file path

        Returns:
            Report data
        """
        # Set default dates if not provided
        if not end_date:
            end_date = datetime.datetime.now()
        if not start_date:
            start_date = end_date - datetime.timedelta(days=30)

        # Set default output file if not provided
        if not output_file:
            date_str = datetime.datetime.now().strftime("%Y%m%d")
            output_file = os.path.join(self.output_dir, f"audit_report_{date_str}.csv")

        # Fetch data from Supabase or use mock data if not available
        if not self.use_mock_data and self.supabase:
            try:
                leads, responses, form_submissions = self._fetch_data(lead_id, start_date, end_date)
                print(f"[INFO] Fetched data from Supabase: {len(leads)} leads, {len(responses)} responses, {len(form_submissions)} form submissions")
            except Exception as e:
                print(f"[ERROR] Failed to fetch data from Supabase: {e}")
                print("[INFO] Falling back to mock data.")
                leads, responses, form_submissions = self._generate_mock_data()
        else:
            print("[INFO] Using mock data for audit report.")
            leads, responses, form_submissions = self._generate_mock_data()

        # Process data and calculate scores
        report_rows = self._process_data(leads, responses, form_submissions)

        # Write report to CSV
        if report_rows:
            with open(output_file, "w", newline="", encoding="utf-8") as f:
                writer = csv.DictWriter(f, fieldnames=report_rows[0].keys())
                writer.writeheader()
                writer.writerows(report_rows)
            print(f"[INFO] Audit report written to {output_file} ({len(report_rows)} rows)")
        else:
            print("[WARN] No data for audit report")

        # Calculate summary statistics
        summary = self._calculate_summary(report_rows)

        return {
            "report_file": output_file,
            "row_count": len(report_rows),
            "start_date": start_date.isoformat() if isinstance(start_date, datetime.datetime) else start_date,
            "end_date": end_date.isoformat() if isinstance(end_date, datetime.datetime) else end_date,
            "summary": summary
        }

    def _fetch_data(self,
                   lead_id: Optional[str] = None,
                   start_date: Optional[datetime.datetime] = None,
                   end_date: Optional[datetime.datetime] = None) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Fetch data from Supabase.

        Args:
            lead_id: Lead ID to filter by
            start_date: Start date for filtering
            end_date: End date for filtering

        Returns:
            Tuple of (leads, responses, form_submissions)
        """
        # Fetch leads
        leads_query = self.supabase.table("leads").select("*")
        if lead_id:
            leads_query = leads_query.eq("id", lead_id)
        if start_date:
            leads_query = leads_query.gte("created_at", start_date.isoformat())
        if end_date:
            leads_query = leads_query.lte("created_at", end_date.isoformat())
        leads_result = leads_query.execute()
        leads = leads_result.data if hasattr(leads_result, 'data') else leads_result

        # Get lead IDs
        lead_ids = [lead.get("id") for lead in leads]

        # Fetch responses
        responses_query = self.supabase.table("responses").select("*")
        if lead_ids:
            responses_query = responses_query.in_("lead_id", lead_ids)
        if start_date:
            responses_query = responses_query.gte("received_at", start_date.isoformat())
        if end_date:
            responses_query = responses_query.lte("received_at", end_date.isoformat())
        responses_result = responses_query.execute()
        responses = responses_result.data if hasattr(responses_result, 'data') else responses_result

        # Fetch form submissions
        submissions_query = self.supabase.table("form_submissions").select("*")
        if lead_ids:
            submissions_query = submissions_query.in_("lead_id", lead_ids)
        if start_date:
            submissions_query = submissions_query.gte("created_at", start_date.isoformat())
        if end_date:
            submissions_query = submissions_query.lte("created_at", end_date.isoformat())
        submissions_result = submissions_query.execute()
        form_submissions = submissions_result.data if hasattr(submissions_result, 'data') else submissions_result

        return leads, responses, form_submissions

    def _generate_mock_data(self) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Generate mock data for testing.

        Returns:
            Tuple of (leads, responses, form_submissions)
        """
        # Generate mock leads
        leads = []
        for i in range(10):
            lead_id = f"lead_{i}"
            leads.append({
                "id": lead_id,
                "first_name": f"First{i}",
                "last_name": f"Last{i}",
                "email": f"lead{i}@example.com",
                "phone": f"555-000-{i:04d}",
                "company": f"Company {i}",
                "created_at": (datetime.datetime.now() - datetime.timedelta(days=i)).isoformat()
            })

        # Generate mock form submissions
        form_submissions = []
        for i, lead in enumerate(leads):
            submission_time = datetime.datetime.now() - datetime.timedelta(days=i, hours=random.randint(0, 12))
            form_submissions.append({
                "id": f"submission_{i}",
                "lead_id": lead["id"],
                "website": f"https://example{i}.com/contact",
                "form_url": f"https://example{i}.com/contact#form",
                "created_at": submission_time.isoformat(),
                "submitted_at": submission_time.isoformat(),
                "status": "success"
            })

        # Generate mock responses with varying delays and channels
        responses = []
        channels = ["phone", "sms", "email", "email", "sms", "phone", "email", "sms", "phone", "none"]
        for i, (lead, submission) in enumerate(zip(leads, form_submissions)):
            if channels[i] == "none":
                # No response for this lead
                continue

            # Parse submission time
            submission_time = datetime.datetime.fromisoformat(submission["submitted_at"].replace("Z", "+00:00"))

            # Generate response with varying delay
            if channels[i] == "phone":
                delay_minutes = random.randint(1, 20)  # Phone calls tend to be faster
            elif channels[i] == "sms":
                delay_minutes = random.randint(5, 60)  # SMS responses are medium speed
            else:  # email
                delay_minutes = random.randint(30, 480)  # Email responses are slower

            response_time = submission_time + datetime.timedelta(minutes=delay_minutes)

            responses.append({
                "id": f"response_{i}",
                "lead_id": lead["id"],
                "channel": channels[i],
                "received_at": response_time.isoformat(),
                "sender_info": {
                    "name": f"{lead['first_name']} {lead['last_name']}",
                    "email": lead["email"] if channels[i] == "email" else None,
                    "phone": lead["phone"] if channels[i] in ["phone", "sms"] else None
                }
            })

        return leads, responses, form_submissions

    def _process_data(self,
                     leads: List[Dict[str, Any]],
                     responses: List[Dict[str, Any]],
                     form_submissions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process data and calculate scores.

        Args:
            leads: List of leads
            responses: List of responses
            form_submissions: List of form submissions

        Returns:
            List of report rows
        """
        # Index form submissions by lead ID
        submissions_by_lead = {}
        for submission in form_submissions:
            lead_id = submission.get("lead_id")
            if lead_id:
                # Keep the earliest submission for each lead
                if lead_id not in submissions_by_lead or submission.get("created_at", "") < submissions_by_lead[lead_id].get("created_at", ""):
                    submissions_by_lead[lead_id] = submission

        # Index responses by lead ID
        responses_by_lead = defaultdict(list)
        for response in responses:
            lead_id = response.get("lead_id")
            if lead_id:
                responses_by_lead[lead_id].append(response)

        # Calculate scores for each lead
        report_rows = []
        for lead in leads:
            lead_id = lead.get("id")
            lead_name = f"{lead.get('first_name', '')} {lead.get('last_name', '')}".strip()
            lead_email = lead.get("email", "")
            lead_company = lead.get("company", "")

            # Get form submission for this lead
            submission = submissions_by_lead.get(lead_id)
            if not submission:
                # Skip leads without form submissions
                continue

            submission_time = submission.get("created_at") or submission.get("submitted_at")
            if not submission_time:
                continue

            # Parse submission time
            try:
                submission_dt = datetime.datetime.fromisoformat(submission_time.replace("Z", "+00:00"))
            except Exception as e:
                print(f"[WARN] Could not parse submission time for lead {lead_id}: {submission_time}")
                continue

            # Get responses for this lead
            lead_responses = responses_by_lead.get(lead_id, [])

            if not lead_responses:
                # Include leads with no responses
                report_row = {
                    "lead_id": lead_id,
                    "lead_name": lead_name,
                    "lead_email": lead_email,
                    "lead_company": lead_company,
                    "submission_time": submission_time,
                    "channel": "NO_RESPONSE",
                    "response_time": None,
                    "delay_seconds": None,
                    "delay_human": None,
                    "time_score": 0,
                    "channel_bonus": 0,
                    "total_score": 0,
                    "grade": "F",
                    "feedback": "⚫ Dead. You're burning money.",
                    "punchline": "This is the equivalent of paying $200 for a lead and then ghosting them."
                }
                report_rows.append(report_row)
                continue

            # Find the fastest response
            fastest_response = None
            fastest_delay = float('inf')

            for response in lead_responses:
                channel = response.get("channel", "unknown")
                response_time = response.get("received_at")
                if not response_time:
                    continue

                # Parse response time
                try:
                    response_dt = datetime.datetime.fromisoformat(response_time.replace("Z", "+00:00"))
                except Exception as e:
                    print(f"[WARN] Could not parse response time for response {response.get('id')}: {response_time}")
                    continue

                # Calculate delay
                delay_seconds = (response_dt - submission_dt).total_seconds()

                # Check if this is the fastest response
                if delay_seconds < fastest_delay:
                    fastest_delay = delay_seconds
                    fastest_response = response

            # Calculate scores for the fastest response
            if fastest_response:
                channel = fastest_response.get("channel", "unknown")
                response_time = fastest_response.get("received_at")

                # Parse response time
                try:
                    response_dt = datetime.datetime.fromisoformat(response_time.replace("Z", "+00:00"))
                except Exception as e:
                    print(f"[WARN] Could not parse response time for response {fastest_response.get('id')}: {response_time}")
                    continue

                # Calculate delay
                delay_seconds = (response_dt - submission_dt).total_seconds()
                delay_human = str(datetime.timedelta(seconds=int(delay_seconds)))

                # Calculate scores
                time_score = self.calculate_time_score(delay_seconds)
                channel_bonus = self.calculate_channel_bonus(channel)
                total_score = time_score + channel_bonus

                # Get grade and feedback
                grade, feedback = self.get_grade(total_score)

                # Get punchline
                punchline = self.get_punchline(total_score)

                # Add to report
                report_row = {
                    "lead_id": lead_id,
                    "lead_name": lead_name,
                    "lead_email": lead_email,
                    "lead_company": lead_company,
                    "submission_time": submission_time,
                    "channel": channel,
                    "response_time": response_time,
                    "delay_seconds": delay_seconds,
                    "delay_human": delay_human,
                    "time_score": time_score,
                    "channel_bonus": channel_bonus,
                    "total_score": total_score,
                    "grade": grade,
                    "feedback": feedback,
                    "punchline": punchline
                }
                report_rows.append(report_row)

        return report_rows

    def _calculate_summary(self, report_rows: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate summary statistics from report rows.

        Args:
            report_rows: Report rows

        Returns:
            Summary statistics
        """
        if not report_rows:
            return {
                "total_leads": 0,
                "responded_leads": 0,
                "response_rate": 0,
                "avg_score": 0,
                "avg_grade": "F",
                "channels": {},
                "grades": {}
            }

        # Count total leads
        total_leads = len(report_rows)

        # Count leads with responses
        responded_leads = sum(1 for row in report_rows if row.get("channel") != "NO_RESPONSE")

        # Calculate response rate
        response_rate = responded_leads / total_leads if total_leads > 0 else 0

        # Calculate average score
        scores = [row.get("total_score", 0) for row in report_rows]
        avg_score = sum(scores) / len(scores) if scores else 0

        # Determine average grade
        avg_grade, _ = self.get_grade(avg_score)

        # Group by channel
        channels = defaultdict(list)
        for row in report_rows:
            channel = row.get("channel")
            if channel:
                channels[channel].append(row)

        # Calculate channel statistics
        channel_stats = {}
        for channel, rows in channels.items():
            if channel == "NO_RESPONSE":
                channel_stats[channel] = {
                    "count": len(rows),
                    "percentage": len(rows) / total_leads * 100 if total_leads > 0 else 0
                }
            else:
                scores = [row.get("total_score", 0) for row in rows]
                time_scores = [row.get("time_score", 0) for row in rows]
                delays = [row.get("delay_seconds", 0) for row in rows]

                channel_stats[channel] = {
                    "count": len(rows),
                    "percentage": len(rows) / total_leads * 100 if total_leads > 0 else 0,
                    "avg_score": sum(scores) / len(scores) if scores else 0,
                    "avg_time_score": sum(time_scores) / len(time_scores) if time_scores else 0,
                    "min_delay": min(delays) if delays else 0,
                    "max_delay": max(delays) if delays else 0,
                    "avg_delay": sum(delays) / len(delays) if delays else 0,
                    "median_delay": statistics.median(delays) if delays else 0,
                    "min_delay_human": str(datetime.timedelta(seconds=int(min(delays)))) if delays else "0:00:00",
                    "max_delay_human": str(datetime.timedelta(seconds=int(max(delays)))) if delays else "0:00:00",
                    "avg_delay_human": str(datetime.timedelta(seconds=int(sum(delays) / len(delays)))) if delays else "0:00:00",
                    "median_delay_human": str(datetime.timedelta(seconds=int(statistics.median(delays)))) if delays else "0:00:00"
                }

        # Group by grade
        grades = defaultdict(list)
        for row in report_rows:
            grade = row.get("grade")
            if grade:
                grades[grade].append(row)

        # Calculate grade statistics
        grade_stats = {}
        for grade, rows in grades.items():
            grade_stats[grade] = {
                "count": len(rows),
                "percentage": len(rows) / total_leads * 100 if total_leads > 0 else 0
            }

        return {
            "total_leads": total_leads,
            "responded_leads": responded_leads,
            "response_rate": response_rate,
            "avg_score": avg_score,
            "avg_grade": avg_grade,
            "channels": channel_stats,
            "grades": grade_stats
        }


# Example usage
if __name__ == "__main__":
    # Initialize audit report generator
    generator = AuditReportGenerator()

    # Generate audit report
    report = generator.generate_audit_report()

    # Print summary
    summary = report["summary"]
    print("\nAudit Report Summary:")
    print(f"Total Leads: {summary['total_leads']}")
    print(f"Responded Leads: {summary['responded_leads']}")
    print(f"Response Rate: {summary['response_rate']:.2%}")
    print(f"Average Score: {summary['avg_score']:.1f}")
    print(f"Average Grade: {summary['avg_grade']}")

    print("\nChannel Statistics:")
    for channel, stats in summary["channels"].items():
        if channel == "NO_RESPONSE":
            print(f"\n{channel}:")
            print(f"  Count: {stats['count']}")
            print(f"  Percentage: {stats['percentage']:.2f}%")
        else:
            print(f"\n{channel}:")
            print(f"  Count: {stats['count']}")
            print(f"  Percentage: {stats['percentage']:.2f}%")
            print(f"  Average Score: {stats['avg_score']:.1f}")
            print(f"  Average Time Score: {stats['avg_time_score']:.1f}")
            print(f"  Average Delay: {stats['avg_delay_human']}")

    print("\nGrade Statistics:")
    for grade in ["A+", "A", "B", "C", "D", "F"]:
        if grade in summary["grades"]:
            stats = summary["grades"][grade]
            print(f"  {grade}: {stats['count']} leads ({stats['percentage']:.2f}%)")
        else:
            print(f"  {grade}: 0 leads (0.00%)")
