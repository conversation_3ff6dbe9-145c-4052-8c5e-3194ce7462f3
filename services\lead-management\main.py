import os
import uuid
from typing import List, Dict, Any, Optional
from fastapi import FastAP<PERSON>, BackgroundTasks, HTTPException, Depends, status
from pydantic import BaseModel, EmailStr

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger
from libs.auth import get_current_active_user, User
from libs.interfaces import (
    Lead, 
    Event, 
    EventType, 
    LeadCreatedPayload,
    LeadUpdatedPayload,
    FormSubmittedPayload
)

# Import service-specific modules
from .lead_service import LeadService
from .event_producer import EventProducer
from .event_consumer import EventConsumer

# Initialize FastAPI app
app = FastAPI(
    title="Lead Management Service",
    description="Service for managing leads and form submissions",
    version="0.1.0"
)

# Initialize logger
logger = get_service_logger("lead-management")

# Initialize event producer
event_producer = EventProducer(
    bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092"),
    topic=os.getenv("LEAD_CREATED_TOPIC", "lead.created")
)

# Initialize event consumer
event_consumer = EventConsumer(
    bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092"),
    topic=os.getenv("FORM_SUBMITTED_TOPIC", "form.submitted"),
    group_id="lead-management-service"
)

# Initialize lead service
lead_service = LeadService()


# Request/Response models
class CreateLeadRequest(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    company: Optional[str] = None
    job_title: Optional[str] = None
    source: Optional[str] = None
    tags: List[str] = []
    custom_fields: Dict[str, Any] = {}
    notes: Optional[str] = None


class UpdateLeadRequest(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    company: Optional[str] = None
    job_title: Optional[str] = None
    source: Optional[str] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None


class LeadResponse(BaseModel):
    lead_id: str
    message: str


# Background task for processing form submissions
async def process_form_submission(submission: FormSubmittedPayload):
    """Background task to process a form submission and create a lead."""
    logger.info(f"Processing form submission {submission.submission_id}")
    
    try:
        # Extract lead data from the submission
        lead_data = {}
        
        # Map form fields to lead fields
        for field_name, field_value in submission.submitted_data.items():
            if "name" in field_name.lower():
                if "first" in field_name.lower():
                    lead_data["first_name"] = field_value
                elif "last" in field_name.lower():
                    lead_data["last_name"] = field_value
                else:
                    # If it's just "name", try to split it into first and last
                    if "first_name" not in lead_data and "last_name" not in lead_data:
                        name_parts = field_value.split(" ", 1)
                        if len(name_parts) > 1:
                            lead_data["first_name"] = name_parts[0]
                            lead_data["last_name"] = name_parts[1]
                        else:
                            lead_data["first_name"] = field_value
            
            elif "email" in field_name.lower():
                lead_data["email"] = field_value
            
            elif "phone" in field_name.lower() or "tel" in field_name.lower():
                lead_data["phone"] = field_value
            
            elif "company" in field_name.lower() or "organization" in field_name.lower():
                lead_data["company"] = field_value
            
            elif "job" in field_name.lower() or "title" in field_name.lower() or "position" in field_name.lower():
                lead_data["job_title"] = field_value
            
            elif "message" in field_name.lower() or "comment" in field_name.lower():
                lead_data["notes"] = field_value
        
        # Set source
        lead_data["source"] = f"form_submission:{submission.url}"
        
        # Add tags
        lead_data["tags"] = ["form_submission", f"form_id:{submission.form_id}"]
        
        # Add custom fields
        lead_data["custom_fields"] = {
            "submission_id": submission.submission_id,
            "form_id": submission.form_id,
            "url": str(submission.url)
        }
        
        # Create the lead
        lead_id = await lead_service.create_lead(lead_data)
        
        logger.info(f"Created lead {lead_id} from form submission {submission.submission_id}")
        
        # Publish lead created event
        event = Event(
            event_id=str(uuid.uuid4()),
            event_type=EventType.LEAD_CREATED,
            producer="lead-management-service",
            payload=LeadCreatedPayload(
                lead_id=lead_id,
                source=lead_data["source"],
                lead_data=lead_data,
                metadata={
                    "submission_id": submission.submission_id,
                    "form_id": submission.form_id
                }
            ).dict()
        )
        
        await event_producer.produce_event(event)
        
        logger.info(f"Published lead created event for lead {lead_id}")
        
    except Exception as e:
        logger.error(f"Error processing form submission: {str(e)}")


# API endpoints
@app.post("/api/v1/leads", response_model=LeadResponse)
async def create_lead(
    request: CreateLeadRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new lead.
    
    This endpoint creates a new lead in the system.
    """
    logger.info(f"Creating lead", props={"user": current_user.username})
    
    try:
        # Create the lead
        lead_id = await lead_service.create_lead(request.dict())
        
        # Publish lead created event
        event = Event(
            event_id=str(uuid.uuid4()),
            event_type=EventType.LEAD_CREATED,
            producer="lead-management-service",
            payload=LeadCreatedPayload(
                lead_id=lead_id,
                source=request.source or "manual",
                lead_data=request.dict(),
                metadata={
                    "created_by": current_user.username
                }
            ).dict()
        )
        
        await event_producer.produce_event(event)
        
        return LeadResponse(
            lead_id=lead_id,
            message="Lead created successfully"
        )
        
    except Exception as e:
        logger.error(f"Error creating lead: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating lead: {str(e)}"
        )


@app.get("/api/v1/leads", response_model=List[Lead])
async def get_leads(
    current_user: User = Depends(get_current_active_user),
    skip: int = 0,
    limit: int = 100,
    source: Optional[str] = None,
    tag: Optional[str] = None
):
    """
    Get a list of leads.
    
    This endpoint returns a list of leads with optional filtering.
    """
    logger.info(f"Getting leads", props={
        "user": current_user.username,
        "skip": skip,
        "limit": limit,
        "source": source,
        "tag": tag
    })
    
    try:
        # Get leads
        leads = await lead_service.get_leads(
            skip=skip,
            limit=limit,
            source=source,
            tag=tag
        )
        
        return leads
        
    except Exception as e:
        logger.error(f"Error getting leads: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting leads: {str(e)}"
        )


@app.get("/api/v1/leads/{lead_id}", response_model=Lead)
async def get_lead(
    lead_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a lead by ID.
    
    This endpoint returns a lead by its ID.
    """
    logger.info(f"Getting lead {lead_id}", props={"user": current_user.username})
    
    try:
        # Get lead
        lead = await lead_service.get_lead(lead_id)
        
        if not lead:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Lead {lead_id} not found"
            )
        
        return lead
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting lead: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting lead: {str(e)}"
        )


@app.put("/api/v1/leads/{lead_id}", response_model=LeadResponse)
async def update_lead(
    lead_id: str,
    request: UpdateLeadRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Update a lead.
    
    This endpoint updates a lead by its ID.
    """
    logger.info(f"Updating lead {lead_id}", props={"user": current_user.username})
    
    try:
        # Update lead
        updated = await lead_service.update_lead(lead_id, request.dict(exclude_unset=True))
        
        if not updated:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Lead {lead_id} not found"
            )
        
        # Publish lead updated event
        event = Event(
            event_id=str(uuid.uuid4()),
            event_type=EventType.LEAD_UPDATED,
            producer="lead-management-service",
            payload=LeadUpdatedPayload(
                lead_id=lead_id,
                updated_fields=request.dict(exclude_unset=True),
                metadata={
                    "updated_by": current_user.username
                }
            ).dict()
        )
        
        await event_producer.produce_event(event)
        
        return LeadResponse(
            lead_id=lead_id,
            message="Lead updated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating lead: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating lead: {str(e)}"
        )


@app.delete("/api/v1/leads/{lead_id}", response_model=LeadResponse)
async def delete_lead(
    lead_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a lead.
    
    This endpoint deletes a lead by its ID.
    """
    logger.info(f"Deleting lead {lead_id}", props={"user": current_user.username})
    
    try:
        # Delete lead
        deleted = await lead_service.delete_lead(lead_id)
        
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Lead {lead_id} not found"
            )
        
        return LeadResponse(
            lead_id=lead_id,
            message="Lead deleted successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting lead: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting lead: {str(e)}"
        )


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}


# Start the event consumer when the app starts
@app.on_event("startup")
async def startup_event():
    """Start the event consumer when the app starts."""
    await event_consumer.start(process_form_submission)


# Stop the event consumer when the app stops
@app.on_event("shutdown")
async def shutdown_event():
    """Stop the event consumer when the app stops."""
    await event_consumer.stop()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
