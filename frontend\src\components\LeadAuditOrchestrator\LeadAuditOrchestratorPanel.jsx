import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  Button,
  Grid,
  TextField,
  FormControlLabel,
  Switch,
  Slider,
  Alert,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  Schedule as ScheduleIcon,
  Assessment as ReportIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';
import supabaseService from '../../services/supabaseService';

/**
 * Lead Audit Orchestrator Panel
 * 
 * This component provides a complete interface for managing automated lead audits:
 * - Start automated lead processing from Supabase
 * - Monitor progress of audit batches
 * - View scheduler status and queue
 * - Manage 48-hour monitoring periods
 * - Generate and view audit reports
 */
const LeadAuditOrchestratorPanel = () => {
  // State management
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeBatches, setActiveBatches] = useState([]);
  const [schedulerStatus, setSchedulerStatus] = useState(null);
  const [selectedBatch, setSelectedBatch] = useState(null);
  const [batchDetails, setBatchDetails] = useState(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  
  // Configuration state
  const [config, setConfig] = useState({
    maxLeads: 50,
    priority: 5,
    skipEnrichment: false,
    monitoringHours: 48,
    staggerMinutes: 2
  });
  
  // Error and success states
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // API base URL for orchestrator service
  const ORCHESTRATOR_API = import.meta.env.VITE_ORCHESTRATOR_API_URL || 'http://localhost:8008';

  useEffect(() => {
    // Load initial data
    loadSchedulerStatus();
    loadActiveBatches();
    
    // Set up polling for real-time updates
    const interval = setInterval(() => {
      loadSchedulerStatus();
      loadActiveBatches();
    }, 10000); // Update every 10 seconds
    
    return () => clearInterval(interval);
  }, []);

  const loadSchedulerStatus = async () => {
    try {
      const response = await fetch(`${ORCHESTRATOR_API}/api/v1/scheduler/status`);
      if (response.ok) {
        const status = await response.json();
        setSchedulerStatus(status);
      }
    } catch (err) {
      console.error('Error loading scheduler status:', err);
    }
  };

  const loadActiveBatches = async () => {
    try {
      // TODO: Implement API call to get active batches
      // For now, use mock data
      setActiveBatches([
        {
          batch_id: 'batch_001',
          status: 'monitoring',
          leads_total: 25,
          leads_completed: 15,
          leads_failed: 2,
          leads_disqualified: 3,
          created_at: new Date().toISOString(),
          current_phase: 'monitoring_responses'
        }
      ]);
    } catch (err) {
      console.error('Error loading active batches:', err);
    }
  };

  const startAutomatedAudit = async () => {
    try {
      setIsProcessing(true);
      setError(null);
      setSuccess(null);

      // Call the auto-process endpoint
      const response = await fetch(`${ORCHESTRATOR_API}/api/v1/audit/auto-process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          max_leads: config.maxLeads,
          priority: config.priority,
          skip_enrichment: config.skipEnrichment,
          monitoring_hours: config.monitoringHours
        })
      });

      if (response.ok) {
        const result = await response.json();
        setSuccess(`Started automated audit batch: ${result.batch_id}`);
        loadActiveBatches();
      } else {
        const errorData = await response.json();
        setError(`Failed to start audit: ${errorData.detail}`);
      }
    } catch (err) {
      setError(`Error starting automated audit: ${err.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const viewBatchDetails = async (batchId) => {
    try {
      const response = await fetch(`${ORCHESTRATOR_API}/api/v1/audit/leads/${batchId}`);
      if (response.ok) {
        const details = await response.json();
        setBatchDetails(details);
        setSelectedBatch(batchId);
        setDetailsDialogOpen(true);
      }
    } catch (err) {
      setError(`Error loading batch details: ${err.message}`);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'monitoring': return 'info';
      case 'processing': return 'warning';
      case 'disqualified': return 'secondary';
      default: return 'default';
    }
  };

  const getPhaseDescription = (phase) => {
    switch (phase) {
      case 'enrichment_and_submission': return 'Enriching leads and submitting forms';
      case 'monitoring_responses': return 'Monitoring for responses (48-hour period)';
      case 'generating_reports': return 'Generating audit reports';
      case 'completed': return 'All audits completed';
      default: return phase;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
        🤖 Automated Lead Audit System
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 4, color: 'text.secondary' }}>
        Automatically process leads through the complete audit workflow: enrichment → form submission → 
        48-hour monitoring → report generation. Businesses that respond within 10 minutes are automatically disqualified.
      </Typography>

      {/* Error/Success Messages */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Configuration Panel */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <ScheduleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Audit Configuration
              </Typography>
              
              <Box sx={{ mt: 2 }}>
                <TextField
                  fullWidth
                  label="Max Leads to Process"
                  type="number"
                  value={config.maxLeads}
                  onChange={(e) => setConfig({...config, maxLeads: parseInt(e.target.value)})}
                  sx={{ mb: 2 }}
                  inputProps={{ min: 1, max: 1000 }}
                />
                
                <Typography gutterBottom>Priority Level: {config.priority}</Typography>
                <Slider
                  value={config.priority}
                  onChange={(e, value) => setConfig({...config, priority: value})}
                  min={1}
                  max={10}
                  marks
                  sx={{ mb: 2 }}
                />
                
                <Typography gutterBottom>Monitoring Hours: {config.monitoringHours}</Typography>
                <Slider
                  value={config.monitoringHours}
                  onChange={(e, value) => setConfig({...config, monitoringHours: value})}
                  min={24}
                  max={72}
                  marks={[
                    { value: 24, label: '24h' },
                    { value: 48, label: '48h' },
                    { value: 72, label: '72h' }
                  ]}
                  sx={{ mb: 2 }}
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={config.skipEnrichment}
                      onChange={(e) => setConfig({...config, skipEnrichment: e.target.checked})}
                    />
                  }
                  label="Skip Lead Enrichment"
                  sx={{ mb: 2 }}
                />
                
                <Button
                  fullWidth
                  variant="contained"
                  color="primary"
                  size="large"
                  startIcon={<PlayIcon />}
                  onClick={startAutomatedAudit}
                  disabled={isProcessing}
                  sx={{ mt: 2 }}
                >
                  {isProcessing ? 'Starting...' : 'Start Automated Audit'}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Scheduler Status */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <SpeedIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Scheduler Status
              </Typography>
              
              {schedulerStatus ? (
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Status</Typography>
                      <Chip 
                        label={schedulerStatus.is_running ? 'Running' : 'Stopped'} 
                        color={schedulerStatus.is_running ? 'success' : 'error'}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Pending Tasks</Typography>
                      <Typography variant="h6">{schedulerStatus.pending_tasks}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Running Tasks</Typography>
                      <Typography variant="h6">{schedulerStatus.running_tasks}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Rate Limit</Typography>
                      <Typography variant="body2">
                        {schedulerStatus.rate_limit_usage}/{schedulerStatus.rate_limit_max}/min
                      </Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={(schedulerStatus.rate_limit_usage / schedulerStatus.rate_limit_max) * 100}
                        sx={{ mt: 1 }}
                      />
                    </Grid>
                  </Grid>
                </Box>
              ) : (
                <Typography>Loading scheduler status...</Typography>
              )}
              
              <Button
                fullWidth
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadSchedulerStatus}
                sx={{ mt: 2 }}
              >
                Refresh Status
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <ReportIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Quick Stats
              </Typography>
              
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Active Batches</Typography>
                    <Typography variant="h6">{activeBatches.length}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Total Leads</Typography>
                    <Typography variant="h6">
                      {activeBatches.reduce((sum, batch) => sum + batch.leads_total, 0)}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Completed</Typography>
                    <Typography variant="h6" color="success.main">
                      {activeBatches.reduce((sum, batch) => sum + batch.leads_completed, 0)}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Disqualified</Typography>
                    <Typography variant="h6" color="warning.main">
                      {activeBatches.reduce((sum, batch) => sum + batch.leads_disqualified, 0)}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Active Batches Table */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Active Audit Batches
              </Typography>
              
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Batch ID</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Progress</TableCell>
                      <TableCell>Current Phase</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {activeBatches.map((batch) => (
                      <TableRow key={batch.batch_id}>
                        <TableCell>
                          <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                            {batch.batch_id.substring(0, 8)}...
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={batch.status} 
                            color={getStatusColor(batch.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ width: 200 }}>
                            <Typography variant="body2" sx={{ mb: 1 }}>
                              {batch.leads_completed}/{batch.leads_total} leads
                            </Typography>
                            <LinearProgress 
                              variant="determinate" 
                              value={(batch.leads_completed / batch.leads_total) * 100}
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {getPhaseDescription(batch.current_phase)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(batch.created_at).toLocaleString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Tooltip title="View Details">
                            <IconButton 
                              size="small" 
                              onClick={() => viewBatchDetails(batch.batch_id)}
                            >
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                    {activeBatches.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={6} align="center">
                          <Typography color="text.secondary">
                            No active audit batches. Start an automated audit to begin.
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Batch Details Dialog */}
      <Dialog 
        open={detailsDialogOpen} 
        onClose={() => setDetailsDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Batch Details: {selectedBatch}
        </DialogTitle>
        <DialogContent>
          {batchDetails && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Lead Status Breakdown
              </Typography>
              {/* TODO: Add detailed lead status table */}
              <Typography>
                {batchDetails.leads?.length || 0} leads in this batch
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default LeadAuditOrchestratorPanel;
