/**
 * Lead Enrichment Service
 *
 * This service provides functions to enrich lead data with additional information:
 * - Find company website if not provided
 * - Scrape contact information from website
 * - Detect Facebook Pixel on website
 */

import axios from 'axios';

// Configuration
const SERPER_API_KEY = import.meta.env.VITE_SERPER_API_KEY || '';
const SERPER_SEARCH_URL = 'https://serpapi.com/search.json';

/**
 * Find a company's website using Serper API
 *
 * @param {string} company - Company name
 * @param {string} location - Company location
 * @returns {Promise<string>} - Company website URL or empty string
 */
const findCompanyWebsite = async (company, location) => {
  if (!company || !location || !SERPER_API_KEY) {
    console.warn('Missing required data for website search:', { company, location, hasApiKey: !!SERPER_API_KEY });
    return '';
  }

  try {
    const params = {
      engine: 'google',
      q: `${company} ${location} official website`,
      api_key: SERPER_API_KEY,
      num: 3
    };

    const response = await axios.get(SERPER_SEARCH_URL, { params });
    const data = response.data;

    // Try to extract the first organic result with a plausible URL
    for (const result of data.organic_results || []) {
      const link = result.link || '';
      if (link && !link.startsWith('https://www.indeed.com')) {
        return link;
      }
    }

    return '';
  } catch (error) {
    console.error(`Error finding website for ${company}:`, error);
    return '';
  }
};

/**
 * Extract all valid phone numbers from text
 *
 * @param {string} text - Text to extract phone numbers from
 * @returns {string[]} - Array of phone numbers
 */
const extractPhoneNumbers = (text) => {
  if (!text) return [];

  // Phone pattern for US numbers
  const phonePattern = /(?:\+1[\s.-]?)?(\(?[2-9][0-9]{2}\)?[\s.-]?[0-9]{3}[\s.-]?[0-9]{4})/g;
  const matches = text.match(phonePattern) || [];

  // Clean and normalize phone numbers
  return matches.map(phone => {
    // Remove non-numeric characters except + for international
    const digits = phone.replace(/[^\d+]/g, '');

    // Format as (XXX) XXX-XXXX if 10 digits
    if (digits.length === 10) {
      return `(${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6)}`;
    }

    // Format as +1 (XXX) XXX-XXXX if 11 digits and starts with 1
    if (digits.length === 11 && digits[0] === '1') {
      return `+1 (${digits.substring(1, 4)}) ${digits.substring(4, 7)}-${digits.substring(7)}`;
    }

    return phone;
  }).filter(phone => phone.length >= 10); // Filter out too short numbers
};

/**
 * Extract email addresses from text
 *
 * @param {string} text - Text to extract emails from
 * @returns {string[]} - Array of email addresses
 */
const extractEmails = (text) => {
  if (!text) return [];

  const emailPattern = /[\w\.-]+@[\w\.-]+\.\w+/g;
  return text.match(emailPattern) || [];
};

/**
 * Filter out fake or example email addresses
 *
 * @param {string[]} emails - Array of email addresses
 * @param {string} websiteUrl - Website URL to match domain (optional)
 * @returns {string} - Best email address or empty string
 */
const filterEmails = (emails, websiteUrl = '') => {
  if (!emails || emails.length === 0) return '';

  // Patterns for fake/example emails
  const badPatterns = [
    /example@/i,
    /test@/i,
    /your@email\.com/i,
    /email@example\.com/i,
    /info@example\.com/i,
    /name@email\.com/i,
    /demo@/i,
    /sample@/i,
    /noreply@/i,
    /no-reply@/i,
    /fake@/i,
    /abc@/i,
    /xyz@/i,
    /something@/i,
    /someone@/i
  ];

  // Filter out fake emails
  const validEmails = emails.filter(email => {
    return !badPatterns.some(pattern => pattern.test(email));
  });

  if (validEmails.length === 0) return '';

  // If we have a website URL, try to find an email with matching domain
  if (websiteUrl) {
    try {
      const domain = websiteUrl.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
      const domainEmails = validEmails.filter(email => email.includes(domain));
      if (domainEmails.length > 0) {
        return domainEmails[0];
      }
    } catch (error) {
      console.error('Error matching email domain:', error);
    }
  }

  return validEmails[0];
};

/**
 * Detect if Facebook Pixel is present in HTML
 *
 * @param {string} html - HTML content to check
 * @returns {boolean} - True if Facebook Pixel is detected
 */
const detectFacebookPixel = (html) => {
  if (!html) return false;

  return (
    html.includes('connect.facebook.net/en_US/fbevents.js') ||
    html.includes("fbq('init'") ||
    html.includes('facebook.com/tr?id=')
  );
};

/**
 * Extract social media links from HTML
 *
 * @param {string} html - HTML content to extract links from
 * @param {string} baseUrl - Base URL for resolving relative links
 * @returns {Object} - Object containing social media links
 */
const extractSocialMediaLinks = (html, baseUrl) => {
  if (!html) return {
    facebook: '',
    linkedin: '',
    instagram: '',
    youtube: '',
    tiktok: '',
    twitter: ''
  };

  try {
    // Parse HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // Initialize social media links
    const socialMedia = {
      facebook: '',
      linkedin: '',
      instagram: '',
      youtube: '',
      tiktok: '',
      twitter: ''
    };

    // Get all links
    const links = doc.querySelectorAll('a');

    // Check each link for social media
    links.forEach(link => {
      const href = link.getAttribute('href');
      if (!href) return;

      // Resolve relative URLs
      const fullUrl = href.startsWith('http') ? href : new URL(href, baseUrl).href;

      // Check for social media platforms
      if (fullUrl.includes('facebook.com') || fullUrl.includes('fb.com')) {
        socialMedia.facebook = fullUrl;
      } else if (fullUrl.includes('linkedin.com')) {
        socialMedia.linkedin = fullUrl;
      } else if (fullUrl.includes('instagram.com')) {
        socialMedia.instagram = fullUrl;
      } else if (fullUrl.includes('youtube.com') || fullUrl.includes('youtu.be')) {
        socialMedia.youtube = fullUrl;
      } else if (fullUrl.includes('tiktok.com')) {
        socialMedia.tiktok = fullUrl;
      } else if (fullUrl.includes('twitter.com') || fullUrl.includes('x.com')) {
        socialMedia.twitter = fullUrl;
      }
    });

    return socialMedia;
  } catch (error) {
    console.error('Error extracting social media links:', error);
    return {
      facebook: '',
      linkedin: '',
      instagram: '',
      youtube: '',
      tiktok: '',
      twitter: ''
    };
  }
};

/**
 * Scrape contact details from a website
 *
 * @param {string} url - Website URL to scrape
 * @param {Object} options - Scraping options
 * @param {boolean} options.deepScan - Whether to perform a deep scan
 * @param {number} options.timeout - Timeout in milliseconds
 * @returns {Promise<Object>} - Contact details, social media links, and Facebook Pixel status
 */
const scrapeContactDetails = async (url, options = {}) => {
  if (!url) return {
    email: '',
    phone: '',
    facebookPixel: false,
    facebook: '',
    linkedin: '',
    instagram: '',
    youtube: '',
    tiktok: '',
    twitter: ''
  };

  const { deepScan = false, timeout = 15000 } = options;

  try {
    // Add protocol if missing
    const fullUrl = url.startsWith('http') ? url : `https://${url}`;

    console.log(`Scraping website: ${fullUrl} with ${deepScan ? 'deep scan' : 'standard scan'}`);

    // Fetch the website
    const response = await axios.get(fullUrl, {
      timeout: timeout,
      headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36' }
    });

    const html = response.data;

    // If deep scan is enabled, also fetch the contact and about pages
    let contactHtml = '';
    let aboutHtml = '';

    if (deepScan) {
      try {
        // Try to fetch contact page
        const contactUrls = [
          `${fullUrl}/contact`,
          `${fullUrl}/contact-us`,
          `${fullUrl}/contactus`,
          `${fullUrl}/get-in-touch`,
          `${fullUrl}/reach-us`
        ];

        for (const contactUrl of contactUrls) {
          try {
            console.log(`Deep scan: trying contact page at ${contactUrl}`);
            const contactResponse = await axios.get(contactUrl, {
              timeout: timeout / 2,
              headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36' }
            });
            contactHtml = contactResponse.data;
            console.log('Found contact page!');
            break;
          } catch (err) {
            // Continue to next URL
          }
        }

        // Try to fetch about page
        const aboutUrls = [
          `${fullUrl}/about`,
          `${fullUrl}/about-us`,
          `${fullUrl}/aboutus`,
          `${fullUrl}/company`,
          `${fullUrl}/who-we-are`
        ];

        for (const aboutUrl of aboutUrls) {
          try {
            console.log(`Deep scan: trying about page at ${aboutUrl}`);
            const aboutResponse = await axios.get(aboutUrl, {
              timeout: timeout / 2,
              headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36' }
            });
            aboutHtml = aboutResponse.data;
            console.log('Found about page!');
            break;
          } catch (err) {
            // Continue to next URL
          }
        }
      } catch (err) {
        console.error('Error during deep scan:', err);
      }
    }

    // Combine all HTML for processing
    const combinedHtml = html + contactHtml + aboutHtml;

    // Parse HTML for main page and combined content
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const combinedDoc = parser.parseFromString(combinedHtml, 'text/html');

    // Extract visible text from combined HTML
    const visibleText = combinedDoc.body.textContent || '';

    // Extract emails from combined HTML
    const emails = extractEmails(visibleText);
    const email = filterEmails(emails, url);

    // Extract phone numbers from combined HTML
    const phones = extractPhoneNumbers(visibleText);
    const phone = phones.length > 0 ? phones[0] : '';

    // Check for Facebook Pixel (only in main page)
    const hasFacebookPixel = detectFacebookPixel(html);

    // Extract social media links from combined HTML
    const socialMedia = extractSocialMediaLinks(combinedHtml, fullUrl);

    // Try to extract company name if deep scan is enabled
    let companyName = '';
    if (deepScan) {
      try {
        // Look for company name in title
        const title = doc.title || '';
        if (title) {
          // Remove common suffixes
          companyName = title
            .replace(/\s*[-|]\s*.+$/, '') // Remove everything after a dash or pipe
            .replace(/\s*[.]\s*.+$/, '') // Remove everything after a period
            .replace(/(Home|Homepage|Welcome to|Official Website|Contact Us|About Us)$/i, '')
            .trim();
        }

        // If no company name from title, try meta tags
        if (!companyName) {
          const metaOgSite = doc.querySelector('meta[property="og:site_name"]');
          if (metaOgSite && metaOgSite.getAttribute('content')) {
            companyName = metaOgSite.getAttribute('content').trim();
          }
        }

        // If still no company name, try the domain name
        if (!companyName) {
          const domain = url.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
          const domainParts = domain.split('.');
          if (domainParts.length > 1) {
            companyName = domainParts[0]
              .replace(/[-_]/g, ' ')
              .replace(/\b\w/g, l => l.toUpperCase());
          }
        }
      } catch (err) {
        console.error('Error extracting company name:', err);
      }
    }

    return {
      email,
      phone,
      company: companyName,
      facebookPixel: hasFacebookPixel,
      ...socialMedia
    };
  } catch (error) {
    console.error(`Error scraping ${url}:`, error);
    return {
      email: '',
      phone: '',
      facebookPixel: false,
      facebook: '',
      linkedin: '',
      instagram: '',
      youtube: '',
      tiktok: '',
      twitter: ''
    };
  }
};

/**
 * Enrich a lead with additional data
 *
 * @param {Object} lead - Lead data to enrich
 * @param {Object} options - Enrichment options
 * @param {boolean} options.deepScan - Whether to perform a deep scan
 * @param {number} options.timeout - Timeout in milliseconds
 * @returns {Promise<Object>} - Enriched lead data
 */
const enrichLead = async (lead, options = {}) => {
  try {
    const enrichedLead = { ...lead };
    const { deepScan = false, timeout = 15000 } = options;

    // Step 1: Find website if not provided
    if (!lead.website) {
      const company = lead.company || lead.name || '';
      const location = lead.city || lead.state || lead.country || '';

      if (company && location) {
        console.log(`Finding website for ${company} in ${location}`);
        enrichedLead.website = await findCompanyWebsite(company, location);
      }
    }

    // Step 2: Scrape website for contact info, social media, and Facebook Pixel if we have a website
    if (enrichedLead.website) {
      console.log(`Scraping website: ${enrichedLead.website} with ${deepScan ? 'deep scan' : 'standard scan'}`);
      const scrapedData = await scrapeContactDetails(enrichedLead.website, {
        deepScan,
        timeout
      });

      // Update Facebook Pixel status
      enrichedLead.facebook_pixel = scrapedData.facebookPixel;
      console.log(`Facebook Pixel detected for ${enrichedLead.company || enrichedLead.name || 'lead'}: ${scrapedData.facebookPixel}`);

      // Only update contact info if fields are empty
      if (!lead.email) enrichedLead.email = scrapedData.email;
      if (!lead.phone) enrichedLead.phone = scrapedData.phone;

      // Update company name if it was found and not already set
      if (scrapedData.company && !lead.company) {
        enrichedLead.company = scrapedData.company;
        console.log(`Company name detected: ${scrapedData.company}`);
      }

      // Update social media links if they're empty
      // Make sure to use empty strings instead of null values
      if (!lead.facebook) enrichedLead.facebook = scrapedData.facebook || '';
      if (!lead.linkedin) enrichedLead.linkedin = scrapedData.linkedin || '';
      if (!lead.instagram) enrichedLead.instagram = scrapedData.instagram || '';
      if (!lead.youtube) enrichedLead.youtube = scrapedData.youtube || '';
      if (!lead.tiktok) enrichedLead.tiktok = scrapedData.tiktok || '';
      if (!lead.twitter) enrichedLead.twitter = scrapedData.twitter || '';

      // Log social media links for debugging
      console.log('Social media links for', enrichedLead.company || enrichedLead.name || 'lead', {
        facebook: enrichedLead.facebook,
        linkedin: enrichedLead.linkedin,
        instagram: enrichedLead.instagram,
        youtube: enrichedLead.youtube,
        tiktok: enrichedLead.tiktok,
        twitter: enrichedLead.twitter
      });
    }

    return enrichedLead;
  } catch (error) {
    console.error('Error enriching lead:', error);
    return lead; // Return original lead if enrichment fails
  }
};

/**
 * Enrich multiple leads in parallel
 *
 * @param {Array<Object>} leads - Array of leads to enrich
 * @param {Function} progressCallback - Callback for progress updates
 * @param {number} concurrency - Number of concurrent enrichment operations
 * @returns {Promise<Array<Object>>} - Array of enriched leads
 */
const enrichLeads = async (leads, progressCallback = null, concurrency = 3) => {
  if (!leads || leads.length === 0) return [];

  const enrichedLeads = [];
  let completed = 0;

  // Process leads in batches to control concurrency
  for (let i = 0; i < leads.length; i += concurrency) {
    const batch = leads.slice(i, i + concurrency);
    const batchPromises = batch.map(lead => enrichLead(lead));

    const batchResults = await Promise.all(batchPromises);
    enrichedLeads.push(...batchResults);

    completed += batch.length;
    if (progressCallback) {
      progressCallback(completed, leads.length);
    }
  }

  return enrichedLeads;
};

const leadEnrichmentService = {
  enrichLead,
  enrichLeads,
  findCompanyWebsite,
  scrapeContactDetails,
  detectFacebookPixel
};

export default leadEnrichmentService;
