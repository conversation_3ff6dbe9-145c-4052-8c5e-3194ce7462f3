-- Create tables for audit reports and outreach messages

-- Create audit_reports table to store generated audit reports
CREATE TABLE IF NOT EXISTS audit_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Lead reference
    lead_id UUID REFERENCES leads(id) ON DELETE CASCADE,
    
    -- Audit metadata
    audit_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    audit_status TEXT DEFAULT 'completed', -- pending, completed, failed
    
    -- Response performance data
    responded BOOLEAN DEFAULT FALSE,
    response_time_seconds INTEGER,
    response_time_human TEXT,
    response_channel TEXT, -- phone, sms, email, none
    
    -- Scoring
    time_score INTEGER DEFAULT 0,
    channel_bonus INTEGER DEFAULT 0,
    multi_channel_bonus INTEGER DEFAULT 0,
    total_score INTEGER DEFAULT 0,
    grade TEXT, -- A+, A, B, C, D, F
    
    -- Facebook Pixel data
    facebook_pixel_detected BOOLEAN DEFAULT FALSE,
    facebook_pixel_id TEXT,
    facebook_pixel_events TEXT[], -- Array of tracked events
    facebook_pixel_quality TEXT, -- basic, advanced, none
    
    -- Form submission data
    form_url TEXT,
    form_submitted_at TIMESTAMP WITH TIME ZONE,
    form_fields_submitted JSONB,
    
    -- Response details
    response_content TEXT,
    response_sender_name TEXT,
    response_sender_contact TEXT,
    
    -- Audit insights
    conversion_rate_current INTEGER, -- Percentage
    conversion_rate_potential INTEGER DEFAULT 85, -- Percentage
    improvement_opportunity_points INTEGER,
    
    -- Raw audit data
    audit_data JSONB, -- Complete audit results
    
    -- Report generation
    report_generated BOOLEAN DEFAULT FALSE,
    report_url TEXT,
    report_pdf_url TEXT
);

-- Create outreach_messages table to store generated cold emails and LinkedIn messages
CREATE TABLE IF NOT EXISTS outreach_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Lead and audit references
    lead_id UUID REFERENCES leads(id) ON DELETE CASCADE,
    audit_report_id UUID REFERENCES audit_reports(id) ON DELETE CASCADE,
    
    -- Message metadata
    message_type TEXT NOT NULL, -- email, linkedin
    status TEXT DEFAULT 'draft', -- draft, sent, delivered, opened, replied, bounced
    
    -- Message content
    subject_line TEXT,
    message_body TEXT NOT NULL,
    personalization_data JSONB, -- Data used for personalization
    
    -- Email specific fields
    from_email TEXT,
    to_email TEXT,
    email_template_id TEXT,
    
    -- LinkedIn specific fields
    linkedin_profile_url TEXT,
    linkedin_connection_status TEXT, -- not_connected, pending, connected
    linkedin_message_thread_id TEXT,
    
    -- Sending metadata
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    replied_at TIMESTAMP WITH TIME ZONE,
    
    -- Performance tracking
    open_count INTEGER DEFAULT 0,
    click_count INTEGER DEFAULT 0,
    reply_count INTEGER DEFAULT 0,
    
    -- AI generation metadata
    ai_model_used TEXT,
    generation_prompt TEXT,
    generation_tokens_used INTEGER,
    generation_cost DECIMAL(10,4),
    
    -- Quality scores
    personalization_score INTEGER, -- 1-10
    relevance_score INTEGER, -- 1-10
    tone_score INTEGER, -- 1-10
    
    -- Additional data
    notes TEXT,
    tags TEXT[],
    metadata JSONB
);

-- Create lead_states table to track overall lead progression
CREATE TABLE IF NOT EXISTS lead_states (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Lead reference
    lead_id UUID REFERENCES leads(id) ON DELETE CASCADE,
    
    -- Current state
    current_state TEXT NOT NULL DEFAULT 'imported', 
    -- States: imported, enriched, form_submitted, audit_completed, outreach_generated, outreach_sent, responded, qualified, disqualified
    
    previous_state TEXT,
    state_changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Progress tracking
    enrichment_completed BOOLEAN DEFAULT FALSE,
    enrichment_completed_at TIMESTAMP WITH TIME ZONE,
    
    form_submission_completed BOOLEAN DEFAULT FALSE,
    form_submission_completed_at TIMESTAMP WITH TIME ZONE,
    
    audit_completed BOOLEAN DEFAULT FALSE,
    audit_completed_at TIMESTAMP WITH TIME ZONE,
    
    outreach_generated BOOLEAN DEFAULT FALSE,
    outreach_generated_at TIMESTAMP WITH TIME ZONE,
    
    outreach_sent BOOLEAN DEFAULT FALSE,
    outreach_sent_at TIMESTAMP WITH TIME ZONE,
    
    response_received BOOLEAN DEFAULT FALSE,
    response_received_at TIMESTAMP WITH TIME ZONE,
    
    -- Qualification status
    qualified BOOLEAN,
    qualification_reason TEXT,
    qualified_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional tracking
    notes TEXT,
    metadata JSONB
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_audit_reports_lead_id ON audit_reports(lead_id);
CREATE INDEX IF NOT EXISTS idx_audit_reports_audit_date ON audit_reports(audit_date);
CREATE INDEX IF NOT EXISTS idx_audit_reports_grade ON audit_reports(grade);
CREATE INDEX IF NOT EXISTS idx_audit_reports_responded ON audit_reports(responded);

CREATE INDEX IF NOT EXISTS idx_outreach_messages_lead_id ON outreach_messages(lead_id);
CREATE INDEX IF NOT EXISTS idx_outreach_messages_audit_report_id ON outreach_messages(audit_report_id);
CREATE INDEX IF NOT EXISTS idx_outreach_messages_type ON outreach_messages(message_type);
CREATE INDEX IF NOT EXISTS idx_outreach_messages_status ON outreach_messages(status);
CREATE INDEX IF NOT EXISTS idx_outreach_messages_sent_at ON outreach_messages(sent_at);

CREATE INDEX IF NOT EXISTS idx_lead_states_lead_id ON lead_states(lead_id);
CREATE INDEX IF NOT EXISTS idx_lead_states_current_state ON lead_states(current_state);
CREATE INDEX IF NOT EXISTS idx_lead_states_state_changed_at ON lead_states(state_changed_at);

-- Create triggers to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_audit_reports_updated_at BEFORE UPDATE ON audit_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_outreach_messages_updated_at BEFORE UPDATE ON outreach_messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lead_states_updated_at BEFORE UPDATE ON lead_states FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
