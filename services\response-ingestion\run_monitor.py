"""
Response Monitor CLI

This script provides a command-line interface for running the response monitoring system.
"""
import os
import sys
import time
import argparse
import json
from datetime import datetime, timedelta

# Add parent directory to path to allow importing from sibling modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import response monitoring modules
from response_monitor import ResponseMonitor
from analytics.response_audit import ResponseAudit

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Response Monitoring System")
    
    # Add subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Monitor command
    monitor_parser = subparsers.add_parser("monitor", help="Start response monitoring")
    monitor_parser.add_argument("--email", action="store_true", help="Enable email monitoring")
    monitor_parser.add_argument("--sms", action="store_true", help="Enable SMS monitoring")
    monitor_parser.add_argument("--interval", type=int, default=60, help="Polling interval in seconds")
    monitor_parser.add_argument("--log-dir", type=str, default="./logs", help="Log directory")
    
    # Report command
    report_parser = subparsers.add_parser("report", help="Generate response audit report")
    report_parser.add_argument("--days", type=int, default=30, help="Number of days to include in the report")
    report_parser.add_argument("--output-dir", type=str, default="./reports", help="Output directory")
    report_parser.add_argument("--format", type=str, choices=["csv", "json"], default="csv", help="Output format")
    
    # Status command
    status_parser = subparsers.add_parser("status", help="Check monitoring status")
    
    return parser.parse_args()

def run_monitor(args):
    """Run the response monitor."""
    print("[INFO] Starting response monitoring...")
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("[WARN] python-dotenv not installed; skipping .env loading.")
    
    # Get email credentials from environment
    email_config = None
    if args.email:
        email_config = {
            "username": os.environ.get("EMAIL_USERNAME"),
            "password": os.environ.get("EMAIL_APP_PASSWORD"),
            "imap_server": os.environ.get("EMAIL_IMAP_SERVER", "imap.gmail.com")
        }
        
        if not email_config["username"] or not email_config["password"]:
            print("[ERROR] Email monitoring requires EMAIL_USERNAME and EMAIL_APP_PASSWORD environment variables")
            return 1
    
    # Get SMS credentials from environment
    sms_config = None
    if args.sms:
        sms_config = {
            "api_key": os.environ.get("TELNYX_API_KEY")
        }
        
        if not sms_config["api_key"]:
            print("[ERROR] SMS monitoring requires TELNYX_API_KEY environment variable")
            return 1
    
    # Initialize response monitor
    monitor = ResponseMonitor(
        email_config=email_config,
        sms_config=sms_config,
        openai_api_key=os.environ.get("OPENAI_API_KEY"),
        enable_audit_logging=True,
        log_dir=args.log_dir
    )
    
    # Start monitoring
    if args.email:
        print(f"[INFO] Starting email monitoring with interval {args.interval}s")
        monitor.start_email_monitoring(interval=args.interval)
    
    if args.sms:
        print("[INFO] SMS monitoring is handled by the webhook server")
    
    if not args.email and not args.sms:
        print("[ERROR] No monitoring channels enabled. Use --email or --sms")
        return 1
    
    try:
        # Keep the main thread running
        print("[INFO] Monitoring active. Press Ctrl+C to stop.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("[INFO] Stopping response monitoring...")
        monitor.stop_monitoring()
    
    return 0

def generate_report(args):
    """Generate a response audit report."""
    print("[INFO] Generating response audit report...")
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("[WARN] python-dotenv not installed; skipping .env loading.")
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=args.days)
    
    # Initialize response audit
    audit = ResponseAudit(output_dir=args.output_dir)
    
    # Generate report
    report = audit.generate_response_time_report(start_date, end_date)
    
    # Print summary
    print("\nResponse Time Summary:")
    print(f"Total Leads: {report['summary']['total_leads']}")
    print(f"Responded Leads: {report['summary']['responded_leads']}")
    print(f"Response Rate: {report['summary']['response_rate']:.2%}")
    
    print("\nChannel Statistics:")
    for channel, stats in report['summary']['channels'].items():
        print(f"\n{channel}:")
        print(f"  Count: {stats['count']}")
        print(f"  Min Time: {stats['min_human']}")
        print(f"  Max Time: {stats['max_human']}")
        print(f"  Avg Time: {stats['avg_human']}")
        print(f"  Median Time: {stats['median_human']}")
    
    print(f"\n[INFO] Report saved to {report['report_file']}")
    
    return 0

def check_status(args):
    """Check the status of the response monitoring system."""
    print("[INFO] Checking response monitoring status...")
    
    # This is a simple implementation that just checks if the monitor process is running
    # In a real implementation, this would check the status of the monitoring threads
    
    # Check for running monitor processes
    try:
        import psutil
        monitor_processes = [p for p in psutil.process_iter() if "run_monitor.py" in " ".join(p.cmdline())]
        
        if monitor_processes:
            print("[INFO] Response monitoring is active")
            for p in monitor_processes:
                print(f"  PID: {p.pid}, Started: {datetime.fromtimestamp(p.create_time()).strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("[INFO] Response monitoring is not active")
    except ImportError:
        print("[WARN] psutil not installed; cannot check process status")
        print("[INFO] To check if monitoring is active, run 'ps aux | grep run_monitor.py'")
    
    return 0

def main():
    """Main entry point."""
    args = parse_args()
    
    if args.command == "monitor":
        return run_monitor(args)
    elif args.command == "report":
        return generate_report(args)
    elif args.command == "status":
        return check_status(args)
    else:
        print("[ERROR] No command specified. Use 'monitor', 'report', or 'status'")
        return 1

if __name__ == "__main__":
    sys.exit(main())
