{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@supabase/supabase-js": "^2.49.8", "axios": "^1.9.0", "papaparse": "^5.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.5.3", "recharts": "^2.15.3", "styled-components": "^6.1.17"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/papaparse": "^5.3.15", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}