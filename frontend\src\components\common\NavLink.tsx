import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ListItem, ListItemButton, ListItemIcon, ListItemText } from '@mui/material';
import { styled } from '@mui/material/styles';

// Define interface for NavLink props
interface NavLinkProps {
  to: string;
  icon: React.ReactNode;
  text: string;
  onClick?: () => void;
}

// Create styled ListItemButton for active state
const StyledListItemButton = styled(ListItemButton)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(0.5),
  transition: 'all 0.2s ease-in-out',
  color: 'inherit',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    transform: 'translateX(2px)',
  },
  '&.active': {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    color: '#ffffff',
    borderLeft: `3px solid ${theme.palette.primary.main}`,
    paddingLeft: theme.spacing(2) - 3, // Adjust padding to account for border
    '& .MuiListItemIcon-root': {
      color: theme.palette.primary.main,
    },
  },
}));

/**
 * NavLink Component
 *
 * A custom navigation link component that highlights the active route
 */
const NavLink: React.FC<NavLinkProps> = ({ to, icon, text, onClick }) => {
  const location = useLocation();
  const isActive = location.pathname === to ||
                  (to !== '/dashboard' && location.pathname.startsWith(to));

  return (
    <ListItem disablePadding>
      <StyledListItemButton
        component={Link}
        to={to}
        className={isActive ? 'active' : ''}
        onClick={onClick}
      >
        <ListItemIcon sx={{ minWidth: 40, color: 'rgba(255, 255, 255, 0.7)' }}>
          {icon}
        </ListItemIcon>
        <ListItemText primary={text} />
      </StyledListItemButton>
    </ListItem>
  );
};

export default NavLink;
