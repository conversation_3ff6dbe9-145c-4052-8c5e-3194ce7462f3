"""
Match Infer Graph Module

This module provides a LangGraph-based agent for matching and inferring responses
from various communication channels.
"""
from typing import Dict, Any, List, Optional, Union, Callable, TypedDict
import json
import re
import os
from datetime import datetime

class ResponseState(TypedDict, total=False):
    message: Dict[str, Any]
    classification: Dict[str, Any]
    entities: Dict[str, Any]
    response: str
    response_explanation: str
    processing_start: str
    processing_completed: str
    error: str
    status: str

try:
    from langchain_core.prompts import ChatPromptTemplate
    from langgraph.graph import StateGraph, END
    from langchain_openai import ChatOpenAI
    from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
except ImportError:
    print("Required packages not found. Install with: pip install langchain langchain-openai langgraph")

class MatchInferGraph:
    """
    LangGraph-based agent to match and infer responses from different channels.
    """
    
    def __init__(self, openai_api_key: Optional[str] = None, model_name: str = "gpt-4"):
        """
        Initialize the match and infer graph agent.
        
        Args:
            openai_api_key: OpenAI API key
            model_name: Model name to use for inference
        """
        self.openai_api_key = openai_api_key or os.environ.get("OPENAI_API_KEY")
        self.model_name = model_name
        self.llm = None
        self.graph = None
        
        # Print the API key status for debugging (redact for safety)
        if not self.openai_api_key:
            print("[ERROR] OpenAI API key not provided. Please set OPENAI_API_KEY in your environment or .env file.")
            raise ValueError("OpenAI API key not provided. Please set OPENAI_API_KEY in your environment or .env file.")
        else:
            print(f"[INFO] OpenAI API key loaded: sk-{'*' * (len(self.openai_api_key)-8)}{self.openai_api_key[-8:]}")
        print("[INFO] Attempting to initialize LLM...")
        self._initialize_llm()
        if not self.llm:
            print("[ERROR] Failed to initialize LLM. Check your OpenAI API key and network connectivity.")
            raise RuntimeError("Failed to initialize LLM. Check your OpenAI API key and network connectivity.")
        print("[INFO] LLM initialized successfully.")
        self._build_graph()
    
    def _initialize_llm(self):
        """Initialize the LLM."""
        try:
            self.llm = ChatOpenAI(
                api_key=self.openai_api_key,
                model=self.model_name,
                temperature=0.1
            )
        except Exception as e:
            print(f"[ERROR] Exception during LLM initialization: {e}")
            self.llm = None
    
    def _build_graph(self):
        """Build the LangGraph workflow for matching and inferring responses."""
        if not self.llm:
            print("LLM not initialized. Cannot build graph.")
            return
        
        # Define the nodes for our graph
        builder = StateGraph(state_schema=ResponseState)
        builder.add_node("classify_message", self.classify_message)
        builder.add_node("extract_entities", self.extract_entities)
        builder.add_node("match_lead", self.match_lead)
        builder.add_node("generate_response", self.generate_response)
        builder.add_node("route_to_human", self.route_to_human)
        builder.add_node("send_response", self.send_response)
        # Edges
        builder.add_edge("classify_message", "extract_entities")
        builder.add_edge("extract_entities", "match_lead")
        builder.add_edge("match_lead", "generate_response")
        builder.add_conditional_edges(
            "generate_response",
            self.decide_next_steps,
            {
                "route_to_human": "route_to_human",
                "send_response": "send_response",
                "end": END
            }
        )
        builder.add_edge("route_to_human", END)
        builder.add_edge("send_response", END)
        builder.set_entry_point("classify_message")
        self.graph = builder.compile()
    
    def classify_message(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Classify the type of message and determine next steps."""
        message = state.get("message", {})
        channel = message.get("channel", "unknown")
        from_email = message.get("from_email", "")
        from_phone = message.get("from_phone", "")
        received_at = message.get("received_at", "")
        body = message.get("message", "")
        subject = message.get("subject", "")

        # System prompt for classification
        system_prompt = """
        You are an AI assistant that classifies incoming messages from different communication channels.
        Analyze the message content and classify it according to its intent and urgency.
        Respond ONLY with a valid JSON object containing the classification fields. Do NOT include any explanation or extra text.
        """

        # Prepare context from state
        if channel == "email":
            content = f"Email from: {from_email}\nReceived at: {received_at}\nBody: {body}"
        elif channel == "sms":
            content = f"SMS from: {from_phone}\nReceived at: {received_at}\nContent: {body}"
        elif channel == "voice":
            content = f"Voice call from: {from_phone}\nReceived at: {received_at}\nTranscript: {body}"
        else:
            content = f"Message from unknown channel: {json.dumps(message)}"

        context = f"""
        {content}
        """

        # Call LLM for classification
        classification_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("human", f"Please classify the following message:\n\n{context}")
        ])
        classification_messages = classification_prompt.format()
        classification_result = self.llm.invoke(classification_messages)
        classification_text = classification_result.content
        
        # Robust JSON parsing with error handling
        try:
            classification_json = json.loads(classification_text)
            return {
                **state,
                "classification": classification_json
            }
        except Exception as e:
            print(f"[ERROR] Failed to parse classification JSON: {e}\nRaw LLM output: {classification_text}")
            return {
                **state,
                "classification": {},
                "error": f"Failed to parse classification JSON: {e}. Raw: {classification_text}"
            }

    def extract_entities(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Extract entities from the message using OpenAI function calling."""
        from langchain_core.messages import HumanMessage, SystemMessage
        from langchain_openai import ChatOpenAI
        import inspect

        # Define the function schema for entity extraction
        entity_function = {
            "name": "extract_entities",
            "description": "Extracts contact entities from a message.",
            "parameters": {
                "type": "object",
                "properties": {
                    "email": {"type": "string", "description": "Email address if present"},
                    "name": {"type": "string", "description": "Full name if present"},
                    "phone": {"type": "string", "description": "Phone number if present"},
                    "domain": {"type": "string", "description": "Domain if present"},
                    "company": {"type": "string", "description": "Company name if present"}
                },
                "required": []
            }
        }

        message = state.get("message", {})
        channel = message.get("channel", "unknown")
        from_email = message.get("from_email", "")
        from_phone = message.get("from_phone", "")
        received_at = message.get("received_at", "")
        body = message.get("message", "")
        subject = message.get("subject", "")

        # Prepare context from state
        if channel == "email":
            content = f"Email from: {from_email}\nReceived at: {received_at}\nBody: {body}"
        elif channel == "sms":
            content = f"SMS from: {from_phone}\nReceived at: {received_at}\nContent: {body}"
        elif channel == "voice":
            content = f"Voice call from: {from_phone}\nReceived at: {received_at}\nTranscript: {body}"
        else:
            content = f"Message from unknown channel: {json.dumps(message)}"

        system_prompt = "You are an AI assistant that extracts contact entities from incoming messages. Extract the sender's email, name, phone, domain (parsed from email), and company if available. Use null if not found."
        user_prompt = f"Extract entities from this message:\n{content}"

        # Call the LLM with function calling
        llm_with_func = ChatOpenAI(
            api_key=self.openai_api_key,
            model=self.model_name,
            temperature=0.1,
            model_kwargs={
                "functions": [entity_function],
                "function_call": {"name": "extract_entities"}
            }
        )
        response = llm_with_func.invoke([
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ])
        # Parse the function call result
        func_args = None
        if hasattr(response, "additional_kwargs") and "function_call" in response.additional_kwargs:
            try:
                func_args = json.loads(response.additional_kwargs["function_call"]["arguments"])
            except Exception as e:
                print(f"[ERROR] Failed to parse function_call arguments: {e}\nRaw: {response.additional_kwargs['function_call']['arguments']}")
                func_args = {}
        else:
            print(f"[ERROR] No function_call in LLM response. Raw: {response}")
            func_args = {}
        # print(f"[DEBUG] Function call entity extraction result: {func_args!r}")
        return {
            **state,
            "entities": func_args
        }

    def match_lead(self, state: Dict[str, Any]) -> Dict[str, Any]:
        from lead_context_store import LeadContextStore
        lead_store = LeadContextStore()
        entities = state.get("entities", {})
        # print(f"[DEBUG] Entities for matching: {entities!r}")
        if not isinstance(entities, dict):
            print(f"[ERROR] Entities is not a dict: {entities!r}")
            state["matched_lead"] = None
            state["error"] = f"Entities is not a dict: {entities!r}"
            return state
        # Try flat keys first
        email = entities.get("email")
        name = entities.get("name")
        phone = entities.get("phone")
        company = entities.get("company")
        domain = entities.get("domain")
        # Fallback to nested keys if needed
        if not email and "Email_From" in entities:
            domain = entities.get("domain")
        # Fallback to nested keys if needed
        if not email and "Email_From" in entities:
            email = entities["Email_From"].get("Email")
        if not phone and "Phone" in entities:
            phone = entities["Phone"]
        # Always extract domain from sender's email if possible, even if it's not in entities
        if email and "@" in email:
            domain_from_email = email.split("@")[-1].strip().lower()
            # Use domain from entities if present, otherwise use extracted
            if not domain:
                domain = domain_from_email
            else:
                # If both present and different, try both
                domains_to_try = [domain.strip().lower()]
                if domain_from_email != domain:
                    domains_to_try.append(domain_from_email)
        else:
            domains_to_try = [domain.strip().lower()] if domain else []
        # Remove duplicates
        domains_to_try = list(dict.fromkeys(domains_to_try))

        matched = None
        # Try matching in order: email, phone, domain(s), name, company
        if email:
            matched = lead_store.find_by_email(email)
        if not matched and phone:
            matched = lead_store.find_by_phone(phone)
        if not matched and 'domains_to_try' in locals():
            for d in domains_to_try:
                matched = lead_store.find_by_domain(d)
                if matched:
                    break
        if not matched and name:
            matched = lead_store.find_by_name(name)
        if not matched and company:
            # Try exact match first
            matched = getattr(lead_store, "find_by_company", lambda c: None)(company)
        if not matched and company:
            # Fallback to fuzzy matching for company name only
            if hasattr(lead_store, "find_fuzzy_lead"):
                matched = lead_store.find_fuzzy_lead(company=company)
        state["matched_lead"] = matched
        if "message" in state and isinstance(state["message"], dict):
            state["message"]["matched_lead"] = matched
        return state

    def generate_response(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate an appropriate response based on message and classification."""
        message = state.get("message", {})
        channel = message.get("channel", "unknown")
        from_email = message.get("from_email", "")
        from_phone = message.get("from_phone", "")
        received_at = message.get("received_at", "")
        body = message.get("message", "")
        subject = message.get("subject", "")
        classification = state.get("classification", {})
        entities = state.get("entities", {})

        # Only generate responses for messages that don't require human attention
        if classification.get("requires_human", True):
            return {
                **state,
                "response": None,
                "response_explanation": "This message requires human attention."
            }

        # System prompt for response generation
        system_prompt = """
        You are an AI assistant that generates appropriate responses to incoming messages.
        Your responses should be professional, helpful, and appropriate to the context.
        
        For inquiries, provide clear and concise information.
        For responses to previous outreach, acknowledge the response and suggest next steps.
        For urgent matters, acknowledge the urgency and explain that a human will follow up soon.
        
        Do not generate responses for messages classified as spam.
        """

        # Prepare context from state
        if channel == "email":
            content = f"Email from: {from_email}\nReceived at: {received_at}\nBody: {body}"
        elif channel == "sms":
            content = f"SMS from: {from_phone}\nReceived at: {received_at}\nContent: {body}"
        elif channel == "voice":
            content = f"Voice call from: {from_phone}\nReceived at: {received_at}\nTranscript: {body}"
        else:
            content = f"Message from unknown channel: {json.dumps(message)}"

        context = f"""
        {content}
        Entities: {json.dumps(entities)}
        Classification: {json.dumps(classification)}
        """

        # Call LLM for response generation
        response_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("human", f"Please generate an appropriate response to the following message:\n\n{context}")
        ])
        response_messages = response_prompt.format()
        response_result = self.llm.invoke(response_messages)
        response_text = response_result.content
        
        # Update state
        return {
            **state,
            "response": response_text,
            "response_explanation": "Generated automated response based on message classification and content."
        }

    def decide_next_steps(self, state: Dict[str, Any]) -> str:
        """Decide what to do next based on the current state."""
        classification = state.get("classification", {})
        
        # If it's spam, we're done
        if classification.get("category") == "SPAM":
            return "end"
        
        # If it requires human attention, route to human
        if classification.get("requires_human", False):
            return "route_to_human"
        
        # If we have a response, send it
        if state.get("response"):
            return "send_response"
        
        # Otherwise, we're done
        return "end"

    def route_to_human(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Route the message to human attention."""
        # In a real implementation, this would add the message to a queue
        # or notify a human operator
        return state

    def send_response(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Send the response."""
        # In a real implementation, this would send the response
        # via the original channel (e.g. email, SMS, voice)
        return state

    def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        if not self.graph:
            if not self.openai_api_key:
                return {
                    "error": "OpenAI API key not provided",
                    "status": "failed"
                }
            self._initialize_llm()
            self._build_graph()

        # Initialize the state with the message
        initial_state = {
            "message": message,
            "processing_start": datetime.now().isoformat()
        }

        try:
            # Run the graph
            result = self.graph.invoke(initial_state)
            
            # Add processing completed timestamp
            result["processing_completed"] = datetime.now().isoformat()
            
            return result
        except Exception as e:
            print(f"Error processing message: {e}")
            return {
                "error": str(e),
                "status": "failed",
                "message": message
            }
    
    def process_email(self, email_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an email message.
        
        Args:
            email_data: Email data
            
        Returns:
            Processing results
        """
        # Format email data for processing
        message = {
            "source": "email",
            "id": email_data.get("id"),
            "from": email_data.get("from"),
            "to": email_data.get("to", ""),
            "subject": email_data.get("subject", ""),
            "body": email_data.get("body", ""),
            "date": email_data.get("date", ""),
            "raw_data": email_data
        }
        
        return self.process_message(message)
    
    def process_sms(self, sms_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an SMS message.
        
        Args:
            sms_data: SMS data
            
        Returns:
            Processing results
        """
        # Format SMS data for processing
        message = {
            "source": "sms",
            "id": sms_data.get("id"),
            "from": sms_data.get("from"),
            "to": sms_data.get("to", ""),
            "text": sms_data.get("text", ""),
            "timestamp": sms_data.get("timestamp", ""),
            "raw_data": sms_data
        }
        
        return self.process_message(message)
    
    def process_voice(self, voice_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process voice call data.
        
        Args:
            voice_data: Voice call data
            
        Returns:
            Processing results
        """
        # Format voice data for processing
        transcript = " ".join(voice_data.get("transcripts", []))
        
        message = {
            "source": "voice",
            "id": voice_data.get("call_leg_id"),
            "from": voice_data.get("from", {}).get("phone_number", ""),
            "to": voice_data.get("to", {}).get("phone_number", ""),
            "transcript": transcript,
            "summary": voice_data.get("summary", ""),
            "start_time": voice_data.get("start_time", ""),
            "end_time": voice_data.get("end_time", ""),
            "raw_data": voice_data
        }
        
        return self.process_message(message)


# Example usage
if __name__ == "__main__":
    # Replace with your OpenAI API key
    OPENAI_API_KEY = "YOUR_OPENAI_API_KEY"
    
    agent = MatchInferGraph(openai_api_key=OPENAI_API_KEY)
    
    # Example SMS message
    example_sms = {
        "id": "msg_123456",
        "from": "+12025550123",
        "to": "+12025550789",
        "text": "Yes, I'm interested in the product demo you mentioned. Can we schedule it for tomorrow?",
        "timestamp": "2023-08-15T14:23:45Z"
    }
    
    # Process the message
    result = agent.process_sms(example_sms)
    
    # Print the result
    print(json.dumps(result, indent=2))
