FROM python:3.9-slim

WORKDIR /app

# Copy requirements files
COPY services/requirements.txt /app/requirements.txt
COPY services/form-analysis/requirements.txt /app/service-requirements.txt

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r service-requirements.txt

# Copy shared libraries
COPY libs /app/libs

# Copy service code
COPY services/form-analysis /app/services/form-analysis

# Set environment variables
ENV PYTHONPATH=/app
ENV SERVICE_NAME=form-analysis

# Expose port
EXPOSE 8002

# Run the service
CMD ["python", "services/form-analysis/main.py"]
