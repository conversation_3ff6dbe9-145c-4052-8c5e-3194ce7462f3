apiVersion: apps/v1
kind: Deployment
metadata:
  name: form-discovery
  labels:
    app: form-discovery
spec:
  replicas: 2
  selector:
    matchLabels:
      app: form-discovery
  template:
    metadata:
      labels:
        app: form-discovery
    spec:
      containers:
      - name: form-discovery
        image: ${DOCKER_REGISTRY}/leadgen-form-discovery:latest
        ports:
        - containerPort: 8001
        env:
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka:9092"
        - name: FORM_DISCOVERED_TOPIC
          value: "form.discovered"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: mongodb-secrets
              key: uri
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: service-secrets
              key: secret-key
        resources:
          limits:
            cpu: "1000m"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: screenshots
          mountPath: /app/screenshots
      volumes:
      - name: screenshots
        persistentVolumeClaim:
          claimName: screenshots-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: form-discovery
spec:
  selector:
    app: form-discovery
  ports:
  - port: 8001
    targetPort: 8001
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: screenshots-pvc
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
