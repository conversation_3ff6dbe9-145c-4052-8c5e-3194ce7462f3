FROM python:3.10-slim

WORKDIR /app

# Copy requirements first for better caching
COPY form-submission/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy shared libraries
COPY libs /app/libs

# Copy service code
COPY form-submission /app/form-submission

# Set Python path
ENV PYTHONPATH=/app

# Expose port
EXPOSE 8002

# Run the service
CMD ["python", "form-submission/main.py"]
