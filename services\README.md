# Form Discovery & Submission Services

This directory contains the Form Discovery and Form Submission services for the Unified AI-Powered Lead Generation System.

## Services

### Form Discovery Service

The Form Discovery Service is responsible for discovering lead forms on websites. It uses various strategies to find and analyze forms, including:

- Direct Form Strategy: Looks for forms directly on the page
- Contact Page Strategy: Navigates to contact pages to find forms
- Navigation Menu Strategy: Explores navigation menus to find contact/quote pages
- Popup Detection Strategy: Detects and interacts with popups that might contain forms
- Sitemap Exploration Strategy: Explores the sitemap to find contact/quote pages

### Form Submission Service

The Form Submission Service is responsible for submitting lead forms. It uses AI reasoning to determine how to fill out forms and handles various form types, including:

- Single-step forms
- Multi-step forms
- Forms with CAPTCHA
- Forms with validation

## Architecture

The services follow a microservices architecture and communicate with each other through events using Kafka. They also use:

- Redis for caching and temporary data storage
- MongoDB for persistent data storage
- FastAPI for the REST API
- LangGraph for AI-powered form filling workflows

## API Endpoints

### Form Discovery Service

- `POST /api/v1/discover`: Discover forms on a website
- `GET /api/v1/discover/{job_id}`: Get the status of a form discovery job
- `GET /health`: Health check endpoint

### Form Submission Service

- `POST /api/v1/submit`: Submit a form
- `GET /api/v1/submit/{job_id}`: Get the result of a form submission job
- `GET /health`: Health check endpoint

## Running the Services

You can run the services using Docker Compose:

```bash
cd services
docker-compose up
```

This will start the Form Discovery Service, Form Submission Service, and all required dependencies (Kafka, Redis, MongoDB).

## Development

To run the services locally for development:

### Form Discovery Service

```bash
cd services/form-discovery
pip install -r requirements.txt
python main.py
```

### Form Submission Service

```bash
cd services/form-submission
pip install -r requirements.txt
python main.py
```

## Testing

To run the tests:

```bash
cd services
pytest
```

## Configuration

The services can be configured using environment variables:

### Form Discovery Service

- `KAFKA_BOOTSTRAP_SERVERS`: Kafka bootstrap servers (default: `kafka:9092`)
- `FORM_DISCOVERED_TOPIC`: Kafka topic for form discovered events (default: `form.discovered`)
- `REDIS_URL`: Redis URL (default: `redis://redis:6379/0`)
- `MONGODB_URI`: MongoDB URI (default: `mongodb://mongodb:27017/form-discovery`)

### Form Submission Service

- `KAFKA_BOOTSTRAP_SERVERS`: Kafka bootstrap servers (default: `kafka:9092`)
- `FORM_SUBMITTED_TOPIC`: Kafka topic for form submitted events (default: `form.submitted`)
- `REDIS_URL`: Redis URL (default: `redis://redis:6379/0`)
- `MONGODB_URI`: MongoDB URI (default: `mongodb://mongodb:27017/form-submission`)
