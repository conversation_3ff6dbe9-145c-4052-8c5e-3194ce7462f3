import React from 'react';
import { Box, Paper } from '@mui/material';
import { AutoMode as AutoModeIcon } from '@mui/icons-material';
import PageHeader from '../components/common/PageHeader';
import LeadAuditOrchestratorPanel from '../components/LeadAuditOrchestrator/LeadAuditOrchestratorPanel';

/**
 * Lead Audit Orchestrator Page
 *
 * This page provides the main interface for the automated lead audit system.
 * It allows users to:
 * - Configure and start automated lead processing
 * - Monitor the progress of audit batches
 * - View scheduler status and queue management
 * - Track 48-hour monitoring periods
 * - Manage lead qualification and disqualification
 */
const LeadAuditOrchestratorPage = () => {
  return (
    <Box>
      <PageHeader
        title="Lead Audit Orchestrator"
        description="Automated lead response audit system with 48-hour monitoring and intelligent qualification"
        breadcrumbs={[
          { label: 'Automation', icon: <AutoModeIcon fontSize="inherit" /> }
        ]}
      />

      <Paper sx={{ p: 3 }}>
        <LeadAuditOrchestratorPanel />
      </Paper>
    </Box>
  );
};

export default LeadAuditOrchestratorPage;
